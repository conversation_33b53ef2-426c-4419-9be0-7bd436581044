const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

async function testPromotionResponse() {
  console.log('🧪 Testing Promotion Response Format');
  console.log('=' .repeat(50));

  try {
    const response = await axios.get(`${API_BASE_URL}/api/promotions/user`);
    
    console.log('✅ Success! Total promotions:', response.data.promotions?.length || 0);
    console.log('\n📋 Promotion Details:');
    
    response.data.promotions?.forEach((promo, index) => {
      console.log(`\n${index + 1}. ${promo.code} (${promo.name})`);
      console.log(`   📊 Type: ${promo.assignmentType || 'UNKNOWN'}`);
      console.log(`   🔓 isPublic: ${promo.isPublic}`);
      console.log(`   👤 userId: ${promo.userId || 'null'}`);
      console.log(`   💰 Discount: ${promo.discountType === 'PERCENTAGE' ? promo.discountValue + '%' : '$' + promo.discountValue}`);
      console.log(`   📏 Min Order: $${promo.minOrderAmount || 0}`);
      console.log(`   🎯 Available: ${promo.isAvailable}`);
      console.log(`   🔢 Remaining Uses: ${promo.remainingUses}`);
    });

    console.log('\n📊 Summary by Type:');
    const byType = response.data.promotions?.reduce((acc, promo) => {
      const type = promo.assignmentType || 'UNKNOWN';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(byType || {}).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} promotions`);
    });

  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }
}

testPromotionResponse();
