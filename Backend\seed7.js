const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion"); // đường dẫn model
require('dotenv').config();

// <PERSON><PERSON>m tra ENVIRONMENT và chọn MongoDB URI phù hợp
const getMongoURI = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  console.log(`🌍 Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log(`📡 Using Production MongoDB: ${process.env.MONGODB_URI_PRODUCTION}`);
    return process.env.MONGODB_URI_PRODUCTION;
  } else {
    console.log(`💻 Using Development MongoDB: ${process.env.MONGODB_URI_DEVELOPMENT}`);
    return process.env.MONGODB_URI_DEVELOPMENT;
  }
};

const mongoURI = getMongoURI();
mongoose.connect(mongoURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log("✅ MongoDB connected successfully");
    console.log(`📍 Connected to: ${mongoURI.includes('mongodb+srv') ? 'MongoDB Atlas (Production)' : 'Local MongoDB (Development)'}`);
})
.catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
});

const promotions = [
    // ========== PUBLIC PROMOTIONS (Hiển thị cho tất cả user) ==========
    {
        code: "SUMMER15",
        name: "Summer Sale 15%",
        description: "Enjoy 15% off on all hotel bookings this summer!",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 100000, // 100k VND
        minOrderAmount: 200000, // 200k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 500,
        usedCount: 125, // 25% used
        userUsageLimit: 2,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "WEEKEND20",
        name: "Weekend Special",
        description: "20% off for weekend getaways (Fri-Sun bookings)",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 150000, // 150k VND
        minOrderAmount: 300000, // 300k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        usedCount: 140, // 70% used
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "STUDENT10",
        name: "Student Discount",
        description: "10% off for students with valid student ID",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 50000, // 50k VND
        minOrderAmount: 100000, // 100k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 900, // 90% used - almost full
        userUsageLimit: 3,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "EARLYBIRD25",
        name: "Early Bird Special",
        description: "25% off for bookings made 30 days in advance",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 200000, // 200k VND
        minOrderAmount: 400000, // 400k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 5, // 5% used - fresh promotion
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },

    // ========== PRIVATE PROMOTIONS (Chỉ user cụ thể mới có) ==========
    {
        code: "VIP_USER2",
        name: "VIP Gold Member",
        description: "Exclusive 50% off for our VIP Gold member",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 500000, // 500k VND
        minOrderAmount: 1000000, // 1M VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 3, // User này có thể dùng 3 lần
        usedCount: 1, // Đã dùng 1 lần
        userUsageLimit: 3,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 2 mới có
        createdBy: 1
    },
    {
        code: "WELCOME_USER3",
        name: "Welcome New Customer",
        description: "Welcome gift: 100,000 VND off your first booking!",
        discountType: "FIXED_AMOUNT",
        discountValue: 100000, // 100k VND
        minOrderAmount: 250000, // 250k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 3 mới có
        createdBy: 1
    },
    {
        code: "BIRTHDAY_USER4",
        name: "Birthday Special Gift",
        description: "Happy Birthday! 75% off your special day booking",
        discountType: "PERCENTAGE",
        discountValue: 75,
        maxDiscountAmount: 300000, // 300k VND
        minOrderAmount: 200000, // 200k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 4, // Chỉ User 4 mới có
        createdBy: 1
    },
    {
        code: "LOYALTY_USER5",
        name: "Loyalty Reward",
        description: "Thank you for your loyalty - 200,000 VND off!",
        discountType: "FIXED_AMOUNT",
        discountValue: 200000, // 200k VND
        minOrderAmount: 600000, // 600k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 2, // User này có thể dùng 2 lần
        usedCount: 0,
        userUsageLimit: 2,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 5 mới có
        createdBy: 1
    },

    // ========== ADDITIONAL PUBLIC PROMOTIONS ==========
    {
        code: "FLASH30",
        name: "Flash Sale",
        description: "Limited time 30% off - Only 50 uses available!",
        discountType: "PERCENTAGE",
        discountValue: 30,
        maxDiscountAmount: 300000, // 300k VND
        minOrderAmount: 500000, // 500k VND
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 50, // Very limited
        usedCount: 35, // 70% used - urgency!
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null,
        createdBy: 1
    },
    {
        code: "FAMILY40",
        name: "Family Package",
        description: "40% off for family bookings (3+ rooms)",
        discountType: "PERCENTAGE",
        discountValue: 40,
        maxDiscountAmount: 800000, // 800k VND
        minOrderAmount: 2000000, // 2M VND (family booking)
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 25, // 25% used
        userUsageLimit: 2,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null,
        createdBy: 1
    }
];

// Hàm seed dữ liệu
const seedPromotions = async () => {
    try {
        // Xóa tất cả dữ liệu promotion cũ
        console.log("🗑️  Đang xóa tất cả dữ liệu promotion cũ...");
        const deleteResult = await Promotion.deleteMany({});
        console.log(`✅ Đã xóa ${deleteResult.deletedCount} promotion cũ`);

        // Thêm dữ liệu mới
        console.log("📝 Đang thêm dữ liệu promotion mới...");
        const insertResult = await Promotion.insertMany(promotions);
        console.log(`✅ Đã thêm ${insertResult.length} promotion mới thành công`);

        // Hiển thị danh sách promotion đã thêm
        console.log("\n📋 Danh sách promotion đã được thêm:");
        insertResult.forEach((promo, index) => {
            const type = promo.isPublic ? '� PUBLIC' : `� PRIVATE (User ${promo.userId})`;
            const usage = promo.usageLimit ? `${promo.usedCount}/${promo.usageLimit}` : 'Unlimited';
            const discount = promo.discountType === 'PERCENTAGE' ? `${promo.discountValue}%` : `${promo.discountValue.toLocaleString()} VND`;
            console.log(`${index + 1}. ${promo.code} - ${promo.name} (${type}) - ${discount} off - Usage: ${usage}`);
        });

        console.log("\n🎉 Seed dữ liệu promotion hoàn tất!");
        console.log("\n📊 Summary:");
        const publicCount = insertResult.filter(p => p.isPublic).length;
        const privateCount = insertResult.filter(p => !p.isPublic).length;
        console.log(`   📢 Public promotions: ${publicCount} (visible to all users)`);
        console.log(`   🔒 Private promotions: ${privateCount} (specific users only)`);

        console.log("\n🧪 Test Scenarios:");
        console.log("   - User 1: Can see all public promotions");
        console.log("   - User 2: Can see public + VIP_USER2 (50% off, 2 uses left)");
        console.log("   - User 3: Can see public + WELCOME_USER3 (100k VND off)");
        console.log("   - User 4: Can see public + BIRTHDAY_USER4 (75% off)");
        console.log("   - User 5: Can see public + LOYALTY_USER5 (200k VND off, 2 uses)");
        console.log("   - FLASH30: Limited quantity (15 uses left!)");
        console.log("   - FAMILY40: High minimum order (2M VND)");

        mongoose.disconnect();
    } catch (error) {
        console.error("❌ Lỗi khi seed dữ liệu:", error);
        mongoose.disconnect();
        process.exit(1);
    }
};

// Chạy seed function
seedPromotions();
