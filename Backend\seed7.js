const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion"); // đường dẫn model
require('dotenv').config();

// <PERSON><PERSON>m tra ENVIRONMENT và chọn MongoDB URI phù hợp
const getMongoURI = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  console.log(`🌍 Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log(`📡 Using Production MongoDB: ${process.env.MONGODB_URI_PRODUCTION}`);
    return process.env.MONGODB_URI_PRODUCTION;
  } else {
    console.log(`💻 Using Development MongoDB: ${process.env.MONGODB_URI_DEVELOPMENT}`);
    return process.env.MONGODB_URI_DEVELOPMENT;
  }
};

const mongoURI = getMongoURI();
mongoose.connect(mongoURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => {
    console.log("✅ MongoDB connected successfully");
    console.log(`📍 Connected to: ${mongoURI.includes('mongodb+srv') ? 'MongoDB Atlas (Production)' : 'Local MongoDB (Development)'}`);
})
.catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
});

const promotions = [
    // ========== PUBLIC PROMOTIONS (Hiển thị cho tất cả user) ==========
    {
        code: "SUMMER15",
        name: "Summer Sale 15%",
        description: "Enjoy 15% off on all hotel bookings this summer!",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 100,
        minOrderAmount: 200,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 500,
        usedCount: 125, // 25% used
        userUsageLimit: 2,
        isActive: true,
        isPersonalized: false, // PUBLIC
        targetUserTypes: ['ALL'],
        createdBy: 1
    },
    {
        code: "WEEKEND20",
        name: "Weekend Special",
        description: "20% off for weekend getaways (Fri-Sun bookings)",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 150,
        minOrderAmount: 300,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        usedCount: 140, // 70% used
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false, // PUBLIC
        targetUserTypes: ['ALL'],
        createdBy: 1
    },
    {
        code: "STUDENT10",
        name: "Student Discount",
        description: "10% off for students with valid student ID",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 50,
        minOrderAmount: 100,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 900, // 90% used - almost full
        userUsageLimit: 3,
        isActive: true,
        isPersonalized: false, // PUBLIC
        targetUserTypes: ['ALL'],
        createdBy: 1
    },
    {
        code: "EARLYBIRD25",
        name: "Early Bird Special",
        description: "25% off for bookings made 30 days in advance",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 200,
        minOrderAmount: 400,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 5, // 5% used - fresh promotion
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false, // PUBLIC
        targetUserTypes: ['ALL'],
        createdBy: 1
    },

    // ========== PERSONALIZED PROMOTIONS (Chỉ người có code mới biết) ==========
    {
        code: "VIP50GOLD",
        name: "VIP Gold Member",
        description: "Exclusive 50% off for our VIP Gold members",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 500,
        minOrderAmount: 500,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng total
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // PERSONALIZED
        targetUserTypes: ['VIP_USER'],
        createdBy: 1
    },
    {
        code: "WELCOME100",
        name: "Welcome New Customer",
        description: "Welcome gift: $100 off your first booking!",
        discountType: "FIXED_AMOUNT",
        discountValue: 100,
        minOrderAmount: 250,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng total
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // PERSONALIZED
        targetUserTypes: ['NEW_USER'],
        createdBy: 1
    },
    {
        code: "BIRTHDAY75",
        name: "Birthday Special Gift",
        description: "Happy Birthday! 75% off your special day booking",
        discountType: "PERCENTAGE",
        discountValue: 75,
        maxDiscountAmount: 300,
        minOrderAmount: 200,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng total
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // PERSONALIZED
        targetUserTypes: ['RETURNING_USER'],
        createdBy: 1
    },
    {
        code: "LOYALTY200",
        name: "Loyalty Reward",
        description: "Thank you for your loyalty - $200 off!",
        discountType: "FIXED_AMOUNT",
        discountValue: 200,
        minOrderAmount: 600,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng total
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // PERSONALIZED
        targetUserTypes: ['RETURNING_USER'],
        createdBy: 1
    }
];

// Hàm seed dữ liệu
const seedPromotions = async () => {
    try {
        // Xóa tất cả dữ liệu promotion cũ
        console.log("🗑️  Đang xóa tất cả dữ liệu promotion cũ...");
        const deleteResult = await Promotion.deleteMany({});
        console.log(`✅ Đã xóa ${deleteResult.deletedCount} promotion cũ`);

        // Thêm dữ liệu mới
        console.log("📝 Đang thêm dữ liệu promotion mới...");
        const insertResult = await Promotion.insertMany(promotions);
        console.log(`✅ Đã thêm ${insertResult.length} promotion mới thành công`);

        // Hiển thị danh sách promotion đã thêm
        console.log("\n📋 Danh sách promotion đã được thêm:");
        insertResult.forEach((promo, index) => {
            const type = promo.isPersonalized ? '🔒 PERSONALIZED' : '📢 PUBLIC';
            const usage = promo.usageLimit ? `${promo.usedCount}/${promo.usageLimit}` : 'Unlimited';
            console.log(`${index + 1}. ${promo.code} - ${promo.name} (${type}) - Usage: ${usage}`);
        });

        console.log("\n🎉 Seed dữ liệu promotion hoàn tất!");
        console.log("\n📊 Summary:");
        const publicCount = insertResult.filter(p => !p.isPersonalized).length;
        const personalizedCount = insertResult.filter(p => p.isPersonalized).length;
        console.log(`   📢 Public promotions: ${publicCount} (visible in modal)`);
        console.log(`   🔒 Personalized promotions: ${personalizedCount} (manual input only)`);

        mongoose.disconnect();
    } catch (error) {
        console.error("❌ Lỗi khi seed dữ liệu:", error);
        mongoose.disconnect();
        process.exit(1);
    }
};

// Chạy seed function
seedPromotions();
