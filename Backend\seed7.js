const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion"); // đường dẫn model
require('dotenv').config();

// <PERSON><PERSON>m tra ENVIRONMENT và chọn MongoDB URI phù hợp
const getMongoURI = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  console.log(`🌍 Environment: ${environment}`);
  
  if (environment === 'production') {
    console.log(`📡 Using Production MongoDB: ${process.env.MONGODB_URI_PRODUCTION}`);
    return process.env.MONGODB_URI_PRODUCTION;
  } else {
    console.log(`💻 Using Development MongoDB: ${process.env.MONGODB_URI_DEVELOPMENT}`);
    return process.env.MONGODB_URI_DEVELOPMENT;
  }
};

const mongoURI = getMongoURI() || 'mongodb://localhost:27017/My_Uroom';
console.log(`🔗 Attempting to connect to: ${mongoURI}`);

mongoose.connect(mongoURI)
.then(() => {
    console.log("✅ MongoDB connected successfully");
    console.log(`📍 Connected to: ${mongoURI.includes('mongodb+srv') ? 'MongoDB Atlas (Production)' : 'Local MongoDB (Development)'}`);
})
.catch((error) => {
    console.error("❌ MongoDB connection error:", error);
    console.error("💡 Make sure MongoDB is running locally or check your connection string");
    process.exit(1);
});

const promotions = [
    // ========== PUBLIC PROMOTIONS (Hiển thị cho tất cả user) ==========
    {
        code: "SUMMER15",
        name: "Summer Sale 15%",
        description: "Enjoy 15% off on all hotel bookings this summer!",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 100, // $100
        minOrderAmount: 200, // $200
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 500,
        usedCount: 125, // 25% used
        userUsageLimit: 2,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "WEEKEND20",
        name: "Weekend Special",
        description: "20% off for weekend getaways (Fri-Sun bookings)",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 150, // $150
        minOrderAmount: 300, // $300
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        usedCount: 140, // 70% used
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "STUDENT10",
        name: "Student Discount",
        description: "10% off for students with valid student ID",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 50, // $50
        minOrderAmount: 100, // $100
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 900, // 90% used - almost full
        userUsageLimit: 3,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },
    {
        code: "EARLYBIRD25",
        name: "Early Bird Special",
        description: "25% off for bookings made 30 days in advance",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 200, // $200
        minOrderAmount: 400, // $400
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 5, // 5% used - fresh promotion
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null, // No specific user
        createdBy: 1
    },

    // ========== PRIVATE PROMOTIONS (Chỉ user cụ thể mới có) ==========
    {
        code: "VIP_USER2",
        name: "VIP Gold Member",
        description: "Exclusive 50% off for our VIP Gold member",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 500, // $500
        minOrderAmount: 1000, // $1000
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 3, // User này có thể dùng 3 lần
        usedCount: 1, // Đã dùng 1 lần
        userUsageLimit: 3,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 2 mới có
        createdBy: 1
    },
    {
        code: "WELCOME_USER3",
        name: "Welcome New Customer",
        description: "Welcome gift: $100 off your first booking!",
        discountType: "FIXED_AMOUNT",
        discountValue: 100, // $100
        minOrderAmount: 250, // $250
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 3, // Chỉ User 3 mới có
        createdBy: 1
    },
    {
        code: "BIRTHDAY_USER4",
        name: "Birthday Special Gift",
        description: "Happy Birthday! 75% off your special day booking",
        discountType: "PERCENTAGE",
        discountValue: 75,
        maxDiscountAmount: 300, // $300
        minOrderAmount: 200, // $200
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // Chỉ 1 lần sử dụng
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 4 mới có
        createdBy: 1
    },
    {
        code: "LOYALTY_USER5",
        name: "Loyalty Reward",
        description: "Thank you for your loyalty - $200 off!",
        discountType: "FIXED_AMOUNT",
        discountValue: 200, // $200
        minOrderAmount: 600, // $600
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 2, // User này có thể dùng 2 lần
        usedCount: 0,
        userUsageLimit: 2,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 11, // Chỉ User 5 mới có
        createdBy: 1
    },
    {
        code: "SPECIAL_USER1",
        name: "Special User 1 Deal",
        description: "Exclusive 60% off for User 1 only!",
        discountType: "PERCENTAGE",
        discountValue: 60,
        maxDiscountAmount: 300, // $300
        minOrderAmount: 200, // $200
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 2, // User này có thể dùng 2 lần
        usedCount: 0,
        userUsageLimit: 2,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: 1, // Chỉ User 1 mới có
        createdBy: 1
    },

    // ========== ADDITIONAL PUBLIC PROMOTIONS ==========
    {
        code: "FLASH30",
        name: "Flash Sale",
        description: "Limited time 30% off - Only 50 uses available!",
        discountType: "PERCENTAGE",
        discountValue: 30,
        maxDiscountAmount: 300, // $300
        minOrderAmount: 500, // $500
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 50, // Very limited
        usedCount: 35, // 70% used - urgency!
        userUsageLimit: 1,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null,
        createdBy: 1
    },
    {
        code: "FAMILY40",
        name: "Family Package",
        description: "40% off for family bookings (3+ rooms)",
        discountType: "PERCENTAGE",
        discountValue: 40,
        maxDiscountAmount: 800, // $800
        minOrderAmount: 2000, // $2000 (family booking)
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 25, // 25% used
        userUsageLimit: 2,
        isActive: true,
        isPublic: true, // PUBLIC
        userId: null,
        createdBy: 1
    },

    // ========== UNASSIGNED PRIVATE PROMOTIONS (userId = null) ==========
    {
        code: "NEWUSER50",
        name: "New User Special",
        description: "50% off for new users - One time use only!",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 250, // $250
        minOrderAmount: 200, // $200
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // One time use
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: null, // Unassigned - can be claimed
        createdBy: 1
    },
    {
        code: "EXCLUSIVE75",
        name: "Exclusive VIP Deal",
        description: "Exclusive 75% off - Limited to one lucky user!",
        discountType: "PERCENTAGE",
        discountValue: 75,
        maxDiscountAmount: 500, // $500
        minOrderAmount: 300, // $300
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // One time use
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: null, // Unassigned - can be claimed
        createdBy: 1
    },
    {
        code: "LUCKY100",
        name: "Lucky Winner",
        description: "Lucky winner gets $100 off - First come first served!",
        discountType: "FIXED_AMOUNT",
        discountValue: 100, // $100
        minOrderAmount: 150, // $150
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // One time use
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: null, // Unassigned - can be claimed
        createdBy: 1
    },
    {
        code: "MYSTERY80",
        name: "Mystery Discount",
        description: "Mystery 80% off deal - Only for the chosen one!",
        discountType: "PERCENTAGE",
        discountValue: 80,
        maxDiscountAmount: 400, // $400
        minOrderAmount: 250, // $250
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // One time use
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: null, // Unassigned - can be claimed
        createdBy: 1
    },
    {
        code: "FIRSTLOVE",
        name: "First Love Discount",
        description: "Special $150 off for your first booking with us!",
        discountType: "FIXED_AMOUNT",
        discountValue: 150, // $150
        minOrderAmount: 300, // $300
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1, // One time use
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false, // PRIVATE
        userId: null, // Unassigned - can be claimed
        createdBy: 1
    }
];

// Hàm seed dữ liệu
const seedPromotions = async () => {
    try {
        // Wait for connection to be ready
        await mongoose.connection.asPromise();
        console.log("🔗 Database connection confirmed");

        // Xóa tất cả dữ liệu promotion cũ
        console.log("🗑️  Đang xóa tất cả dữ liệu promotion cũ...");
        const deleteResult = await Promotion.deleteMany({});
        console.log(`✅ Đã xóa ${deleteResult.deletedCount} promotion cũ`);

        // Thêm dữ liệu mới
        console.log("📝 Đang thêm dữ liệu promotion mới...");
        const insertResult = await Promotion.insertMany(promotions);
        console.log(`✅ Đã thêm ${insertResult.length} promotion mới thành công`);

        // Hiển thị danh sách promotion đã thêm
        console.log("\n📋 Danh sách promotion đã được thêm:");
        insertResult.forEach((promo, index) => {
            const type = promo.isPublic ? '� PUBLIC' : `� PRIVATE (User ${promo.userId})`;
            const usage = promo.usageLimit ? `${promo.usedCount}/${promo.usageLimit}` : 'Unlimited';
            const discount = promo.discountType === 'PERCENTAGE' ? `${promo.discountValue}%` : `$${promo.discountValue}`;
            console.log(`${index + 1}. ${promo.code} - ${promo.name} (${type}) - ${discount} off - Usage: ${usage}`);
        });

        console.log("\n🎉 Seed dữ liệu promotion hoàn tất!");
        console.log("\n📊 Summary:");
        const publicCount = insertResult.filter(p => p.isPublic).length;
        const privateCount = insertResult.filter(p => !p.isPublic).length;
        console.log(`   📢 Public promotions: ${publicCount} (visible to all users)`);
        console.log(`   🔒 Private promotions: ${privateCount} (specific users only)`);

        console.log("\n🧪 Test Scenarios:");
        console.log("   - User 1: Can see all public promotions");
        console.log("   - User 2: Can see public + VIP_USER2 (50% off, 2 uses left)");
        console.log("   - User 3: Can see public + WELCOME_USER3 ($100 off)");
        console.log("   - User 4: Can see public + BIRTHDAY_USER4 (75% off)");
        console.log("   - User 5: Can see public + LOYALTY_USER5 ($200 off, 2 uses)");
        console.log("   - FLASH30: Limited quantity (15 uses left!)");
        console.log("   - FAMILY40: High minimum order ($2000)");
        console.log("   - UNASSIGNED PRIVATE: 5 claimable promotions (NEWUSER50, EXCLUSIVE75, LUCKY100, MYSTERY80, FIRSTLOVE)");

        mongoose.disconnect();
    } catch (error) {
        console.error("❌ Lỗi khi seed dữ liệu:", error);
        mongoose.disconnect();
        process.exit(1);
    }
};

// Chạy seed function với delay để đảm bảo connection
setTimeout(() => {
    seedPromotions();
}, 1000);
