const mongoose = require('mongoose');

const promotionReservationSchema = new mongoose.Schema(
  {
    promotionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Promotion',
      required: true,
    },
    userId: {
      type: Number,
      ref: 'User',
      required: true,
    },
    reservationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reservation',
      default: null, // Null khi chỉ reserve, có giá trị khi booking được tạo
    },
    status: {
      type: String,
      enum: ['RESERVED', 'CONFIRMED', 'EXPIRED', 'CANCELLED'],
      default: 'RESERVED',
    },
    orderAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    discountAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
    reservedAt: {
      type: Date,
      default: Date.now,
    },
    confirmedAt: {
      type: Date,
      default: null,
    },
    // Metadata để audit và debugging
    metadata: {
      userAgent: String,
      ipAddress: String,
      sessionId: String,
      appliedDiscountType: {
        type: String,
        enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
        required: true,
      },
      appliedDiscountValue: {
        type: Number,
        required: true,
      },
    },
  },
  { 
    versionKey: false,
    timestamps: true 
  }
);

// Compound indexes để tối ưu query
promotionReservationSchema.index({ promotionId: 1, status: 1 });
promotionReservationSchema.index({ userId: 1, status: 1 });
promotionReservationSchema.index({ status: 1, expiresAt: 1 }); // Cho cleanup
promotionReservationSchema.index({ reservationId: 1 }, { sparse: true });

// TTL index để tự động xóa expired records sau 24h
promotionReservationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 86400 });

// Static method để reserve promotion
promotionReservationSchema.statics.reservePromotion = async function(data) {
  const { promotionId, userId, orderAmount, discountAmount, metadata = {} } = data;
  
  // Set expiration time (15 minutes from now)
  const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
  
  const reservation = new this({
    promotionId,
    userId,
    orderAmount,
    discountAmount,
    expiresAt,
    metadata,
    status: 'RESERVED'
  });
  
  return await reservation.save();
};

// Static method để confirm reservation khi booking thành công
promotionReservationSchema.statics.confirmReservation = async function(promotionId, userId, reservationId) {
  return await this.findOneAndUpdate(
    {
      promotionId,
      userId,
      status: 'RESERVED',
      expiresAt: { $gt: new Date() }
    },
    {
      status: 'CONFIRMED',
      reservationId,
      confirmedAt: new Date()
    },
    { new: true }
  );
};

// Static method để cancel reservation
promotionReservationSchema.statics.cancelReservation = async function(promotionId, userId) {
  return await this.findOneAndUpdate(
    {
      promotionId,
      userId,
      status: 'RESERVED'
    },
    {
      status: 'CANCELLED'
    },
    { new: true }
  );
};

// Static method để cleanup expired reservations
promotionReservationSchema.statics.cleanupExpired = async function() {
  const result = await this.updateMany(
    {
      status: 'RESERVED',
      expiresAt: { $lt: new Date() }
    },
    {
      status: 'EXPIRED'
    }
  );
  
  return result.modifiedCount;
};

// Static method để check user có reservation active không
promotionReservationSchema.statics.hasActiveReservation = async function(promotionId, userId) {
  const reservation = await this.findOne({
    promotionId,
    userId,
    status: 'RESERVED',
    expiresAt: { $gt: new Date() }
  });
  
  return !!reservation;
};

// Static method để get user's active reservations
promotionReservationSchema.statics.getUserActiveReservations = async function(userId) {
  return await this.find({
    userId,
    status: 'RESERVED',
    expiresAt: { $gt: new Date() }
  }).populate('promotionId', 'code name discountType discountValue');
};

module.exports = mongoose.model('PromotionReservation', promotionReservationSchema);
