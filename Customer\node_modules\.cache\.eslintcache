[{"E:\\WDP301_UROOM\\Customer\\src\\index.js": "1", "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Customer\\src\\App.js": "3", "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js": "5", "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js": "6", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js": "7", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js": "8", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js": "9", "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx": "10", "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx": "11", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx": "12", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx": "13", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx": "14", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx": "15", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx": "16", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx": "17", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx": "18", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx": "19", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx": "20", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx": "21", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx": "22", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx": "23", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx": "24", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx": "25", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx": "26", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx": "27", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx": "28", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx": "29", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx": "30", "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js": "31", "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js": "32", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx": "33", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx": "34", "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx": "35", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js": "36", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js": "37", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js": "38", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js": "39", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js": "40", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js": "41", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js": "42", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js": "43", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js": "44", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js": "45", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js": "46", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js": "47", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js": "48", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js": "49", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js": "50", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js": "51", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js": "52", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js": "53", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js": "54", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js": "55", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js": "56", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js": "57", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js": "58", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js": "59", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js": "60", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js": "61", "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js": "62", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js": "63", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js": "64", "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx": "65", "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx": "66", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js": "67", "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx": "68", "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx": "69", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx": "70", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx": "71", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js": "72", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx": "73", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx": "74", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx": "75", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx": "76", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx": "77", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx": "78", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx": "79", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx": "80", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js": "81", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js": "82", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js": "83", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js": "84", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js": "85", "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js": "86", "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js": "87", "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js": "88", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx": "89", "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js": "90", "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js": "91", "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js": "92", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx": "93", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx": "94", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js": "95", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js": "96", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js": "97", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js": "98", "E:\\WDP301_UROOM\\Customer\\src\\socket.js": "99", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx": "100", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx": "101", "E:\\WDP301_UROOM\\Customer\\src\\utils\\apiConfig.js": "102", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\promotionSlice.js": "103", "E:\\WDP301_UROOM\\Customer\\src\\hooks\\usePromotionValidation.js": "104"}, {"size": 831, "mtime": 1750045388384, "results": "105", "hashOfConfig": "106"}, {"size": 375, "mtime": 1750045388405, "results": "107", "hashOfConfig": "106"}, {"size": 4850, "mtime": 1751248384117, "results": "108", "hashOfConfig": "106"}, {"size": 1287, "mtime": 1752941662446, "results": "109", "hashOfConfig": "106"}, {"size": 1300, "mtime": 1750045388406, "results": "110", "hashOfConfig": "106"}, {"size": 1451, "mtime": 1750045388405, "results": "111", "hashOfConfig": "106"}, {"size": 1133, "mtime": 1752941647991, "results": "112", "hashOfConfig": "106"}, {"size": 731, "mtime": 1751349366731, "results": "113", "hashOfConfig": "106"}, {"size": 3227, "mtime": 1750128490073, "results": "114", "hashOfConfig": "106"}, {"size": 1928, "mtime": 1752937073241, "results": "115", "hashOfConfig": "106"}, {"size": 1374, "mtime": 1750045388385, "results": "116", "hashOfConfig": "106"}, {"size": 41908, "mtime": 1752937055687, "results": "117", "hashOfConfig": "106"}, {"size": 38585, "mtime": 1752937055688, "results": "118", "hashOfConfig": "106"}, {"size": 38536, "mtime": 1752956940552, "results": "119", "hashOfConfig": "106"}, {"size": 88624, "mtime": 1752937055686, "results": "120", "hashOfConfig": "106"}, {"size": 3330, "mtime": 1752937073245, "results": "121", "hashOfConfig": "106"}, {"size": 7041, "mtime": 1750045388389, "results": "122", "hashOfConfig": "106"}, {"size": 4608, "mtime": 1750712271161, "results": "123", "hashOfConfig": "106"}, {"size": 51823, "mtime": 1751418509021, "results": "124", "hashOfConfig": "106"}, {"size": 26015, "mtime": 1750045388387, "results": "125", "hashOfConfig": "106"}, {"size": 13701, "mtime": 1750045388389, "results": "126", "hashOfConfig": "106"}, {"size": 10248, "mtime": 1752937073250, "results": "127", "hashOfConfig": "106"}, {"size": 3742, "mtime": 1750045388396, "results": "128", "hashOfConfig": "106"}, {"size": 7427, "mtime": 1750045388396, "results": "129", "hashOfConfig": "106"}, {"size": 5465, "mtime": 1750045388396, "results": "130", "hashOfConfig": "106"}, {"size": 8504, "mtime": 1750045388397, "results": "131", "hashOfConfig": "106"}, {"size": 7544, "mtime": 1750045388397, "results": "132", "hashOfConfig": "106"}, {"size": 5583, "mtime": 1751349366727, "results": "133", "hashOfConfig": "106"}, {"size": 33077, "mtime": 1751348982932, "results": "134", "hashOfConfig": "106"}, {"size": 16963, "mtime": 1750712271164, "results": "135", "hashOfConfig": "106"}, {"size": 10836, "mtime": 1752937055697, "results": "136", "hashOfConfig": "106"}, {"size": 4408, "mtime": 1751418509025, "results": "137", "hashOfConfig": "106"}, {"size": 2571, "mtime": 1750128490060, "results": "138", "hashOfConfig": "106"}, {"size": 25894, "mtime": 1750128490061, "results": "139", "hashOfConfig": "106"}, {"size": 2587, "mtime": 1750045388385, "results": "140", "hashOfConfig": "106"}, {"size": 1803, "mtime": 1750045388403, "results": "141", "hashOfConfig": "106"}, {"size": 541, "mtime": 1750045388403, "results": "142", "hashOfConfig": "106"}, {"size": 4244, "mtime": 1750045388399, "results": "143", "hashOfConfig": "106"}, {"size": 2230, "mtime": 1750045388398, "results": "144", "hashOfConfig": "106"}, {"size": 1043, "mtime": 1750128490070, "results": "145", "hashOfConfig": "106"}, {"size": 487, "mtime": 1750045388399, "results": "146", "hashOfConfig": "106"}, {"size": 5057, "mtime": 1750045388398, "results": "147", "hashOfConfig": "106"}, {"size": 223, "mtime": 1750045388405, "results": "148", "hashOfConfig": "106"}, {"size": 1136, "mtime": 1750045388398, "results": "149", "hashOfConfig": "106"}, {"size": 9581, "mtime": 1750045388398, "results": "150", "hashOfConfig": "106"}, {"size": 888, "mtime": 1750045388399, "results": "151", "hashOfConfig": "106"}, {"size": 919, "mtime": 1750045388402, "results": "152", "hashOfConfig": "106"}, {"size": 3342, "mtime": 1750045388402, "results": "153", "hashOfConfig": "106"}, {"size": 265, "mtime": 1750128490068, "results": "154", "hashOfConfig": "106"}, {"size": 1693, "mtime": 1750128490069, "results": "155", "hashOfConfig": "106"}, {"size": 235, "mtime": 1750045388403, "results": "156", "hashOfConfig": "106"}, {"size": 1669, "mtime": 1750712271166, "results": "157", "hashOfConfig": "106"}, {"size": 551, "mtime": 1750045388399, "results": "158", "hashOfConfig": "106"}, {"size": 3000, "mtime": 1750045388402, "results": "159", "hashOfConfig": "106"}, {"size": 137, "mtime": 1750045388403, "results": "160", "hashOfConfig": "106"}, {"size": 2085, "mtime": 1750045388401, "results": "161", "hashOfConfig": "106"}, {"size": 1536, "mtime": 1750045388398, "results": "162", "hashOfConfig": "106"}, {"size": 1271, "mtime": 1750045388397, "results": "163", "hashOfConfig": "106"}, {"size": 917, "mtime": 1750045388401, "results": "164", "hashOfConfig": "106"}, {"size": 574, "mtime": 1750045388398, "results": "165", "hashOfConfig": "106"}, {"size": 322, "mtime": 1750045388399, "results": "166", "hashOfConfig": "106"}, {"size": 551, "mtime": 1750045388406, "results": "167", "hashOfConfig": "106"}, {"size": 1439, "mtime": 1750045388397, "results": "168", "hashOfConfig": "106"}, {"size": 426, "mtime": 1750045388401, "results": "169", "hashOfConfig": "106"}, {"size": 2348, "mtime": 1750045388347, "results": "170", "hashOfConfig": "106"}, {"size": 1621, "mtime": 1750045388347, "results": "171", "hashOfConfig": "106"}, {"size": 402, "mtime": 1750045388402, "results": "172", "hashOfConfig": "106"}, {"size": 864, "mtime": 1750045388347, "results": "173", "hashOfConfig": "106"}, {"size": 1493, "mtime": 1750045388347, "results": "174", "hashOfConfig": "106"}, {"size": 2146, "mtime": 1750045388396, "results": "175", "hashOfConfig": "106"}, {"size": 1088, "mtime": 1750045388391, "results": "176", "hashOfConfig": "106"}, {"size": 438, "mtime": 1750045388402, "results": "177", "hashOfConfig": "106"}, {"size": 16495, "mtime": 1750861714769, "results": "178", "hashOfConfig": "106"}, {"size": 8729, "mtime": 1750128490066, "results": "179", "hashOfConfig": "106"}, {"size": 27188, "mtime": 1752937073249, "results": "180", "hashOfConfig": "106"}, {"size": 30766, "mtime": 1751418509023, "results": "181", "hashOfConfig": "106"}, {"size": 9884, "mtime": 1750045388394, "results": "182", "hashOfConfig": "106"}, {"size": 5820, "mtime": 1750045388394, "results": "183", "hashOfConfig": "106"}, {"size": 14855, "mtime": 1751418509024, "results": "184", "hashOfConfig": "106"}, {"size": 21286, "mtime": 1750128490068, "results": "185", "hashOfConfig": "106"}, {"size": 741, "mtime": 1750045388403, "results": "186", "hashOfConfig": "106"}, {"size": 582, "mtime": 1750045388402, "results": "187", "hashOfConfig": "106"}, {"size": 491, "mtime": 1750045388401, "results": "188", "hashOfConfig": "106"}, {"size": 377, "mtime": 1750045388399, "results": "189", "hashOfConfig": "106"}, {"size": 1071, "mtime": 1750045388399, "results": "190", "hashOfConfig": "106"}, {"size": 2824, "mtime": 1752937073239, "results": "191", "hashOfConfig": "106"}, {"size": 2658, "mtime": 1750045388385, "results": "192", "hashOfConfig": "106"}, {"size": 693, "mtime": 1750045388385, "results": "193", "hashOfConfig": "106"}, {"size": 11166, "mtime": 1752937055689, "results": "194", "hashOfConfig": "106"}, {"size": 830, "mtime": 1750128490069, "results": "195", "hashOfConfig": "106"}, {"size": 636, "mtime": 1750128490073, "results": "196", "hashOfConfig": "106"}, {"size": 2015, "mtime": 1750128490072, "results": "197", "hashOfConfig": "106"}, {"size": 22383, "mtime": 1752957483412, "results": "198", "hashOfConfig": "106"}, {"size": 23165, "mtime": 1752944270161, "results": "199", "hashOfConfig": "106"}, {"size": 3738, "mtime": 1752937055695, "results": "200", "hashOfConfig": "106"}, {"size": 1177, "mtime": 1751349366728, "results": "201", "hashOfConfig": "106"}, {"size": 9044, "mtime": 1752944429452, "results": "202", "hashOfConfig": "106"}, {"size": 341, "mtime": 1751349366728, "results": "203", "hashOfConfig": "106"}, {"size": 465, "mtime": 1750045388406, "results": "204", "hashOfConfig": "106"}, {"size": 1719, "mtime": 1751418509022, "results": "205", "hashOfConfig": "106"}, {"size": 1048, "mtime": 1752937073248, "results": "206", "hashOfConfig": "106"}, {"size": 346, "mtime": 1752937055696, "results": "207", "hashOfConfig": "106"}, {"size": 8364, "mtime": 1752956890374, "results": "208", "hashOfConfig": "106"}, {"size": 6517, "mtime": 1752941316261, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xcqp28", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\WDP301_UROOM\\Customer\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\App.js", ["522", "523", "524"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx", ["525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx", ["540"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx", ["541", "542", "543", "544", "545", "546", "547", "548", "549", "550"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx", ["551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx", ["565", "566"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx", ["567", "568", "569", "570", "571"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx", ["572", "573"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx", ["574", "575", "576", "577", "578"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx", ["579", "580", "581", "582", "583", "584", "585"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx", ["586", "587", "588"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx", ["589", "590"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx", ["591", "592", "593", "594", "595"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx", ["596", "597"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx", ["598", "599", "600"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx", ["601", "602", "603", "604", "605"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx", ["606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx", ["619", "620", "621", "622", "623", "624", "625"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx", ["626", "627", "628", "629"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js", ["630"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx", ["631", "632", "633"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx", ["634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx", ["646"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js", ["647", "648"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js", ["649", "650", "651", "652", "653", "654"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js", ["655"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx", ["656", "657"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx", ["658", "659"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx", ["660", "661", "662", "663", "664", "665", "666", "667"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx", ["668", "669", "670", "671"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx", ["672"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx", ["673", "674"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx", ["675", "676", "677", "678", "679"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx", ["680", "681", "682", "683", "684"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js", ["685", "686", "687"], [], "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js", ["688", "689", "690"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js", ["691"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx", ["692", "693", "694", "695"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx", ["696", "697", "698"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx", ["699", "700"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\socket.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\apiConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\promotionSlice.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\hooks\\usePromotionValidation.js", ["701", "702"], [], {"ruleId": "703", "severity": 1, "message": "704", "line": 3, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 3, "endColumn": 13}, {"ruleId": "707", "severity": 1, "message": "708", "line": 46, "column": 6, "nodeType": "709", "endLine": 46, "endColumn": 17, "suggestions": "710"}, {"ruleId": "711", "severity": 1, "message": "712", "line": 103, "column": 61, "nodeType": "713", "messageId": "714", "endLine": 103, "endColumn": 76}, {"ruleId": "703", "severity": 1, "message": "715", "line": 33, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 33, "endColumn": 14}, {"ruleId": "703", "severity": 1, "message": "716", "line": 34, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 34, "endColumn": 14}, {"ruleId": "703", "severity": 1, "message": "717", "line": 35, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 35, "endColumn": 14}, {"ruleId": "703", "severity": 1, "message": "718", "line": 63, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 63, "endColumn": 19}, {"ruleId": "703", "severity": 1, "message": "704", "line": 64, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 64, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "719", "line": 87, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 87, "endColumn": 16}, {"ruleId": "707", "severity": 1, "message": "708", "line": 97, "column": 6, "nodeType": "709", "endLine": 97, "endColumn": 8, "suggestions": "720"}, {"ruleId": "703", "severity": 1, "message": "721", "line": 165, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 165, "endColumn": 16}, {"ruleId": "722", "severity": 1, "message": "723", "line": 638, "column": 24, "nodeType": "724", "messageId": "725", "endLine": 638, "endColumn": 26}, {"ruleId": "722", "severity": 1, "message": "726", "line": 665, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 665, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 675, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 675, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 685, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 685, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "723", "line": 907, "column": 29, "nodeType": "724", "messageId": "725", "endLine": 907, "endColumn": 31}, {"ruleId": "703", "severity": 1, "message": "727", "line": 1004, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 1004, "endColumn": 25}, {"ruleId": "722", "severity": 1, "message": "723", "line": 1203, "column": 39, "nodeType": "724", "messageId": "725", "endLine": 1203, "endColumn": 41}, {"ruleId": "728", "severity": 1, "message": "729", "line": 835, "column": 37, "nodeType": "713", "endLine": 842, "endColumn": 38}, {"ruleId": "703", "severity": 1, "message": "730", "line": 2, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 2, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "731", "line": 10, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 10, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "732", "line": 30, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 30, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "733", "line": 31, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 31, "endColumn": 20}, {"ruleId": "703", "severity": 1, "message": "734", "line": 64, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 64, "endColumn": 30}, {"ruleId": "703", "severity": 1, "message": "735", "line": 65, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 65, "endColumn": 28}, {"ruleId": "703", "severity": 1, "message": "736", "line": 85, "column": 33, "nodeType": "705", "messageId": "706", "endLine": 85, "endColumn": 57}, {"ruleId": "703", "severity": 1, "message": "737", "line": 87, "column": 46, "nodeType": "705", "messageId": "706", "endLine": 87, "endColumn": 83}, {"ruleId": "728", "severity": 1, "message": "729", "line": 650, "column": 21, "nodeType": "713", "endLine": 654, "endColumn": 22}, {"ruleId": "728", "severity": 1, "message": "729", "line": 688, "column": 23, "nodeType": "713", "endLine": 702, "endColumn": 24}, {"ruleId": "703", "severity": 1, "message": "738", "line": 6, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 6, "endColumn": 8}, {"ruleId": "703", "severity": 1, "message": "739", "line": 159, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 159, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "740", "line": 159, "column": 23, "nodeType": "705", "messageId": "706", "endLine": 159, "endColumn": 37}, {"ruleId": "707", "severity": 1, "message": "741", "line": 180, "column": 6, "nodeType": "709", "endLine": 180, "endColumn": 8, "suggestions": "742"}, {"ruleId": "707", "severity": 1, "message": "743", "line": 315, "column": 6, "nodeType": "709", "endLine": 315, "endColumn": 25, "suggestions": "744"}, {"ruleId": "703", "severity": 1, "message": "745", "line": 866, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 866, "endColumn": 15}, {"ruleId": "722", "severity": 1, "message": "723", "line": 959, "column": 28, "nodeType": "724", "messageId": "725", "endLine": 959, "endColumn": 30}, {"ruleId": "728", "severity": 1, "message": "729", "line": 1071, "column": 19, "nodeType": "713", "endLine": 1082, "endColumn": 20}, {"ruleId": "746", "severity": 1, "message": "747", "line": 1130, "column": 63, "nodeType": "748", "messageId": "749", "endLine": 1130, "endColumn": 65}, {"ruleId": "722", "severity": 1, "message": "723", "line": 1764, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 1764, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "726", "line": 2061, "column": 39, "nodeType": "724", "messageId": "725", "endLine": 2061, "endColumn": 41}, {"ruleId": "722", "severity": 1, "message": "723", "line": 2241, "column": 42, "nodeType": "724", "messageId": "725", "endLine": 2241, "endColumn": 44}, {"ruleId": "728", "severity": 1, "message": "729", "line": 2390, "column": 13, "nodeType": "713", "endLine": 2390, "endColumn": 41}, {"ruleId": "728", "severity": 1, "message": "729", "line": 2391, "column": 32, "nodeType": "713", "endLine": 2391, "endColumn": 60}, {"ruleId": "703", "severity": 1, "message": "750", "line": 1, "column": 17, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 26}, {"ruleId": "703", "severity": 1, "message": "751", "line": 10, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 10, "endColumn": 17}, {"ruleId": "707", "severity": 1, "message": "752", "line": 35, "column": 5, "nodeType": "709", "endLine": 35, "endColumn": 7, "suggestions": "753"}, {"ruleId": "707", "severity": 1, "message": "754", "line": 55, "column": 6, "nodeType": "709", "endLine": 55, "endColumn": 16, "suggestions": "755"}, {"ruleId": "707", "severity": 1, "message": "756", "line": 94, "column": 6, "nodeType": "709", "endLine": 94, "endColumn": 8, "suggestions": "757"}, {"ruleId": "707", "severity": 1, "message": "758", "line": 107, "column": 6, "nodeType": "709", "endLine": 107, "endColumn": 26, "suggestions": "759"}, {"ruleId": "760", "severity": 1, "message": "761", "line": 190, "column": 19, "nodeType": "713", "endLine": 190, "endColumn": 40}, {"ruleId": "703", "severity": 1, "message": "762", "line": 6, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 6, "endColumn": 21}, {"ruleId": "707", "severity": 1, "message": "763", "line": 35, "column": 6, "nodeType": "709", "endLine": 35, "endColumn": 31, "suggestions": "764"}, {"ruleId": "722", "severity": 1, "message": "726", "line": 650, "column": 38, "nodeType": "724", "messageId": "725", "endLine": 650, "endColumn": 40}, {"ruleId": "703", "severity": 1, "message": "765", "line": 667, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 667, "endColumn": 29}, {"ruleId": "722", "severity": 1, "message": "723", "line": 907, "column": 18, "nodeType": "724", "messageId": "725", "endLine": 907, "endColumn": 20}, {"ruleId": "703", "severity": 1, "message": "766", "line": 944, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 944, "endColumn": 19}, {"ruleId": "722", "severity": 1, "message": "726", "line": 1366, "column": 46, "nodeType": "724", "messageId": "725", "endLine": 1366, "endColumn": 48}, {"ruleId": "703", "severity": 1, "message": "767", "line": 35, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 35, "endColumn": 20}, {"ruleId": "707", "severity": 1, "message": "768", "line": 83, "column": 6, "nodeType": "709", "endLine": 83, "endColumn": 8, "suggestions": "769"}, {"ruleId": "707", "severity": 1, "message": "770", "line": 87, "column": 6, "nodeType": "709", "endLine": 87, "endColumn": 20, "suggestions": "771"}, {"ruleId": "722", "severity": 1, "message": "726", "line": 121, "column": 29, "nodeType": "724", "messageId": "725", "endLine": 121, "endColumn": 31}, {"ruleId": "707", "severity": 1, "message": "768", "line": 144, "column": 6, "nodeType": "709", "endLine": 144, "endColumn": 44, "suggestions": "772"}, {"ruleId": "722", "severity": 1, "message": "726", "line": 429, "column": 46, "nodeType": "724", "messageId": "725", "endLine": 429, "endColumn": 48}, {"ruleId": "722", "severity": 1, "message": "726", "line": 442, "column": 38, "nodeType": "724", "messageId": "725", "endLine": 442, "endColumn": 40}, {"ruleId": "703", "severity": 1, "message": "773", "line": 22, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 22, "endColumn": 13}, {"ruleId": "728", "severity": 1, "message": "729", "line": 249, "column": 29, "nodeType": "713", "endLine": 253, "endColumn": 30}, {"ruleId": "728", "severity": 1, "message": "729", "line": 258, "column": 29, "nodeType": "713", "endLine": 266, "endColumn": 30}, {"ruleId": "703", "severity": 1, "message": "774", "line": 4, "column": 29, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 40}, {"ruleId": "728", "severity": 1, "message": "729", "line": 243, "column": 17, "nodeType": "713", "endLine": 247, "endColumn": 52}, {"ruleId": "703", "severity": 1, "message": "774", "line": 4, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "775", "line": 10, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 10, "endColumn": 19}, {"ruleId": "703", "severity": 1, "message": "730", "line": 12, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 12, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "776", "line": 15, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 15, "endColumn": 17}, {"ruleId": "703", "severity": 1, "message": "777", "line": 63, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 63, "endColumn": 33}, {"ruleId": "703", "severity": 1, "message": "774", "line": 4, "column": 29, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 40}, {"ruleId": "728", "severity": 1, "message": "729", "line": 215, "column": 17, "nodeType": "713", "endLine": 219, "endColumn": 52}, {"ruleId": "703", "severity": 1, "message": "774", "line": 4, "column": 29, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 40}, {"ruleId": "703", "severity": 1, "message": "738", "line": 6, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 6, "endColumn": 15}, {"ruleId": "703", "severity": 1, "message": "730", "line": 9, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 9, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "774", "line": 3, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 3, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "730", "line": 10, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 10, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "778", "line": 22, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 22, "endColumn": 19}, {"ruleId": "703", "severity": 1, "message": "779", "line": 27, "column": 17, "nodeType": "705", "messageId": "706", "endLine": 27, "endColumn": 25}, {"ruleId": "728", "severity": 1, "message": "780", "line": 203, "column": 17, "nodeType": "713", "endLine": 203, "endColumn": 89}, {"ruleId": "703", "severity": 1, "message": "750", "line": 1, "column": 17, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 26}, {"ruleId": "703", "severity": 1, "message": "781", "line": 1, "column": 28, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 36}, {"ruleId": "703", "severity": 1, "message": "762", "line": 25, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 25, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "783", "line": 71, "column": 21, "nodeType": "713", "endLine": 83, "endColumn": 23}, {"ruleId": "722", "severity": 1, "message": "726", "line": 96, "column": 39, "nodeType": "724", "messageId": "725", "endLine": 96, "endColumn": 41}, {"ruleId": "722", "severity": 1, "message": "726", "line": 114, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 114, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 115, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 115, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 117, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 117, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 118, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 118, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 119, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 119, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 120, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 120, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 121, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 121, "endColumn": 28}, {"ruleId": "722", "severity": 1, "message": "726", "line": 122, "column": 26, "nodeType": "724", "messageId": "725", "endLine": 122, "endColumn": 28}, {"ruleId": "703", "severity": 1, "message": "784", "line": 19, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 19, "endColumn": 10}, {"ruleId": "703", "severity": 1, "message": "785", "line": 20, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 20, "endColumn": 13}, {"ruleId": "703", "severity": 1, "message": "786", "line": 52, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 52, "endColumn": 20}, {"ruleId": "703", "severity": 1, "message": "787", "line": 59, "column": 15, "nodeType": "705", "messageId": "706", "endLine": 59, "endColumn": 21}, {"ruleId": "703", "severity": 1, "message": "788", "line": 60, "column": 15, "nodeType": "705", "messageId": "706", "endLine": 60, "endColumn": 25}, {"ruleId": "707", "severity": 1, "message": "789", "line": 75, "column": 6, "nodeType": "709", "endLine": 75, "endColumn": 10, "suggestions": "790"}, {"ruleId": "707", "severity": 1, "message": "791", "line": 84, "column": 6, "nodeType": "709", "endLine": 84, "endColumn": 25, "suggestions": "792"}, {"ruleId": "703", "severity": 1, "message": "793", "line": 39, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 39, "endColumn": 26}, {"ruleId": "703", "severity": 1, "message": "794", "line": 39, "column": 28, "nodeType": "705", "messageId": "706", "endLine": 39, "endColumn": 47}, {"ruleId": "707", "severity": 1, "message": "789", "line": 235, "column": 6, "nodeType": "709", "endLine": 235, "endColumn": 21, "suggestions": "795"}, {"ruleId": "707", "severity": 1, "message": "796", "line": 277, "column": 6, "nodeType": "709", "endLine": 277, "endColumn": 49, "suggestions": "797"}, {"ruleId": "703", "severity": 1, "message": "798", "line": 78, "column": 27, "nodeType": "705", "messageId": "706", "endLine": 78, "endColumn": 45}, {"ruleId": "728", "severity": 1, "message": "780", "line": 32, "column": 15, "nodeType": "713", "endLine": 52, "endColumn": 16}, {"ruleId": "722", "severity": 1, "message": "723", "line": 35, "column": 33, "nodeType": "724", "messageId": "725", "endLine": 35, "endColumn": 35}, {"ruleId": "728", "severity": 1, "message": "780", "line": 55, "column": 15, "nodeType": "713", "endLine": 55, "endColumn": 27}, {"ruleId": "703", "severity": 1, "message": "799", "line": 17, "column": 66, "nodeType": "705", "messageId": "706", "endLine": 17, "endColumn": 72}, {"ruleId": "703", "severity": 1, "message": "800", "line": 34, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 34, "endColumn": 16}, {"ruleId": "703", "severity": 1, "message": "801", "line": 34, "column": 18, "nodeType": "705", "messageId": "706", "endLine": 34, "endColumn": 27}, {"ruleId": "722", "severity": 1, "message": "723", "line": 256, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 256, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "723", "line": 268, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 268, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "723", "line": 280, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 280, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "723", "line": 292, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 292, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "723", "line": 304, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 304, "endColumn": 34}, {"ruleId": "722", "severity": 1, "message": "723", "line": 316, "column": 32, "nodeType": "724", "messageId": "725", "endLine": 316, "endColumn": 34}, {"ruleId": "728", "severity": 1, "message": "729", "line": 479, "column": 21, "nodeType": "713", "endLine": 487, "endColumn": 22}, {"ruleId": "722", "severity": 1, "message": "723", "line": 492, "column": 42, "nodeType": "724", "messageId": "725", "endLine": 492, "endColumn": 44}, {"ruleId": "722", "severity": 1, "message": "723", "line": 492, "column": 68, "nodeType": "724", "messageId": "725", "endLine": 492, "endColumn": 70}, {"ruleId": "707", "severity": 1, "message": "802", "line": 47, "column": 6, "nodeType": "709", "endLine": 47, "endColumn": 8, "suggestions": "803"}, {"ruleId": "703", "severity": 1, "message": "804", "line": 8, "column": 40, "nodeType": "705", "messageId": "706", "endLine": 8, "endColumn": 48}, {"ruleId": "703", "severity": 1, "message": "805", "line": 8, "column": 50, "nodeType": "705", "messageId": "706", "endLine": 8, "endColumn": 57}, {"ruleId": "703", "severity": 1, "message": "806", "line": 1, "column": 14, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 18}, {"ruleId": "703", "severity": 1, "message": "807", "line": 1, "column": 20, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 24}, {"ruleId": "703", "severity": 1, "message": "808", "line": 1, "column": 26, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 29}, {"ruleId": "703", "severity": 1, "message": "809", "line": 1, "column": 31, "nodeType": "705", "messageId": "706", "endLine": 1, "endColumn": 40}, {"ruleId": "703", "severity": 1, "message": "775", "line": 2, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 2, "endColumn": 19}, {"ruleId": "703", "severity": 1, "message": "751", "line": 3, "column": 8, "nodeType": "705", "messageId": "706", "endLine": 3, "endColumn": 17}, {"ruleId": "703", "severity": 1, "message": "805", "line": 72, "column": 44, "nodeType": "705", "messageId": "706", "endLine": 72, "endColumn": 51}, {"ruleId": "707", "severity": 1, "message": "810", "line": 101, "column": 6, "nodeType": "709", "endLine": 101, "endColumn": 12, "suggestions": "811"}, {"ruleId": "707", "severity": 1, "message": "812", "line": 136, "column": 6, "nodeType": "709", "endLine": 136, "endColumn": 46, "suggestions": "813"}, {"ruleId": "703", "severity": 1, "message": "814", "line": 2, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 2, "endColumn": 12}, {"ruleId": "703", "severity": 1, "message": "731", "line": 8, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 8, "endColumn": 13}, {"ruleId": "722", "severity": 1, "message": "726", "line": 48, "column": 18, "nodeType": "724", "messageId": "725", "endLine": 48, "endColumn": 20}, {"ruleId": "703", "severity": 1, "message": "815", "line": 52, "column": 24, "nodeType": "705", "messageId": "706", "endLine": 52, "endColumn": 39}, {"ruleId": "707", "severity": 1, "message": "816", "line": 128, "column": 6, "nodeType": "709", "endLine": 128, "endColumn": 16, "suggestions": "817"}, {"ruleId": "707", "severity": 1, "message": "818", "line": 216, "column": 6, "nodeType": "709", "endLine": 216, "endColumn": 46, "suggestions": "819"}, {"ruleId": "707", "severity": 1, "message": "820", "line": 265, "column": 6, "nodeType": "709", "endLine": 265, "endColumn": 32, "suggestions": "821"}, {"ruleId": "822", "severity": 1, "message": "823", "line": 306, "column": 12, "nodeType": "705", "messageId": "824", "endLine": 306, "endColumn": 25}, {"ruleId": "703", "severity": 1, "message": "825", "line": 332, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 332, "endColumn": 30}, {"ruleId": "722", "severity": 1, "message": "726", "line": 574, "column": 37, "nodeType": "724", "messageId": "725", "endLine": 574, "endColumn": 39}, {"ruleId": "707", "severity": 1, "message": "826", "line": 93, "column": 6, "nodeType": "709", "endLine": 93, "endColumn": 40, "suggestions": "827"}, {"ruleId": "707", "severity": 1, "message": "828", "line": 98, "column": 6, "nodeType": "709", "endLine": 98, "endColumn": 40, "suggestions": "829"}, {"ruleId": "707", "severity": 1, "message": "830", "line": 184, "column": 6, "nodeType": "709", "endLine": 184, "endColumn": 41, "suggestions": "831"}, {"ruleId": "703", "severity": 1, "message": "832", "line": 372, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 372, "endColumn": 19}, {"ruleId": "703", "severity": 1, "message": "750", "line": 2, "column": 17, "nodeType": "705", "messageId": "706", "endLine": 2, "endColumn": 26}, {"ruleId": "722", "severity": 1, "message": "723", "line": 67, "column": 31, "nodeType": "724", "messageId": "725", "endLine": 67, "endColumn": 33}, {"ruleId": "722", "severity": 1, "message": "723", "line": 141, "column": 33, "nodeType": "724", "messageId": "725", "endLine": 141, "endColumn": 35}, {"ruleId": "703", "severity": 1, "message": "833", "line": 17, "column": 26, "nodeType": "705", "messageId": "706", "endLine": 17, "endColumn": 27}, {"ruleId": "703", "severity": 1, "message": "834", "line": 17, "column": 29, "nodeType": "705", "messageId": "706", "endLine": 17, "endColumn": 35}, {"ruleId": "703", "severity": 1, "message": "835", "line": 17, "column": 37, "nodeType": "705", "messageId": "706", "endLine": 17, "endColumn": 42}, {"ruleId": "707", "severity": 1, "message": "836", "line": 36, "column": 6, "nodeType": "709", "endLine": 36, "endColumn": 16, "suggestions": "837"}, {"ruleId": "722", "severity": 1, "message": "726", "line": 226, "column": 33, "nodeType": "724", "messageId": "725", "endLine": 226, "endColumn": 35}, {"ruleId": "703", "severity": 1, "message": "838", "line": 2, "column": 52, "nodeType": "705", "messageId": "706", "endLine": 2, "endColumn": 57}, {"ruleId": "703", "severity": 1, "message": "839", "line": 20, "column": 9, "nodeType": "705", "messageId": "706", "endLine": 20, "endColumn": 13}, {"ruleId": "707", "severity": 1, "message": "840", "line": 67, "column": 6, "nodeType": "709", "endLine": 67, "endColumn": 30, "suggestions": "841"}, {"ruleId": "722", "severity": 1, "message": "726", "line": 353, "column": 36, "nodeType": "724", "messageId": "725", "endLine": 353, "endColumn": 38}, {"ruleId": "722", "severity": 1, "message": "726", "line": 361, "column": 36, "nodeType": "724", "messageId": "725", "endLine": 361, "endColumn": 38}, {"ruleId": "722", "severity": 1, "message": "723", "line": 12, "column": 33, "nodeType": "724", "messageId": "725", "endLine": 12, "endColumn": 35}, {"ruleId": "722", "severity": 1, "message": "723", "line": 15, "column": 37, "nodeType": "724", "messageId": "725", "endLine": 15, "endColumn": 39}, {"ruleId": "722", "severity": 1, "message": "723", "line": 18, "column": 33, "nodeType": "724", "messageId": "725", "endLine": 18, "endColumn": 35}, {"ruleId": "842", "severity": 1, "message": "843", "line": 10, "column": 3, "nodeType": "844", "messageId": "725", "endLine": 10, "endColumn": 17}, {"ruleId": "842", "severity": 1, "message": "845", "line": 11, "column": 3, "nodeType": "844", "messageId": "725", "endLine": 11, "endColumn": 18}, {"ruleId": "842", "severity": 1, "message": "846", "line": 48, "column": 3, "nodeType": "844", "messageId": "725", "endLine": 48, "endColumn": 28}, {"ruleId": "847", "severity": 1, "message": "848", "line": 37, "column": 1, "nodeType": "849", "endLine": 108, "endColumn": 3}, {"ruleId": "703", "severity": 1, "message": "850", "line": 4, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 14}, {"ruleId": "703", "severity": 1, "message": "851", "line": 4, "column": 23, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 26}, {"ruleId": "703", "severity": 1, "message": "852", "line": 4, "column": 28, "nodeType": "705", "messageId": "706", "endLine": 4, "endColumn": 31}, {"ruleId": "707", "severity": 1, "message": "853", "line": 165, "column": 6, "nodeType": "709", "endLine": 165, "endColumn": 8, "suggestions": "854"}, {"ruleId": "703", "severity": 1, "message": "855", "line": 13, "column": 10, "nodeType": "705", "messageId": "706", "endLine": 13, "endColumn": 27}, {"ruleId": "703", "severity": 1, "message": "856", "line": 13, "column": 29, "nodeType": "705", "messageId": "706", "endLine": 13, "endColumn": 49}, {"ruleId": "707", "severity": 1, "message": "857", "line": 26, "column": 6, "nodeType": "709", "endLine": 26, "endColumn": 24, "suggestions": "858"}, {"ruleId": "707", "severity": 1, "message": "859", "line": 21, "column": 9, "nodeType": "860", "endLine": 21, "endColumn": 69}, {"ruleId": "707", "severity": 1, "message": "861", "line": 187, "column": 6, "nodeType": "709", "endLine": 187, "endColumn": 57, "suggestions": "862"}, {"ruleId": "703", "severity": 1, "message": "863", "line": 7, "column": 3, "nodeType": "705", "messageId": "706", "endLine": 7, "endColumn": 11}, {"ruleId": "707", "severity": 1, "message": "864", "line": 56, "column": 29, "nodeType": "705", "endLine": 56, "endColumn": 40}, "no-unused-vars", "'Utils' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["865"], "react/jsx-pascal-case", "Imported JSX component Home_detail must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "'image4' is defined but never used.", "'image5' is defined but never used.", "'image6' is defined but never used.", "'RoomActions' is defined but never used.", "'hotels' is assigned a value but never used.", ["866"], "'errors' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "'CustomerReviews' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'axios' is defined but never used.", "'InputGroup' is defined but never used.", "'applyPromotionCode' is defined but never used.", "'validatePromotion' is defined but never used.", "'isPromotionValidating' is assigned a value but never used.", "'isPromotionApplying' is assigned a value but never used.", "'setIsValidatingPromotion' is assigned a value but never used.", "'setIsValidatingPromotionBeforeBooking' is assigned a value but never used.", "'Route' is defined but never used.", "'isSearching' is assigned a value but never used.", "'setIsSearching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleBackToBooking'. Either include it or remove the dependency array.", ["867"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["868"], "'status' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'useEffect' is defined but never used.", "'Factories' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReservation'. Either include it or remove the dependency array.", ["869"], "React Hook useEffect has missing dependencies: 'createdAt', 'idReservation', 'messageError', 'messageSuccess', 'navigate', and 'totalPrice'. Either include them or remove the dependency array.", ["870"], "React Hook useEffect has a missing dependency: 'handleAccept'. Either include it or remove the dependency array.", ["871"], "React Hook useEffect has a missing dependency: 'handleDelete'. Either include it or remove the dependency array.", ["872"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'useLocation' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleConfirm'. Either include it or remove the dependency array.", ["873"], "'currentServiceIndex' is assigned a value but never used.", "'totalPrice' is assigned a value but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["874"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["875"], ["876"], "'User' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'useState' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'FaPhone' is defined but never used.", "'FaEnvelope' is defined but never used.", "'fontLoaded' is assigned a value but never used.", "'pdfLib' is assigned a value but never used.", "'pdfFontLib' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReservationDetail'. Either include it or remove the dependency array.", ["877"], "React Hook useEffect has a missing dependency: 'fetchHotelDetails'. Either include it or remove the dependency array.", ["878"], "'userReservations' is assigned a value but never used.", "'setUserReservations' is assigned a value but never used.", ["879"], "React Hook useEffect has missing dependencies: 'dispatch' and 'fetchHotelDetails'. Either include them or remove the dependency array.", ["880"], "'setSearchParamsObj' is assigned a value but never used.", "'FaUser' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addressMap'. Either include it or remove the dependency array.", ["881"], "'onFailed' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'call' is defined but never used.", "'fork' is defined but never used.", "'put' is defined but never used.", "'takeEvery' is defined but never used.", "React Hook useEffect has a missing dependency: 'activePage'. Either include it or remove the dependency array.", ["882"], "React Hook useEffect has missing dependencies: 'activePage' and 'updateURL'. Either include them or remove the dependency array.", ["883"], "'Container' is defined but never used.", "'setItemsPerPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReservations'. Either include it or remove the dependency array.", ["884"], "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["885"], "React Hook useEffect has missing dependencies: 'activePage' and 'totalPages'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setActivePage' needs the current value of 'totalPages'.", ["886"], "no-redeclare", "'parseCurrency' is already defined.", "redeclared", "'calculateRefundPolicy' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activePage', 'sortOption', and 'starFilter'. Either include them or remove the dependency array.", ["887"], "React Hook useEffect has a missing dependency: 'fetchUserFeedbacks'. Either include it or remove the dependency array.", ["888"], "React Hook useEffect has missing dependencies: 'activePage', 'getFilteredFeedbacks', and 'updateURL'. Either include them or remove the dependency array.", ["889"], "'formatDate' is assigned a value but never used.", "'X' is defined but never used.", "'Pencil' is defined but never used.", "'Trash' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["890"], "'Toast' is defined but never used.", "'Auth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["891"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Form' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "React Hook useEffect has missing dependencies: 'calculateRefundPolicy' and 'setRefundAmount'. Either include them or remove the dependency array. If 'setRefundAmount' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["892"], "'selectedPromotion' is assigned a value but never used.", "'setSelectedPromotion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["893"], "The 'safePromotions' conditional could make the dependencies of useEffect Hook (at line 223) change on every render. To fix this, wrap the initialization of 'safePromotions' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'getPromotionStatus'. Either include it or remove the dependency array.", ["894"], "'setError' is defined but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"desc": "915", "fix": "916"}, {"desc": "917", "fix": "918"}, {"desc": "919", "fix": "920"}, {"desc": "921", "fix": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"desc": "937", "fix": "938"}, {"desc": "939", "fix": "940"}, {"desc": "941", "fix": "942"}, {"desc": "943", "fix": "944"}, {"desc": "945", "fix": "946"}, {"desc": "947", "fix": "948"}, {"desc": "949", "fix": "950"}, {"desc": "951", "fix": "952"}, {"desc": "953", "fix": "954"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [dispatch]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [handleBackToBooking]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [fetchReservation]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [handleAccept]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [timeLeft, navigate, handleDelete]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [reservationId, navigate, handleConfirm]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [fetchReservationDetail, id]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [fetchHotelDetails, reservationDetail]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [fetchReservationDetail, reservationId]", {"range": "983", "text": "984"}, "Update the dependencies array to be: [Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [addressMap]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [activePage, page]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [dispatch, fetchUserReservations]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [activeFilter, reservations, dateFilter, filters]", {"range": "995", "text": "996"}, "Update the dependencies array to be: [activePage, filterBill, itemsPerPage, totalPages]", {"range": "997", "text": "998"}, "Update the dependencies array to be: [activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", {"range": "999", "text": "1000"}, "Update the dependencies array to be: [dispatch, fetchUserFeedbacks, sortOption, starFilter]", {"range": "1001", "text": "1002"}, "Update the dependencies array to be: [feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", {"range": "1003", "text": "1004"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "1005", "text": "1006"}, "Update the dependencies array to be: [dispatch, activeStatus, fetchData]", {"range": "1007", "text": "1008"}, "Update the dependencies array to be: [calculateRefundPolicy, setRefundAmount]", {"range": "1009", "text": "1010"}, "Update the dependencies array to be: [fetchPromotions, show, totalPrice]", {"range": "1011", "text": "1012"}, "Update the dependencies array to be: [safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]", {"range": "1013", "text": "1014"}, [2163, 2174], "[Auth?._id, dispatch]", [3226, 3228], "[dispatch]", [6200, 6202], "[handleBackToBooking]", [9936, 9955], "[hotelId, dispatch, searchParams]", [1405, 1407], "[fetchReservation]", [1841, 1851], "[createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", [3122, 3124], "[handleAccept]", [3447, 3467], "[timeLeft, navigate, handleDelete]", [1197, 1222], "[reservationId, navigate, handleConfirm]", [2913, 2915], "[fetchAllUser]", [2972, 2986], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4575, 4613], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [2124, 2128], "[fetchReservationDetail, id]", [2328, 2347], "[fetchHotelDetails, reservationDetail]", [7500, 7515], "[fetchReservationDetail, reservationId]", [9032, 9075], "[Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", [1416, 1418], "[addressMap]", [3659, 3665], "[activePage, page]", [4664, 4704], "[dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", [4815, 4825], "[dispatch, fetchUserReservations]", [7810, 7850], "[activeFilter, reservations, dateFilter, filters]", [9284, 9310], "[activePage, filterBill, itemsPerPage, totalPages]", [3136, 3170], "[activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", [3276, 3310], "[dispatch, fetchUserFeedbacks, sortOption, starFilter]", [6005, 6040], "[feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", [1363, 1373], "[Auth._id, fetchUserReports]", [2318, 2342], "[dispatch, activeStatus, fetchData]", [5035, 5037], "[calculateRefundPolicy, setRefundAmount]", [1188, 1206], "[fetchPromotions, show, totalPrice]", [7357, 7408], "[safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]"]