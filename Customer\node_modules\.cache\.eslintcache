[{"E:\\WDP301_UROOM\\Customer\\src\\index.js": "1", "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js": "2", "E:\\WDP301_UROOM\\Customer\\src\\App.js": "3", "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js": "4", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js": "5", "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js": "6", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js": "7", "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js": "8", "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js": "9", "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx": "10", "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx": "11", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx": "12", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx": "13", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx": "14", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx": "15", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx": "16", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx": "17", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx": "18", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx": "19", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx": "20", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx": "21", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx": "22", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx": "23", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx": "24", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx": "25", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx": "26", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx": "27", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx": "28", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx": "29", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx": "30", "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js": "31", "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js": "32", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx": "33", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx": "34", "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx": "35", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js": "36", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js": "37", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js": "38", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js": "39", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js": "40", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js": "41", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js": "42", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js": "43", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js": "44", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js": "45", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js": "46", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js": "47", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js": "48", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js": "49", "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js": "50", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js": "51", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js": "52", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js": "53", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js": "54", "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js": "55", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js": "56", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js": "57", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js": "58", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js": "59", "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js": "60", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js": "61", "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js": "62", "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js": "63", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js": "64", "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx": "65", "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx": "66", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js": "67", "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx": "68", "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx": "69", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx": "70", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx": "71", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js": "72", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx": "73", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx": "74", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx": "75", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx": "76", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx": "77", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx": "78", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx": "79", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx": "80", "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js": "81", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js": "82", "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js": "83", "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js": "84", "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js": "85", "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js": "86", "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js": "87", "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js": "88", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx": "89", "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js": "90", "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js": "91", "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js": "92", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx": "93", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx": "94", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js": "95", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js": "96", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js": "97", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js": "98", "E:\\WDP301_UROOM\\Customer\\src\\socket.js": "99", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx": "100", "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx": "101", "E:\\WDP301_UROOM\\Customer\\src\\utils\\apiConfig.js": "102", "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\promotionSlice.js": "103"}, {"size": 831, "mtime": 1750045388384, "results": "104", "hashOfConfig": "105"}, {"size": 375, "mtime": 1750045388405, "results": "106", "hashOfConfig": "105"}, {"size": 4850, "mtime": 1751248384117, "results": "107", "hashOfConfig": "105"}, {"size": 1287, "mtime": 1752941662446, "results": "108", "hashOfConfig": "105"}, {"size": 1300, "mtime": 1750045388406, "results": "109", "hashOfConfig": "105"}, {"size": 1451, "mtime": 1750045388405, "results": "110", "hashOfConfig": "105"}, {"size": 1133, "mtime": 1752941647991, "results": "111", "hashOfConfig": "105"}, {"size": 731, "mtime": 1751349366731, "results": "112", "hashOfConfig": "105"}, {"size": 3227, "mtime": 1750128490073, "results": "113", "hashOfConfig": "105"}, {"size": 1928, "mtime": 1752937073241, "results": "114", "hashOfConfig": "105"}, {"size": 1374, "mtime": 1750045388385, "results": "115", "hashOfConfig": "105"}, {"size": 41908, "mtime": 1752937055687, "results": "116", "hashOfConfig": "105"}, {"size": 38585, "mtime": 1752937055688, "results": "117", "hashOfConfig": "105"}, {"size": 41061, "mtime": 1752937073242, "results": "118", "hashOfConfig": "105"}, {"size": 88624, "mtime": 1752937055686, "results": "119", "hashOfConfig": "105"}, {"size": 3330, "mtime": 1752937073245, "results": "120", "hashOfConfig": "105"}, {"size": 7041, "mtime": 1750045388389, "results": "121", "hashOfConfig": "105"}, {"size": 4608, "mtime": 1750712271161, "results": "122", "hashOfConfig": "105"}, {"size": 51823, "mtime": 1751418509021, "results": "123", "hashOfConfig": "105"}, {"size": 26015, "mtime": 1750045388387, "results": "124", "hashOfConfig": "105"}, {"size": 13701, "mtime": 1750045388389, "results": "125", "hashOfConfig": "105"}, {"size": 10248, "mtime": 1752937073250, "results": "126", "hashOfConfig": "105"}, {"size": 3742, "mtime": 1750045388396, "results": "127", "hashOfConfig": "105"}, {"size": 7427, "mtime": 1750045388396, "results": "128", "hashOfConfig": "105"}, {"size": 5465, "mtime": 1750045388396, "results": "129", "hashOfConfig": "105"}, {"size": 8504, "mtime": 1750045388397, "results": "130", "hashOfConfig": "105"}, {"size": 7544, "mtime": 1750045388397, "results": "131", "hashOfConfig": "105"}, {"size": 5583, "mtime": 1751349366727, "results": "132", "hashOfConfig": "105"}, {"size": 33077, "mtime": 1751348982932, "results": "133", "hashOfConfig": "105"}, {"size": 16963, "mtime": 1750712271164, "results": "134", "hashOfConfig": "105"}, {"size": 10836, "mtime": 1752937055697, "results": "135", "hashOfConfig": "105"}, {"size": 4408, "mtime": 1751418509025, "results": "136", "hashOfConfig": "105"}, {"size": 2571, "mtime": 1750128490060, "results": "137", "hashOfConfig": "105"}, {"size": 25894, "mtime": 1750128490061, "results": "138", "hashOfConfig": "105"}, {"size": 2587, "mtime": 1750045388385, "results": "139", "hashOfConfig": "105"}, {"size": 1803, "mtime": 1750045388403, "results": "140", "hashOfConfig": "105"}, {"size": 541, "mtime": 1750045388403, "results": "141", "hashOfConfig": "105"}, {"size": 4244, "mtime": 1750045388399, "results": "142", "hashOfConfig": "105"}, {"size": 2230, "mtime": 1750045388398, "results": "143", "hashOfConfig": "105"}, {"size": 1043, "mtime": 1750128490070, "results": "144", "hashOfConfig": "105"}, {"size": 487, "mtime": 1750045388399, "results": "145", "hashOfConfig": "105"}, {"size": 5057, "mtime": 1750045388398, "results": "146", "hashOfConfig": "105"}, {"size": 223, "mtime": 1750045388405, "results": "147", "hashOfConfig": "105"}, {"size": 1136, "mtime": 1750045388398, "results": "148", "hashOfConfig": "105"}, {"size": 9581, "mtime": 1750045388398, "results": "149", "hashOfConfig": "105"}, {"size": 888, "mtime": 1750045388399, "results": "150", "hashOfConfig": "105"}, {"size": 919, "mtime": 1750045388402, "results": "151", "hashOfConfig": "105"}, {"size": 3342, "mtime": 1750045388402, "results": "152", "hashOfConfig": "105"}, {"size": 265, "mtime": 1750128490068, "results": "153", "hashOfConfig": "105"}, {"size": 1693, "mtime": 1750128490069, "results": "154", "hashOfConfig": "105"}, {"size": 235, "mtime": 1750045388403, "results": "155", "hashOfConfig": "105"}, {"size": 1669, "mtime": 1750712271166, "results": "156", "hashOfConfig": "105"}, {"size": 551, "mtime": 1750045388399, "results": "157", "hashOfConfig": "105"}, {"size": 3000, "mtime": 1750045388402, "results": "158", "hashOfConfig": "105"}, {"size": 137, "mtime": 1750045388403, "results": "159", "hashOfConfig": "105"}, {"size": 2085, "mtime": 1750045388401, "results": "160", "hashOfConfig": "105"}, {"size": 1536, "mtime": 1750045388398, "results": "161", "hashOfConfig": "105"}, {"size": 1271, "mtime": 1750045388397, "results": "162", "hashOfConfig": "105"}, {"size": 917, "mtime": 1750045388401, "results": "163", "hashOfConfig": "105"}, {"size": 574, "mtime": 1750045388398, "results": "164", "hashOfConfig": "105"}, {"size": 322, "mtime": 1750045388399, "results": "165", "hashOfConfig": "105"}, {"size": 551, "mtime": 1750045388406, "results": "166", "hashOfConfig": "105"}, {"size": 1439, "mtime": 1750045388397, "results": "167", "hashOfConfig": "105"}, {"size": 426, "mtime": 1750045388401, "results": "168", "hashOfConfig": "105"}, {"size": 2348, "mtime": 1750045388347, "results": "169", "hashOfConfig": "105"}, {"size": 1621, "mtime": 1750045388347, "results": "170", "hashOfConfig": "105"}, {"size": 402, "mtime": 1750045388402, "results": "171", "hashOfConfig": "105"}, {"size": 864, "mtime": 1750045388347, "results": "172", "hashOfConfig": "105"}, {"size": 1493, "mtime": 1750045388347, "results": "173", "hashOfConfig": "105"}, {"size": 2146, "mtime": 1750045388396, "results": "174", "hashOfConfig": "105"}, {"size": 1088, "mtime": 1750045388391, "results": "175", "hashOfConfig": "105"}, {"size": 438, "mtime": 1750045388402, "results": "176", "hashOfConfig": "105"}, {"size": 16495, "mtime": 1750861714769, "results": "177", "hashOfConfig": "105"}, {"size": 8729, "mtime": 1750128490066, "results": "178", "hashOfConfig": "105"}, {"size": 27188, "mtime": 1752937073249, "results": "179", "hashOfConfig": "105"}, {"size": 30766, "mtime": 1751418509023, "results": "180", "hashOfConfig": "105"}, {"size": 9884, "mtime": 1750045388394, "results": "181", "hashOfConfig": "105"}, {"size": 5820, "mtime": 1750045388394, "results": "182", "hashOfConfig": "105"}, {"size": 14855, "mtime": 1751418509024, "results": "183", "hashOfConfig": "105"}, {"size": 21286, "mtime": 1750128490068, "results": "184", "hashOfConfig": "105"}, {"size": 741, "mtime": 1750045388403, "results": "185", "hashOfConfig": "105"}, {"size": 582, "mtime": 1750045388402, "results": "186", "hashOfConfig": "105"}, {"size": 491, "mtime": 1750045388401, "results": "187", "hashOfConfig": "105"}, {"size": 377, "mtime": 1750045388399, "results": "188", "hashOfConfig": "105"}, {"size": 1071, "mtime": 1750045388399, "results": "189", "hashOfConfig": "105"}, {"size": 2824, "mtime": 1752937073239, "results": "190", "hashOfConfig": "105"}, {"size": 2658, "mtime": 1750045388385, "results": "191", "hashOfConfig": "105"}, {"size": 693, "mtime": 1750045388385, "results": "192", "hashOfConfig": "105"}, {"size": 11166, "mtime": 1752937055689, "results": "193", "hashOfConfig": "105"}, {"size": 830, "mtime": 1750128490069, "results": "194", "hashOfConfig": "105"}, {"size": 636, "mtime": 1750128490073, "results": "195", "hashOfConfig": "105"}, {"size": 2015, "mtime": 1750128490072, "results": "196", "hashOfConfig": "105"}, {"size": 20822, "mtime": 1752944158092, "results": "197", "hashOfConfig": "105"}, {"size": 23165, "mtime": 1752944270161, "results": "198", "hashOfConfig": "105"}, {"size": 3738, "mtime": 1752937055695, "results": "199", "hashOfConfig": "105"}, {"size": 1177, "mtime": 1751349366728, "results": "200", "hashOfConfig": "105"}, {"size": 9044, "mtime": 1752944429452, "results": "201", "hashOfConfig": "105"}, {"size": 341, "mtime": 1751349366728, "results": "202", "hashOfConfig": "105"}, {"size": 465, "mtime": 1750045388406, "results": "203", "hashOfConfig": "105"}, {"size": 1719, "mtime": 1751418509022, "results": "204", "hashOfConfig": "105"}, {"size": 1048, "mtime": 1752937073248, "results": "205", "hashOfConfig": "105"}, {"size": 346, "mtime": 1752937055696, "results": "206", "hashOfConfig": "105"}, {"size": 8636, "mtime": 1752942247317, "results": "207", "hashOfConfig": "105"}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xcqp28", {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WDP301_UROOM\\Customer\\src\\index.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\reportWebVitals.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\App.js", ["517", "518", "519"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\store.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Routes.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\root-saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\Utils.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\BannedPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\ErrorPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomePage.jsx", ["520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx", ["535"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx", ["536", "537", "538", "539", "540"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx", ["541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx", ["555", "556"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx", ["557", "558", "559", "560", "561"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx", ["562", "563"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx", ["564", "565", "566", "567", "568"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx", ["569", "570", "571", "572", "573", "574", "575"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx", ["576", "577", "578"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx", ["579", "580"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx", ["581", "582", "583", "584", "585"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx", ["586", "587"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx", ["588", "589", "590"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx", ["591", "592", "593", "594", "595"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx", ["596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx", ["609", "610", "611", "612", "613", "614", "615"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx", ["616", "617", "618", "619"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\qaData.js", ["620"], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\data.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Footer.jsx", ["621", "622", "623"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\Header.jsx", ["624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\MapLocation.jsx", ["636"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\saga.js", ["637", "638"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\saga.js", ["639", "640", "641", "642", "643", "644"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\chatbox\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\saga.js", ["645"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\search\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\feedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\handleToken.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\auth\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\Pagination.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\components\\ToastContainer.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx", ["646", "647"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx", ["648", "649"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx", ["650", "651", "652", "653", "654", "655", "656", "657"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx", ["658", "659", "660", "661"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx", ["662"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx", ["663", "664"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx", ["665", "666", "667", "668", "669"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx", ["670", "671", "672", "673", "674"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\room\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reservations\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\message\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\hotel\\factories.js", ["675", "676", "677"], [], "E:\\WDP301_UROOM\\Customer\\src\\adapter\\ApiConstants.js", ["678", "679", "680"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\api\\index.js", ["681"], [], "E:\\WDP301_UROOM\\Customer\\src\\libs\\firebaseConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx", ["682", "683", "684", "685"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\refunding_reservation\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\fonts.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\services\\NotificationService.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx", ["686", "687", "688"], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx", ["689", "690"], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\reducer.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\actions.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\saga.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\factories.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\socket.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx", [], [], "E:\\WDP301_UROOM\\Customer\\src\\utils\\apiConfig.js", [], [], "E:\\WDP301_UROOM\\Customer\\src\\redux\\promotion\\promotionSlice.js", [], [], {"ruleId": "691", "severity": 1, "message": "692", "line": 3, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 3, "endColumn": 13}, {"ruleId": "695", "severity": 1, "message": "696", "line": 46, "column": 6, "nodeType": "697", "endLine": 46, "endColumn": 17, "suggestions": "698"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 103, "column": 61, "nodeType": "701", "messageId": "702", "endLine": 103, "endColumn": 76}, {"ruleId": "691", "severity": 1, "message": "703", "line": 33, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 33, "endColumn": 14}, {"ruleId": "691", "severity": 1, "message": "704", "line": 34, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 34, "endColumn": 14}, {"ruleId": "691", "severity": 1, "message": "705", "line": 35, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 35, "endColumn": 14}, {"ruleId": "691", "severity": 1, "message": "706", "line": 63, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 63, "endColumn": 19}, {"ruleId": "691", "severity": 1, "message": "692", "line": 64, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 64, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "707", "line": 87, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 87, "endColumn": 16}, {"ruleId": "695", "severity": 1, "message": "696", "line": 97, "column": 6, "nodeType": "697", "endLine": 97, "endColumn": 8, "suggestions": "708"}, {"ruleId": "691", "severity": 1, "message": "709", "line": 165, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 165, "endColumn": 16}, {"ruleId": "710", "severity": 1, "message": "711", "line": 638, "column": 24, "nodeType": "712", "messageId": "713", "endLine": 638, "endColumn": 26}, {"ruleId": "710", "severity": 1, "message": "714", "line": 665, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 665, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 675, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 675, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 685, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 685, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "711", "line": 907, "column": 29, "nodeType": "712", "messageId": "713", "endLine": 907, "endColumn": 31}, {"ruleId": "691", "severity": 1, "message": "715", "line": 1004, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 1004, "endColumn": 25}, {"ruleId": "710", "severity": 1, "message": "711", "line": 1203, "column": 39, "nodeType": "712", "messageId": "713", "endLine": 1203, "endColumn": 41}, {"ruleId": "716", "severity": 1, "message": "717", "line": 835, "column": 37, "nodeType": "701", "endLine": 842, "endColumn": 38}, {"ruleId": "691", "severity": 1, "message": "718", "line": 10, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 10, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "719", "line": 29, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 29, "endColumn": 19}, {"ruleId": "695", "severity": 1, "message": "720", "line": 273, "column": 6, "nodeType": "697", "endLine": 273, "endColumn": 77, "suggestions": "721"}, {"ruleId": "716", "severity": 1, "message": "717", "line": 731, "column": 21, "nodeType": "701", "endLine": 735, "endColumn": 22}, {"ruleId": "716", "severity": 1, "message": "717", "line": 769, "column": 23, "nodeType": "701", "endLine": 783, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "722", "line": 6, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 6, "endColumn": 8}, {"ruleId": "691", "severity": 1, "message": "723", "line": 159, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 159, "endColumn": 21}, {"ruleId": "691", "severity": 1, "message": "724", "line": 159, "column": 23, "nodeType": "693", "messageId": "694", "endLine": 159, "endColumn": 37}, {"ruleId": "695", "severity": 1, "message": "725", "line": 180, "column": 6, "nodeType": "697", "endLine": 180, "endColumn": 8, "suggestions": "726"}, {"ruleId": "695", "severity": 1, "message": "727", "line": 315, "column": 6, "nodeType": "697", "endLine": 315, "endColumn": 25, "suggestions": "728"}, {"ruleId": "691", "severity": 1, "message": "729", "line": 866, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 866, "endColumn": 15}, {"ruleId": "710", "severity": 1, "message": "711", "line": 959, "column": 28, "nodeType": "712", "messageId": "713", "endLine": 959, "endColumn": 30}, {"ruleId": "716", "severity": 1, "message": "717", "line": 1071, "column": 19, "nodeType": "701", "endLine": 1082, "endColumn": 20}, {"ruleId": "730", "severity": 1, "message": "731", "line": 1130, "column": 63, "nodeType": "732", "messageId": "733", "endLine": 1130, "endColumn": 65}, {"ruleId": "710", "severity": 1, "message": "711", "line": 1764, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 1764, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "714", "line": 2061, "column": 39, "nodeType": "712", "messageId": "713", "endLine": 2061, "endColumn": 41}, {"ruleId": "710", "severity": 1, "message": "711", "line": 2241, "column": 42, "nodeType": "712", "messageId": "713", "endLine": 2241, "endColumn": 44}, {"ruleId": "716", "severity": 1, "message": "717", "line": 2390, "column": 13, "nodeType": "701", "endLine": 2390, "endColumn": 41}, {"ruleId": "716", "severity": 1, "message": "717", "line": 2391, "column": 32, "nodeType": "701", "endLine": 2391, "endColumn": 60}, {"ruleId": "691", "severity": 1, "message": "734", "line": 1, "column": 17, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 26}, {"ruleId": "691", "severity": 1, "message": "735", "line": 10, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 10, "endColumn": 17}, {"ruleId": "695", "severity": 1, "message": "736", "line": 35, "column": 5, "nodeType": "697", "endLine": 35, "endColumn": 7, "suggestions": "737"}, {"ruleId": "695", "severity": 1, "message": "738", "line": 55, "column": 6, "nodeType": "697", "endLine": 55, "endColumn": 16, "suggestions": "739"}, {"ruleId": "695", "severity": 1, "message": "740", "line": 94, "column": 6, "nodeType": "697", "endLine": 94, "endColumn": 8, "suggestions": "741"}, {"ruleId": "695", "severity": 1, "message": "742", "line": 107, "column": 6, "nodeType": "697", "endLine": 107, "endColumn": 26, "suggestions": "743"}, {"ruleId": "744", "severity": 1, "message": "745", "line": 190, "column": 19, "nodeType": "701", "endLine": 190, "endColumn": 40}, {"ruleId": "691", "severity": 1, "message": "746", "line": 6, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 6, "endColumn": 21}, {"ruleId": "695", "severity": 1, "message": "747", "line": 35, "column": 6, "nodeType": "697", "endLine": 35, "endColumn": 31, "suggestions": "748"}, {"ruleId": "710", "severity": 1, "message": "714", "line": 650, "column": 38, "nodeType": "712", "messageId": "713", "endLine": 650, "endColumn": 40}, {"ruleId": "691", "severity": 1, "message": "749", "line": 667, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 667, "endColumn": 29}, {"ruleId": "710", "severity": 1, "message": "711", "line": 907, "column": 18, "nodeType": "712", "messageId": "713", "endLine": 907, "endColumn": 20}, {"ruleId": "691", "severity": 1, "message": "750", "line": 944, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 944, "endColumn": 19}, {"ruleId": "710", "severity": 1, "message": "714", "line": 1366, "column": 46, "nodeType": "712", "messageId": "713", "endLine": 1366, "endColumn": 48}, {"ruleId": "691", "severity": 1, "message": "751", "line": 35, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 35, "endColumn": 20}, {"ruleId": "695", "severity": 1, "message": "752", "line": 83, "column": 6, "nodeType": "697", "endLine": 83, "endColumn": 8, "suggestions": "753"}, {"ruleId": "695", "severity": 1, "message": "754", "line": 87, "column": 6, "nodeType": "697", "endLine": 87, "endColumn": 20, "suggestions": "755"}, {"ruleId": "710", "severity": 1, "message": "714", "line": 121, "column": 29, "nodeType": "712", "messageId": "713", "endLine": 121, "endColumn": 31}, {"ruleId": "695", "severity": 1, "message": "752", "line": 144, "column": 6, "nodeType": "697", "endLine": 144, "endColumn": 44, "suggestions": "756"}, {"ruleId": "710", "severity": 1, "message": "714", "line": 429, "column": 46, "nodeType": "712", "messageId": "713", "endLine": 429, "endColumn": 48}, {"ruleId": "710", "severity": 1, "message": "714", "line": 442, "column": 38, "nodeType": "712", "messageId": "713", "endLine": 442, "endColumn": 40}, {"ruleId": "691", "severity": 1, "message": "757", "line": 22, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 22, "endColumn": 13}, {"ruleId": "716", "severity": 1, "message": "717", "line": 249, "column": 29, "nodeType": "701", "endLine": 253, "endColumn": 30}, {"ruleId": "716", "severity": 1, "message": "717", "line": 258, "column": 29, "nodeType": "701", "endLine": 266, "endColumn": 30}, {"ruleId": "691", "severity": 1, "message": "758", "line": 4, "column": 29, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 40}, {"ruleId": "716", "severity": 1, "message": "717", "line": 243, "column": 17, "nodeType": "701", "endLine": 247, "endColumn": 52}, {"ruleId": "691", "severity": 1, "message": "758", "line": 4, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 21}, {"ruleId": "691", "severity": 1, "message": "759", "line": 10, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 10, "endColumn": 19}, {"ruleId": "691", "severity": 1, "message": "760", "line": 12, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 12, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "761", "line": 15, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 15, "endColumn": 17}, {"ruleId": "691", "severity": 1, "message": "762", "line": 63, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 63, "endColumn": 33}, {"ruleId": "691", "severity": 1, "message": "758", "line": 4, "column": 29, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 40}, {"ruleId": "716", "severity": 1, "message": "717", "line": 215, "column": 17, "nodeType": "701", "endLine": 219, "endColumn": 52}, {"ruleId": "691", "severity": 1, "message": "758", "line": 4, "column": 29, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 40}, {"ruleId": "691", "severity": 1, "message": "722", "line": 6, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 6, "endColumn": 15}, {"ruleId": "691", "severity": 1, "message": "760", "line": 9, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 9, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "758", "line": 3, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 3, "endColumn": 21}, {"ruleId": "691", "severity": 1, "message": "760", "line": 10, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 10, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "763", "line": 22, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 22, "endColumn": 19}, {"ruleId": "691", "severity": 1, "message": "764", "line": 27, "column": 17, "nodeType": "693", "messageId": "694", "endLine": 27, "endColumn": 25}, {"ruleId": "716", "severity": 1, "message": "765", "line": 203, "column": 17, "nodeType": "701", "endLine": 203, "endColumn": 89}, {"ruleId": "691", "severity": 1, "message": "734", "line": 1, "column": 17, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 26}, {"ruleId": "691", "severity": 1, "message": "766", "line": 1, "column": 28, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 36}, {"ruleId": "691", "severity": 1, "message": "746", "line": 25, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 25, "endColumn": 21}, {"ruleId": "767", "severity": 1, "message": "768", "line": 71, "column": 21, "nodeType": "701", "endLine": 83, "endColumn": 23}, {"ruleId": "710", "severity": 1, "message": "714", "line": 96, "column": 39, "nodeType": "712", "messageId": "713", "endLine": 96, "endColumn": 41}, {"ruleId": "710", "severity": 1, "message": "714", "line": 114, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 114, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 115, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 115, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 117, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 117, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 118, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 118, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 119, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 119, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 120, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 120, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 121, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 121, "endColumn": 28}, {"ruleId": "710", "severity": 1, "message": "714", "line": 122, "column": 26, "nodeType": "712", "messageId": "713", "endLine": 122, "endColumn": 28}, {"ruleId": "691", "severity": 1, "message": "769", "line": 19, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 19, "endColumn": 10}, {"ruleId": "691", "severity": 1, "message": "770", "line": 20, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 20, "endColumn": 13}, {"ruleId": "691", "severity": 1, "message": "771", "line": 52, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 52, "endColumn": 20}, {"ruleId": "691", "severity": 1, "message": "772", "line": 59, "column": 15, "nodeType": "693", "messageId": "694", "endLine": 59, "endColumn": 21}, {"ruleId": "691", "severity": 1, "message": "773", "line": 60, "column": 15, "nodeType": "693", "messageId": "694", "endLine": 60, "endColumn": 25}, {"ruleId": "695", "severity": 1, "message": "774", "line": 75, "column": 6, "nodeType": "697", "endLine": 75, "endColumn": 10, "suggestions": "775"}, {"ruleId": "695", "severity": 1, "message": "776", "line": 84, "column": 6, "nodeType": "697", "endLine": 84, "endColumn": 25, "suggestions": "777"}, {"ruleId": "691", "severity": 1, "message": "778", "line": 39, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 39, "endColumn": 26}, {"ruleId": "691", "severity": 1, "message": "779", "line": 39, "column": 28, "nodeType": "693", "messageId": "694", "endLine": 39, "endColumn": 47}, {"ruleId": "695", "severity": 1, "message": "774", "line": 235, "column": 6, "nodeType": "697", "endLine": 235, "endColumn": 21, "suggestions": "780"}, {"ruleId": "695", "severity": 1, "message": "781", "line": 277, "column": 6, "nodeType": "697", "endLine": 277, "endColumn": 49, "suggestions": "782"}, {"ruleId": "691", "severity": 1, "message": "783", "line": 78, "column": 27, "nodeType": "693", "messageId": "694", "endLine": 78, "endColumn": 45}, {"ruleId": "716", "severity": 1, "message": "765", "line": 32, "column": 15, "nodeType": "701", "endLine": 52, "endColumn": 16}, {"ruleId": "710", "severity": 1, "message": "711", "line": 35, "column": 33, "nodeType": "712", "messageId": "713", "endLine": 35, "endColumn": 35}, {"ruleId": "716", "severity": 1, "message": "765", "line": 55, "column": 15, "nodeType": "701", "endLine": 55, "endColumn": 27}, {"ruleId": "691", "severity": 1, "message": "784", "line": 17, "column": 66, "nodeType": "693", "messageId": "694", "endLine": 17, "endColumn": 72}, {"ruleId": "691", "severity": 1, "message": "785", "line": 34, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 34, "endColumn": 16}, {"ruleId": "691", "severity": 1, "message": "786", "line": 34, "column": 18, "nodeType": "693", "messageId": "694", "endLine": 34, "endColumn": 27}, {"ruleId": "710", "severity": 1, "message": "711", "line": 256, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 256, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "711", "line": 268, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 268, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "711", "line": 280, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 280, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "711", "line": 292, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 292, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "711", "line": 304, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 304, "endColumn": 34}, {"ruleId": "710", "severity": 1, "message": "711", "line": 316, "column": 32, "nodeType": "712", "messageId": "713", "endLine": 316, "endColumn": 34}, {"ruleId": "716", "severity": 1, "message": "717", "line": 479, "column": 21, "nodeType": "701", "endLine": 487, "endColumn": 22}, {"ruleId": "710", "severity": 1, "message": "711", "line": 492, "column": 42, "nodeType": "712", "messageId": "713", "endLine": 492, "endColumn": 44}, {"ruleId": "710", "severity": 1, "message": "711", "line": 492, "column": 68, "nodeType": "712", "messageId": "713", "endLine": 492, "endColumn": 70}, {"ruleId": "695", "severity": 1, "message": "787", "line": 47, "column": 6, "nodeType": "697", "endLine": 47, "endColumn": 8, "suggestions": "788"}, {"ruleId": "691", "severity": 1, "message": "789", "line": 8, "column": 40, "nodeType": "693", "messageId": "694", "endLine": 8, "endColumn": 48}, {"ruleId": "691", "severity": 1, "message": "790", "line": 8, "column": 50, "nodeType": "693", "messageId": "694", "endLine": 8, "endColumn": 57}, {"ruleId": "691", "severity": 1, "message": "791", "line": 1, "column": 14, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 18}, {"ruleId": "691", "severity": 1, "message": "792", "line": 1, "column": 20, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 24}, {"ruleId": "691", "severity": 1, "message": "793", "line": 1, "column": 26, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 29}, {"ruleId": "691", "severity": 1, "message": "794", "line": 1, "column": 31, "nodeType": "693", "messageId": "694", "endLine": 1, "endColumn": 40}, {"ruleId": "691", "severity": 1, "message": "759", "line": 2, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 2, "endColumn": 19}, {"ruleId": "691", "severity": 1, "message": "735", "line": 3, "column": 8, "nodeType": "693", "messageId": "694", "endLine": 3, "endColumn": 17}, {"ruleId": "691", "severity": 1, "message": "790", "line": 72, "column": 44, "nodeType": "693", "messageId": "694", "endLine": 72, "endColumn": 51}, {"ruleId": "695", "severity": 1, "message": "795", "line": 101, "column": 6, "nodeType": "697", "endLine": 101, "endColumn": 12, "suggestions": "796"}, {"ruleId": "695", "severity": 1, "message": "797", "line": 136, "column": 6, "nodeType": "697", "endLine": 136, "endColumn": 46, "suggestions": "798"}, {"ruleId": "691", "severity": 1, "message": "799", "line": 2, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 2, "endColumn": 12}, {"ruleId": "691", "severity": 1, "message": "718", "line": 8, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 8, "endColumn": 13}, {"ruleId": "710", "severity": 1, "message": "714", "line": 48, "column": 18, "nodeType": "712", "messageId": "713", "endLine": 48, "endColumn": 20}, {"ruleId": "691", "severity": 1, "message": "800", "line": 52, "column": 24, "nodeType": "693", "messageId": "694", "endLine": 52, "endColumn": 39}, {"ruleId": "695", "severity": 1, "message": "801", "line": 128, "column": 6, "nodeType": "697", "endLine": 128, "endColumn": 16, "suggestions": "802"}, {"ruleId": "695", "severity": 1, "message": "803", "line": 216, "column": 6, "nodeType": "697", "endLine": 216, "endColumn": 46, "suggestions": "804"}, {"ruleId": "695", "severity": 1, "message": "805", "line": 265, "column": 6, "nodeType": "697", "endLine": 265, "endColumn": 32, "suggestions": "806"}, {"ruleId": "807", "severity": 1, "message": "808", "line": 306, "column": 12, "nodeType": "693", "messageId": "809", "endLine": 306, "endColumn": 25}, {"ruleId": "691", "severity": 1, "message": "810", "line": 332, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 332, "endColumn": 30}, {"ruleId": "710", "severity": 1, "message": "714", "line": 574, "column": 37, "nodeType": "712", "messageId": "713", "endLine": 574, "endColumn": 39}, {"ruleId": "695", "severity": 1, "message": "811", "line": 93, "column": 6, "nodeType": "697", "endLine": 93, "endColumn": 40, "suggestions": "812"}, {"ruleId": "695", "severity": 1, "message": "813", "line": 98, "column": 6, "nodeType": "697", "endLine": 98, "endColumn": 40, "suggestions": "814"}, {"ruleId": "695", "severity": 1, "message": "815", "line": 184, "column": 6, "nodeType": "697", "endLine": 184, "endColumn": 41, "suggestions": "816"}, {"ruleId": "691", "severity": 1, "message": "817", "line": 372, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 372, "endColumn": 19}, {"ruleId": "691", "severity": 1, "message": "734", "line": 2, "column": 17, "nodeType": "693", "messageId": "694", "endLine": 2, "endColumn": 26}, {"ruleId": "710", "severity": 1, "message": "711", "line": 67, "column": 31, "nodeType": "712", "messageId": "713", "endLine": 67, "endColumn": 33}, {"ruleId": "710", "severity": 1, "message": "711", "line": 141, "column": 33, "nodeType": "712", "messageId": "713", "endLine": 141, "endColumn": 35}, {"ruleId": "691", "severity": 1, "message": "818", "line": 17, "column": 26, "nodeType": "693", "messageId": "694", "endLine": 17, "endColumn": 27}, {"ruleId": "691", "severity": 1, "message": "819", "line": 17, "column": 29, "nodeType": "693", "messageId": "694", "endLine": 17, "endColumn": 35}, {"ruleId": "691", "severity": 1, "message": "820", "line": 17, "column": 37, "nodeType": "693", "messageId": "694", "endLine": 17, "endColumn": 42}, {"ruleId": "695", "severity": 1, "message": "821", "line": 36, "column": 6, "nodeType": "697", "endLine": 36, "endColumn": 16, "suggestions": "822"}, {"ruleId": "710", "severity": 1, "message": "714", "line": 226, "column": 33, "nodeType": "712", "messageId": "713", "endLine": 226, "endColumn": 35}, {"ruleId": "691", "severity": 1, "message": "823", "line": 2, "column": 52, "nodeType": "693", "messageId": "694", "endLine": 2, "endColumn": 57}, {"ruleId": "691", "severity": 1, "message": "824", "line": 20, "column": 9, "nodeType": "693", "messageId": "694", "endLine": 20, "endColumn": 13}, {"ruleId": "695", "severity": 1, "message": "825", "line": 67, "column": 6, "nodeType": "697", "endLine": 67, "endColumn": 30, "suggestions": "826"}, {"ruleId": "710", "severity": 1, "message": "714", "line": 353, "column": 36, "nodeType": "712", "messageId": "713", "endLine": 353, "endColumn": 38}, {"ruleId": "710", "severity": 1, "message": "714", "line": 361, "column": 36, "nodeType": "712", "messageId": "713", "endLine": 361, "endColumn": 38}, {"ruleId": "710", "severity": 1, "message": "711", "line": 12, "column": 33, "nodeType": "712", "messageId": "713", "endLine": 12, "endColumn": 35}, {"ruleId": "710", "severity": 1, "message": "711", "line": 15, "column": 37, "nodeType": "712", "messageId": "713", "endLine": 15, "endColumn": 39}, {"ruleId": "710", "severity": 1, "message": "711", "line": 18, "column": 33, "nodeType": "712", "messageId": "713", "endLine": 18, "endColumn": 35}, {"ruleId": "827", "severity": 1, "message": "828", "line": 10, "column": 3, "nodeType": "829", "messageId": "713", "endLine": 10, "endColumn": 17}, {"ruleId": "827", "severity": 1, "message": "830", "line": 11, "column": 3, "nodeType": "829", "messageId": "713", "endLine": 11, "endColumn": 18}, {"ruleId": "827", "severity": 1, "message": "831", "line": 48, "column": 3, "nodeType": "829", "messageId": "713", "endLine": 48, "endColumn": 28}, {"ruleId": "832", "severity": 1, "message": "833", "line": 37, "column": 1, "nodeType": "834", "endLine": 108, "endColumn": 3}, {"ruleId": "691", "severity": 1, "message": "835", "line": 4, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 14}, {"ruleId": "691", "severity": 1, "message": "836", "line": 4, "column": 23, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 26}, {"ruleId": "691", "severity": 1, "message": "837", "line": 4, "column": 28, "nodeType": "693", "messageId": "694", "endLine": 4, "endColumn": 31}, {"ruleId": "695", "severity": 1, "message": "838", "line": 165, "column": 6, "nodeType": "697", "endLine": 165, "endColumn": 8, "suggestions": "839"}, {"ruleId": "691", "severity": 1, "message": "840", "line": 13, "column": 10, "nodeType": "693", "messageId": "694", "endLine": 13, "endColumn": 27}, {"ruleId": "691", "severity": 1, "message": "841", "line": 13, "column": 29, "nodeType": "693", "messageId": "694", "endLine": 13, "endColumn": 49}, {"ruleId": "695", "severity": 1, "message": "842", "line": 26, "column": 6, "nodeType": "697", "endLine": 26, "endColumn": 24, "suggestions": "843"}, {"ruleId": "695", "severity": 1, "message": "844", "line": 21, "column": 9, "nodeType": "845", "endLine": 21, "endColumn": 69}, {"ruleId": "695", "severity": 1, "message": "846", "line": 187, "column": 6, "nodeType": "697", "endLine": 187, "endColumn": 57, "suggestions": "847"}, "no-unused-vars", "'Utils' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["848"], "react/jsx-pascal-case", "Imported JSX component Home_detail must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "'image4' is defined but never used.", "'image5' is defined but never used.", "'image6' is defined but never used.", "'RoomActions' is defined but never used.", "'hotels' is assigned a value but never used.", ["849"], "'errors' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "'CustomerReviews' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'showToast' is defined but never used.", "React Hook useEffect has a missing dependency: 'API_BASE_URL'. Either include it or remove the dependency array.", ["850"], "'Route' is defined but never used.", "'isSearching' is assigned a value but never used.", "'setIsSearching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleBackToBooking'. Either include it or remove the dependency array.", ["851"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["852"], "'status' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'useEffect' is defined but never used.", "'Factories' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReservation'. Either include it or remove the dependency array.", ["853"], "React Hook useEffect has missing dependencies: 'createdAt', 'idReservation', 'messageError', 'messageSuccess', 'navigate', and 'totalPrice'. Either include them or remove the dependency array.", ["854"], "React Hook useEffect has a missing dependency: 'handleAccept'. Either include it or remove the dependency array.", ["855"], "React Hook useEffect has a missing dependency: 'handleDelete'. Either include it or remove the dependency array.", ["856"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'useLocation' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleConfirm'. Either include it or remove the dependency array.", ["857"], "'currentServiceIndex' is assigned a value but never used.", "'totalPrice' is assigned a value but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["858"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["859"], ["860"], "'User' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'useState' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'FaPhone' is defined but never used.", "'FaEnvelope' is defined but never used.", "'fontLoaded' is assigned a value but never used.", "'pdfLib' is assigned a value but never used.", "'pdfFontLib' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReservationDetail'. Either include it or remove the dependency array.", ["861"], "React Hook useEffect has a missing dependency: 'fetchHotelDetails'. Either include it or remove the dependency array.", ["862"], "'userReservations' is assigned a value but never used.", "'setUserReservations' is assigned a value but never used.", ["863"], "React Hook useEffect has missing dependencies: 'dispatch' and 'fetchHotelDetails'. Either include them or remove the dependency array.", ["864"], "'setSearchParamsObj' is assigned a value but never used.", "'FaUser' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addressMap'. Either include it or remove the dependency array.", ["865"], "'onFailed' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'call' is defined but never used.", "'fork' is defined but never used.", "'put' is defined but never used.", "'takeEvery' is defined but never used.", "React Hook useEffect has a missing dependency: 'activePage'. Either include it or remove the dependency array.", ["866"], "React Hook useEffect has missing dependencies: 'activePage' and 'updateURL'. Either include them or remove the dependency array.", ["867"], "'Container' is defined but never used.", "'setItemsPerPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReservations'. Either include it or remove the dependency array.", ["868"], "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["869"], "React Hook useEffect has missing dependencies: 'activePage' and 'totalPages'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setActivePage' needs the current value of 'totalPages'.", ["870"], "no-redeclare", "'parseCurrency' is already defined.", "redeclared", "'calculateRefundPolicy' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activePage', 'sortOption', and 'starFilter'. Either include them or remove the dependency array.", ["871"], "React Hook useEffect has a missing dependency: 'fetchUserFeedbacks'. Either include it or remove the dependency array.", ["872"], "React Hook useEffect has missing dependencies: 'activePage', 'getFilteredFeedbacks', and 'updateURL'. Either include them or remove the dependency array.", ["873"], "'formatDate' is assigned a value but never used.", "'X' is defined but never used.", "'Pencil' is defined but never used.", "'Trash' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["874"], "'Toast' is defined but never used.", "'Auth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["875"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Form' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "React Hook useEffect has missing dependencies: 'calculateRefundPolicy' and 'setRefundAmount'. Either include them or remove the dependency array. If 'setRefundAmount' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["876"], "'selectedPromotion' is assigned a value but never used.", "'setSelectedPromotion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["877"], "The 'safePromotions' conditional could make the dependencies of useEffect Hook (at line 223) change on every render. To fix this, wrap the initialization of 'safePromotions' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'getPromotionStatus'. Either include it or remove the dependency array.", ["878"], {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"desc": "915", "fix": "916"}, {"desc": "917", "fix": "918"}, {"desc": "919", "fix": "920"}, {"desc": "921", "fix": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"desc": "937", "fix": "938"}, {"desc": "939", "fix": "940"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [dispatch]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, API_BASE_URL]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [handleBackToBooking]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams]", {"range": "949", "text": "950"}, "Update the dependencies array to be: [fetchReservation]", {"range": "951", "text": "952"}, "Update the dependencies array to be: [createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", {"range": "953", "text": "954"}, "Update the dependencies array to be: [handleAccept]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [timeLeft, navigate, handleDelete]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [reservationId, navigate, handleConfirm]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [fetchReservationDetail, id]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [fetchHotelDetails, reservationDetail]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [fetchReservationDetail, reservationId]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [addressMap]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [activePage, page]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [dispatch, fetchUserReservations]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [activeFilter, reservations, dateFilter, filters]", {"range": "983", "text": "984"}, "Update the dependencies array to be: [activePage, filterBill, itemsPerPage, totalPages]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [dispatch, fetchUserFeedbacks, sortOption, starFilter]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [dispatch, activeStatus, fetchData]", {"range": "995", "text": "996"}, "Update the dependencies array to be: [calculateRefundPolicy, setRefundAmount]", {"range": "997", "text": "998"}, "Update the dependencies array to be: [fetchPromotions, show, totalPrice]", {"range": "999", "text": "1000"}, "Update the dependencies array to be: [safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]", {"range": "1001", "text": "1002"}, [2163, 2174], "[Auth?._id, dispatch]", [3226, 3228], "[dispatch]", [9861, 9932], "[dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, API_BASE_URL]", [6200, 6202], "[handleBackToBooking]", [9936, 9955], "[hotelId, dispatch, searchParams]", [1405, 1407], "[fetchReservation]", [1841, 1851], "[createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", [3122, 3124], "[handleAccept]", [3447, 3467], "[timeLeft, navigate, handleDelete]", [1197, 1222], "[reservationId, navigate, handleConfirm]", [2913, 2915], "[fetchAllUser]", [2972, 2986], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4575, 4613], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [2124, 2128], "[fetchReservationDetail, id]", [2328, 2347], "[fetchHotelDetails, reservationDetail]", [7500, 7515], "[fetchReservationDetail, reservationId]", [9032, 9075], "[Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", [1416, 1418], "[addressMap]", [3659, 3665], "[activePage, page]", [4664, 4704], "[dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", [4815, 4825], "[dispatch, fetchUserReservations]", [7810, 7850], "[activeFilter, reservations, dateFilter, filters]", [9284, 9310], "[activePage, filterBill, itemsPerPage, totalPages]", [3136, 3170], "[activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", [3276, 3310], "[dispatch, fetchUserFeedbacks, sortOption, starFilter]", [6005, 6040], "[feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", [1363, 1373], "[Auth._id, fetchUserReports]", [2318, 2342], "[dispatch, activeStatus, fetchData]", [5035, 5037], "[calculateRefundPolicy, setRefundAmount]", [1188, 1206], "[fetchPromotions, show, totalPrice]", [7357, 7408], "[safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]"]