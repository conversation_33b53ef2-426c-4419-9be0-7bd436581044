import React, { useState, useEffect } from 'react';
import {
  Container,
  Row,
  Col,
  Card,
  Table,
  Button,
  Form,
  Modal,
  Alert,
  Badge,
  InputGroup,
  Spinner
} from 'react-bootstrap';
import {
  FaPlus,
  FaSearch,
  FaUserTag,
  FaGift,
  FaUsers,
  FaCalendar,
  FaChartLine
} from 'react-icons/fa';
import axios from 'axios';

const UserPromotionManagement = () => {
  const [assignments, setAssignments] = useState([]);
  const [promotions, setPromotions] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form states
  const [assignForm, setAssignForm] = useState({
    userId: '',
    promotionId: '',
    assignmentType: 'ASSIGNED',
    maxUsage: 1,
    customExpiryDate: '',
    metadata: {
      assignmentReason: 'MANUAL_ASSIGNMENT'
    }
  });

  const [bulkForm, setBulkForm] = useState({
    userIds: '',
    promotionId: '',
    assignmentType: 'CAMPAIGN',
    maxUsage: 1,
    metadata: {
      assignmentReason: 'CAMPAIGN_TARGET'
    }
  });

  const [filters, setFilters] = useState({
    userId: '',
    promotionId: '',
    status: '',
    assignmentType: ''
  });

  useEffect(() => {
    fetchAssignments();
    fetchPromotions();
    fetchUsers();
  }, []);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/user-promotions/all', {
        params: filters
      });
      setAssignments(response.data.assignments);
    } catch (error) {
      setError('Failed to fetch assignments');
    } finally {
      setLoading(false);
    }
  };

  const fetchPromotions = async () => {
    try {
      const response = await axios.get('/api/promotions');
      setPromotions(response.data.promotions || []);
    } catch (error) {
      console.error('Failed to fetch promotions:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      // Assuming you have a users endpoint
      const response = await axios.get('/api/users');
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };

  const handleAssignPromotion = async () => {
    try {
      setLoading(true);
      await axios.post('/api/user-promotions/assign', assignForm);
      setSuccess('Promotion assigned successfully');
      setShowAssignModal(false);
      setAssignForm({
        userId: '',
        promotionId: '',
        assignmentType: 'ASSIGNED',
        maxUsage: 1,
        customExpiryDate: '',
        metadata: { assignmentReason: 'MANUAL_ASSIGNMENT' }
      });
      fetchAssignments();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to assign promotion');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkAssign = async () => {
    try {
      setLoading(true);
      const userIds = bulkForm.userIds.split(',').map(id => id.trim()).filter(id => id);
      
      await axios.post('/api/user-promotions/bulk-assign', {
        ...bulkForm,
        userIds
      });
      
      setSuccess(`Promotion assigned to ${userIds.length} users`);
      setShowBulkModal(false);
      setBulkForm({
        userIds: '',
        promotionId: '',
        assignmentType: 'CAMPAIGN',
        maxUsage: 1,
        metadata: { assignmentReason: 'CAMPAIGN_TARGET' }
      });
      fetchAssignments();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to bulk assign promotion');
    } finally {
      setLoading(false);
    }
  };

  const handleRevokePromotion = async (userId, promotionId) => {
    if (!window.confirm('Are you sure you want to revoke this promotion?')) return;

    try {
      await axios.post('/api/user-promotions/revoke', { userId, promotionId });
      setSuccess('Promotion revoked successfully');
      fetchAssignments();
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to revoke promotion');
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      ASSIGNED: 'success',
      USED: 'secondary',
      EXPIRED: 'warning',
      REVOKED: 'danger'
    };
    return <Badge bg={variants[status] || 'secondary'}>{status}</Badge>;
  };

  const getAssignmentTypeBadge = (type) => {
    const variants = {
      PUBLIC: 'info',
      ASSIGNED: 'primary',
      EARNED: 'success',
      CAMPAIGN: 'warning',
      LOYALTY_REWARD: 'success'
    };
    return <Badge bg={variants[type] || 'secondary'}>{type}</Badge>;
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <FaUserTag className="me-2" />
            User Promotion Management
          </h2>
        </Col>
        <Col xs="auto">
          <Button
            variant="primary"
            onClick={() => setShowAssignModal(true)}
            className="me-2"
          >
            <FaPlus className="me-1" />
            Assign Promotion
          </Button>
          <Button
            variant="success"
            onClick={() => setShowBulkModal(true)}
          >
            <FaUsers className="me-1" />
            Bulk Assign
          </Button>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-4">
        <Card.Header>
          <h5>Filters</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={3}>
              <Form.Group>
                <Form.Label>User ID</Form.Label>
                <Form.Control
                  type="text"
                  value={filters.userId}
                  onChange={(e) => setFilters({...filters, userId: e.target.value})}
                  placeholder="Enter user ID"
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Promotion</Form.Label>
                <Form.Select
                  value={filters.promotionId}
                  onChange={(e) => setFilters({...filters, promotionId: e.target.value})}
                >
                  <option value="">All Promotions</option>
                  {promotions.map(promo => (
                    <option key={promo._id} value={promo._id}>
                      {promo.code} - {promo.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Status</Form.Label>
                <Form.Select
                  value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                >
                  <option value="">All Status</option>
                  <option value="ASSIGNED">Assigned</option>
                  <option value="USED">Used</option>
                  <option value="EXPIRED">Expired</option>
                  <option value="REVOKED">Revoked</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label>Type</Form.Label>
                <Form.Select
                  value={filters.assignmentType}
                  onChange={(e) => setFilters({...filters, assignmentType: e.target.value})}
                >
                  <option value="">All Types</option>
                  <option value="PUBLIC">Public</option>
                  <option value="ASSIGNED">Assigned</option>
                  <option value="EARNED">Earned</option>
                  <option value="CAMPAIGN">Campaign</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2} className="d-flex align-items-end">
              <Button variant="outline-primary" onClick={fetchAssignments}>
                <FaSearch className="me-1" />
                Search
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Assignments Table */}
      <Card>
        <Card.Header>
          <h5>Promotion Assignments</h5>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" />
            </div>
          ) : (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>User</th>
                  <th>Promotion</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Usage</th>
                  <th>Assigned Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {assignments.map(assignment => (
                  <tr key={assignment._id}>
                    <td>
                      <div>
                        <strong>{assignment.userId?.fullName || 'Unknown'}</strong>
                        <br />
                        <small className="text-muted">{assignment.userId?.email}</small>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>{assignment.promotionId?.code}</strong>
                        <br />
                        <small className="text-muted">{assignment.promotionId?.name}</small>
                      </div>
                    </td>
                    <td>{getAssignmentTypeBadge(assignment.assignmentType)}</td>
                    <td>{getStatusBadge(assignment.status)}</td>
                    <td>
                      <div>
                        {assignment.usageCount} / {assignment.maxUsage}
                        <br />
                        <small className="text-muted">
                          {Math.round((assignment.usageCount / assignment.maxUsage) * 100)}%
                        </small>
                      </div>
                    </td>
                    <td>
                      {new Date(assignment.assignedAt).toLocaleDateString()}
                    </td>
                    <td>
                      {assignment.status === 'ASSIGNED' && (
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRevokePromotion(
                            assignment.userId._id,
                            assignment.promotionId._id
                          )}
                        >
                          Revoke
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Assign Modal */}
      <Modal show={showAssignModal} onHide={() => setShowAssignModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Assign Promotion to User</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>User</Form.Label>
                  <Form.Select
                    value={assignForm.userId}
                    onChange={(e) => setAssignForm({...assignForm, userId: e.target.value})}
                  >
                    <option value="">Select User</option>
                    {users.map(user => (
                      <option key={user._id} value={user._id}>
                        {user.fullName} ({user.email})
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Promotion</Form.Label>
                  <Form.Select
                    value={assignForm.promotionId}
                    onChange={(e) => setAssignForm({...assignForm, promotionId: e.target.value})}
                  >
                    <option value="">Select Promotion</option>
                    {promotions.map(promo => (
                      <option key={promo._id} value={promo._id}>
                        {promo.code} - {promo.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Assignment Type</Form.Label>
                  <Form.Select
                    value={assignForm.assignmentType}
                    onChange={(e) => setAssignForm({...assignForm, assignmentType: e.target.value})}
                  >
                    <option value="ASSIGNED">Assigned</option>
                    <option value="EARNED">Earned</option>
                    <option value="LOYALTY_REWARD">Loyalty Reward</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Max Usage</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    value={assignForm.maxUsage}
                    onChange={(e) => setAssignForm({...assignForm, maxUsage: parseInt(e.target.value)})}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Custom Expiry Date (Optional)</Form.Label>
              <Form.Control
                type="datetime-local"
                value={assignForm.customExpiryDate}
                onChange={(e) => setAssignForm({...assignForm, customExpiryDate: e.target.value})}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAssignModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleAssignPromotion}
            disabled={!assignForm.userId || !assignForm.promotionId}
          >
            Assign Promotion
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Bulk Assign Modal */}
      <Modal show={showBulkModal} onHide={() => setShowBulkModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Bulk Assign Promotion</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>User IDs (comma separated)</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={bulkForm.userIds}
                onChange={(e) => setBulkForm({...bulkForm, userIds: e.target.value})}
                placeholder="1, 2, 3, 4, 5..."
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Promotion</Form.Label>
              <Form.Select
                value={bulkForm.promotionId}
                onChange={(e) => setBulkForm({...bulkForm, promotionId: e.target.value})}
              >
                <option value="">Select Promotion</option>
                {promotions.map(promo => (
                  <option key={promo._id} value={promo._id}>
                    {promo.code} - {promo.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Assignment Type</Form.Label>
                  <Form.Select
                    value={bulkForm.assignmentType}
                    onChange={(e) => setBulkForm({...bulkForm, assignmentType: e.target.value})}
                  >
                    <option value="CAMPAIGN">Campaign</option>
                    <option value="LOYALTY_REWARD">Loyalty Reward</option>
                    <option value="ASSIGNED">Manual Assignment</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Max Usage per User</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    value={bulkForm.maxUsage}
                    onChange={(e) => setBulkForm({...bulkForm, maxUsage: parseInt(e.target.value)})}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowBulkModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="success" 
            onClick={handleBulkAssign}
            disabled={!bulkForm.userIds || !bulkForm.promotionId}
          >
            Bulk Assign
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default UserPromotionManagement;
