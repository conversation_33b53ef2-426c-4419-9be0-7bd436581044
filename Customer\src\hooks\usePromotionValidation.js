import { useCallback, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { debounce } from 'lodash';
import {
  validatePromotion,
  clearPromotion,
  setError,
  updateLastValidatedAmount,
  checkExpiration,
  selectAppliedPromotion,
  selectLastValidatedAmount,
  selectPromotionValidating,
  selectIsPromotionExpired,
} from '../redux/promotion/promotionSlice';

// Configuration constants
const VALIDATION_CONFIG = {
  DEBOUNCE_DELAY: 500, // ms
  SIGNIFICANT_CHANGE_THRESHOLD: 0.05, // 5%
  MIN_AMOUNT_CHANGE: 10000, // 10k VND minimum change to trigger validation
  VALIDATION_COOLDOWN: 2000, // 2 seconds cooldown between validations
  EXPIRATION_CHECK_INTERVAL: 30000, // 30 seconds
};

export const usePromotionValidation = () => {
  const dispatch = useDispatch();
  const appliedPromotion = useSelector(selectAppliedPromotion);
  const lastValidatedAmount = useSelector(selectLastValidatedAmount);
  const isValidating = useSelector(selectPromotionValidating);
  const isExpired = useSelector(selectIsPromotionExpired);
  
  const lastValidationTime = useRef(0);
  const validationTimeoutRef = useRef(null);
  const expirationCheckRef = useRef(null);

  // Check if validation is needed based on amount change
  const shouldValidate = useCallback((newAmount) => {
    if (!appliedPromotion.code) return false;
    if (isValidating) return false;
    
    const now = Date.now();
    if (now - lastValidationTime.current < VALIDATION_CONFIG.VALIDATION_COOLDOWN) {
      return false;
    }
    
    const amountDiff = Math.abs(newAmount - lastValidatedAmount);
    const percentChange = lastValidatedAmount > 0 ? amountDiff / lastValidatedAmount : 1;
    
    return (
      amountDiff >= VALIDATION_CONFIG.MIN_AMOUNT_CHANGE ||
      percentChange >= VALIDATION_CONFIG.SIGNIFICANT_CHANGE_THRESHOLD
    );
  }, [appliedPromotion.code, lastValidatedAmount, isValidating]);

  // Debounced validation function
  const debouncedValidate = useCallback(
    debounce(async (amount) => {
      if (!appliedPromotion.code || !shouldValidate(amount)) return;
      
      try {
        lastValidationTime.current = Date.now();
        
        const result = await dispatch(validatePromotion({
          code: appliedPromotion.code,
          orderAmount: amount,
        })).unwrap();
        
        if (result.valid) {
          dispatch(updateLastValidatedAmount(amount));
        }
      } catch (error) {
        console.error('Promotion validation failed:', error);
        // Error is already handled in the slice
      }
    }, VALIDATION_CONFIG.DEBOUNCE_DELAY),
    [appliedPromotion.code, shouldValidate, dispatch]
  );

  // Main validation function
  const validatePromotionIfNeeded = useCallback((orderAmount) => {
    // Clear any pending validation
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
    
    // Check expiration first
    dispatch(checkExpiration());
    
    if (isExpired) {
      return;
    }
    
    // Schedule validation
    validationTimeoutRef.current = setTimeout(() => {
      debouncedValidate(orderAmount);
    }, 0);
  }, [debouncedValidate, dispatch, isExpired]);

  // Force validation (for critical moments like before booking)
  const forceValidation = useCallback(async (orderAmount) => {
    if (!appliedPromotion.code) {
      return { valid: true };
    }
    
    try {
      const result = await dispatch(validatePromotion({
        code: appliedPromotion.code,
        orderAmount,
      })).unwrap();
      
      if (result.valid) {
        dispatch(updateLastValidatedAmount(orderAmount));
      }
      
      return result;
    } catch (error) {
      return {
        valid: false,
        message: error || 'Validation failed',
      };
    }
  }, [appliedPromotion.code, dispatch]);

  // Clear promotion with cleanup
  const clearPromotionWithCleanup = useCallback(() => {
    // Clear any pending validations
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
    debouncedValidate.cancel();
    
    dispatch(clearPromotion());
  }, [dispatch, debouncedValidate]);

  // Set up expiration checking
  useEffect(() => {
    if (appliedPromotion.code && appliedPromotion.expiresAt) {
      expirationCheckRef.current = setInterval(() => {
        dispatch(checkExpiration());
      }, VALIDATION_CONFIG.EXPIRATION_CHECK_INTERVAL);
      
      return () => {
        if (expirationCheckRef.current) {
          clearInterval(expirationCheckRef.current);
        }
      };
    }
  }, [appliedPromotion.code, appliedPromotion.expiresAt, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
      if (expirationCheckRef.current) {
        clearInterval(expirationCheckRef.current);
      }
      debouncedValidate.cancel();
    };
  }, [debouncedValidate]);

  // Calculate time until expiration
  const getTimeUntilExpiration = useCallback(() => {
    if (!appliedPromotion.expiresAt) return null;
    
    const expirationTime = new Date(appliedPromotion.expiresAt).getTime();
    const now = Date.now();
    const timeLeft = expirationTime - now;
    
    if (timeLeft <= 0) return 0;
    
    return Math.floor(timeLeft / 1000); // seconds
  }, [appliedPromotion.expiresAt]);

  // Format time remaining for display
  const formatTimeRemaining = useCallback(() => {
    const seconds = getTimeUntilExpiration();
    if (seconds === null) return null;
    if (seconds <= 0) return 'Expired';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  }, [getTimeUntilExpiration]);

  // Check if promotion is about to expire (less than 2 minutes)
  const isAboutToExpire = useCallback(() => {
    const seconds = getTimeUntilExpiration();
    return seconds !== null && seconds > 0 && seconds < 120;
  }, [getTimeUntilExpiration]);

  return {
    // Validation functions
    validatePromotionIfNeeded,
    forceValidation,
    clearPromotionWithCleanup,
    
    // State
    isValidating,
    isExpired,
    isAboutToExpire: isAboutToExpire(),
    
    // Time utilities
    getTimeUntilExpiration,
    formatTimeRemaining,
    
    // Configuration
    config: VALIDATION_CONFIG,
  };
};

export default usePromotionValidation;
