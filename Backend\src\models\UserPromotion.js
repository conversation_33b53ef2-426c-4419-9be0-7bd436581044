const mongoose = require('mongoose');

const userPromotionSchema = new mongoose.Schema(
  {
    userId: {
      type: Number,
      ref: 'User',
      required: true,
    },
    promotionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Promotion',
      required: true,
    },
    assignmentType: {
      type: String,
      enum: ['PUBLIC', 'ASSIGNED', 'EARNED', 'CAMPAIGN', 'LOYALTY_REWARD'],
      default: 'PUBLIC',
    },
    assignedAt: {
      type: Date,
      default: Date.now,
    },
    assignedBy: {
      type: Number,
      ref: 'User', // Admin who assigned
      default: null,
    },
    status: {
      type: String,
      enum: ['ASSIGNED', 'USED', 'EXPIRED', 'REVOKED'],
      default: 'ASSIGNED',
    },
    usageCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    maxUsage: {
      type: Number,
      default: 1,
      min: 1,
    },
    customExpiryDate: {
      type: Date,
      default: null, // If null, use promotion's endDate
    },
    lastUsedAt: {
      type: Date,
      default: null,
    },
    // Assignment context
    metadata: {
      assignmentReason: {
        type: String,
        enum: [
          'WELCOME_BONUS',
          'LOYALTY_REWARD', 
          'BIRTHDAY_GIFT',
          'CAMPAIGN_TARGET',
          'MANUAL_ASSIGNMENT',
          'REFERRAL_BONUS',
          'MILESTONE_REWARD',
          'RETENTION_OFFER'
        ],
      },
      campaignId: String,
      userSegment: String,
      originalOrderAmount: Number, // For targeted offers
      triggerEvent: String,
    },
    // Notification tracking
    notificationSent: {
      type: Boolean,
      default: false,
    },
    notificationSentAt: {
      type: Date,
      default: null,
    },
  },
  { 
    versionKey: '__v',
    timestamps: true 
  }
);

// Compound indexes
userPromotionSchema.index({ userId: 1, status: 1 });
userPromotionSchema.index({ promotionId: 1, status: 1 });
userPromotionSchema.index({ userId: 1, promotionId: 1 }, { unique: true });
userPromotionSchema.index({ assignmentType: 1, status: 1 });
userPromotionSchema.index({ customExpiryDate: 1 }, { sparse: true });
userPromotionSchema.index({ 'metadata.campaignId': 1 }, { sparse: true });

// Virtual for checking if promotion is available
userPromotionSchema.virtual('isAvailable').get(function() {
  if (this.status !== 'ASSIGNED') return false;
  if (this.usageCount >= this.maxUsage) return false;
  
  const now = new Date();
  const expiryDate = this.customExpiryDate || this.promotionId?.endDate;
  if (expiryDate && now > expiryDate) return false;
  
  return true;
});

// Static method to assign promotion to user
userPromotionSchema.statics.assignToUser = async function(data) {
  const {
    userId,
    promotionId,
    assignmentType = 'ASSIGNED',
    assignedBy = null,
    maxUsage = 1,
    customExpiryDate = null,
    metadata = {}
  } = data;

  // Check if already assigned
  const existing = await this.findOne({ userId, promotionId });
  if (existing) {
    throw new Error('Promotion already assigned to this user');
  }

  const assignment = new this({
    userId,
    promotionId,
    assignmentType,
    assignedBy,
    maxUsage,
    customExpiryDate,
    metadata,
    status: 'ASSIGNED'
  });

  return await assignment.save();
};

// Static method to get user's available promotions
userPromotionSchema.statics.getUserAvailablePromotions = async function(userId, options = {}) {
  const { includePublic = true, status = 'ASSIGNED' } = options;
  
  let query = { userId, status };
  
  const assignments = await this.find(query)
    .populate({
      path: 'promotionId',
      match: { isActive: true },
      select: 'code name description discountType discountValue maxDiscountAmount minOrderAmount startDate endDate'
    })
    .sort({ assignedAt: -1 });

  // Filter out assignments where promotion was not found (inactive)
  const validAssignments = assignments.filter(assignment => assignment.promotionId);

  // Add public promotions if requested
  if (includePublic) {
    const Promotion = require('./Promotion');
    const publicPromotions = await Promotion.find({
      isActive: true,
      isPersonalized: false,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    });

    // Convert public promotions to assignment format
    const publicAssignments = publicPromotions.map(promotion => ({
      _id: `public_${promotion._id}`,
      userId,
      promotionId: promotion,
      assignmentType: 'PUBLIC',
      status: 'ASSIGNED',
      usageCount: 0,
      maxUsage: promotion.userUsageLimit || 1,
      assignedAt: promotion.startDate,
      isAvailable: true
    }));

    return [...validAssignments, ...publicAssignments];
  }

  return validAssignments;
};

// Static method to use promotion
userPromotionSchema.statics.usePromotion = async function(userId, promotionId, reservationId) {
  const assignment = await this.findOne({
    userId,
    promotionId,
    status: 'ASSIGNED'
  });

  if (!assignment) {
    throw new Error('Promotion not assigned to user or already used');
  }

  if (assignment.usageCount >= assignment.maxUsage) {
    throw new Error('Promotion usage limit exceeded');
  }

  // Check expiry
  const now = new Date();
  const expiryDate = assignment.customExpiryDate || assignment.promotionId?.endDate;
  if (expiryDate && now > expiryDate) {
    assignment.status = 'EXPIRED';
    await assignment.save();
    throw new Error('Promotion has expired');
  }

  // Increment usage
  assignment.usageCount += 1;
  assignment.lastUsedAt = now;
  
  // Mark as used if reached max usage
  if (assignment.usageCount >= assignment.maxUsage) {
    assignment.status = 'USED';
  }

  await assignment.save();
  return assignment;
};

// Static method for bulk assignment (campaigns)
userPromotionSchema.statics.bulkAssign = async function(userIds, promotionId, options = {}) {
  const {
    assignmentType = 'CAMPAIGN',
    assignedBy = null,
    maxUsage = 1,
    metadata = {}
  } = options;

  const assignments = userIds.map(userId => ({
    userId,
    promotionId,
    assignmentType,
    assignedBy,
    maxUsage,
    metadata,
    status: 'ASSIGNED'
  }));

  // Use insertMany with ordered: false to continue on duplicates
  try {
    const result = await this.insertMany(assignments, { ordered: false });
    return {
      success: result.length,
      total: userIds.length,
      failed: userIds.length - result.length
    };
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key errors
      const successCount = error.result?.result?.insertedCount || 0;
      return {
        success: successCount,
        total: userIds.length,
        failed: userIds.length - successCount,
        error: 'Some users already have this promotion assigned'
      };
    }
    throw error;
  }
};

// Static method to revoke promotion
userPromotionSchema.statics.revokePromotion = async function(userId, promotionId, revokedBy) {
  const assignment = await this.findOne({
    userId,
    promotionId,
    status: { $in: ['ASSIGNED', 'USED'] }
  });

  if (!assignment) {
    throw new Error('Promotion assignment not found');
  }

  assignment.status = 'REVOKED';
  assignment.metadata = {
    ...assignment.metadata,
    revokedBy,
    revokedAt: new Date()
  };

  await assignment.save();
  return assignment;
};

module.exports = mongoose.model('UserPromotion', userPromotionSchema);
