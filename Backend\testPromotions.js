const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const UserPromotion = require("./src/models/UserPromotion");
const User = require("./src/models/user");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function testPromotionScenarios() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB for testing");

    // Test 1: Get user promotions for different users
    console.log("\n🧪 Test 1: User Promotion Availability");
    console.log("=" .repeat(50));
    
    for (let userId = 1; userId <= 5; userId++) {
      console.log(`\n👤 User ${userId} available promotions:`);
      
      try {
        const userPromotions = await UserPromotion.getUserAvailablePromotions(userId, {
          includePublic: true,
          status: 'ASSIGNED'
        });
        
        if (userPromotions.length === 0) {
          console.log("  No promotions available");
        } else {
          userPromotions.forEach(assignment => {
            const promo = assignment.promotionId;
            const remaining = assignment.maxUsage - assignment.usageCount;
            const type = assignment.assignmentType || 'PUBLIC';
            
            console.log(`  - ${promo.code}: ${promo.name}`);
            console.log(`    Type: ${type}, Remaining: ${remaining}/${assignment.maxUsage}`);
            console.log(`    Discount: ${promo.discountType === 'PERCENTAGE' ? promo.discountValue + '%' : promo.discountValue + ' VND'}`);
          });
        }
      } catch (error) {
        console.log(`  Error: ${error.message}`);
      }
    }

    // Test 2: Apply promotion simulation
    console.log("\n\n🧪 Test 2: Promotion Application Simulation");
    console.log("=" .repeat(50));
    
    const testCases = [
      { userId: 2, code: "WELCOME15", orderAmount: 600000 },
      { userId: 2, code: "SUMMER25", orderAmount: 1200000 },
      { userId: 3, code: "LOYALTY30", orderAmount: 2000000 },
      { userId: 4, code: "BIRTHDAY50", orderAmount: 1500000 },
      { userId: 1, code: "WELCOME15", orderAmount: 600000 }, // Should fail - already used
      { userId: 2, code: "EXPIRED10", orderAmount: 500000 }, // Should fail - expired
      { userId: 2, code: "MAXEDOUT", orderAmount: 500000 }, // Should fail - used up
    ];

    for (const testCase of testCases) {
      console.log(`\n🎯 Testing: User ${testCase.userId} applying ${testCase.code} for ${testCase.orderAmount.toLocaleString()} VND`);
      
      try {
        const promotion = await Promotion.findOne({ code: testCase.code });
        if (!promotion) {
          console.log("  ❌ Promotion not found");
          continue;
        }

        const result = await Promotion.applyPromotionWithLock(
          promotion._id,
          testCase.userId,
          testCase.orderAmount
        );
        
        console.log(`  ✅ Success: ${result.discount.toLocaleString()} VND discount`);
        console.log(`  📝 Reservation ID: ${result.reservationId}`);
        console.log(`  ⏰ Expires: ${new Date(result.expiresAt).toLocaleTimeString()}`);
        
      } catch (error) {
        console.log(`  ❌ Failed: ${error.message}`);
      }
    }

    // Test 3: Check promotion statistics
    console.log("\n\n🧪 Test 3: Promotion Statistics");
    console.log("=" .repeat(50));
    
    const allPromotions = await Promotion.find({}).select('code name usedCount usageLimit isActive');
    
    console.log("\n📊 Promotion Usage Statistics:");
    allPromotions.forEach(promo => {
      const usagePercent = promo.usageLimit ? Math.round((promo.usedCount / promo.usageLimit) * 100) : 0;
      const status = promo.isActive ? '🟢 Active' : '🔴 Inactive';
      
      console.log(`  ${promo.code}: ${promo.usedCount}/${promo.usageLimit || '∞'} (${usagePercent}%) ${status}`);
    });

    // Test 4: User assignment statistics
    console.log("\n\n🧪 Test 4: User Assignment Statistics");
    console.log("=" .repeat(50));
    
    const assignmentStats = await UserPromotion.aggregate([
      {
        $group: {
          _id: '$assignmentType',
          count: { $sum: 1 },
          used: { $sum: { $cond: [{ $eq: ['$status', 'USED'] }, 1, 0] } },
          assigned: { $sum: { $cond: [{ $eq: ['$status', 'ASSIGNED'] }, 1, 0] } }
        }
      }
    ]);

    console.log("\n📈 Assignment Type Statistics:");
    assignmentStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} total (${stat.used} used, ${stat.assigned} assigned)`);
    });

    // Test 5: Cleanup expired reservations simulation
    console.log("\n\n🧪 Test 5: Cleanup Simulation");
    console.log("=" .repeat(50));
    
    const PromotionReservation = require("./src/models/PromotionReservation");
    
    const activeReservations = await PromotionReservation.countDocuments({
      status: 'RESERVED',
      expiresAt: { $gt: new Date() }
    });
    
    const expiredReservations = await PromotionReservation.countDocuments({
      status: 'RESERVED',
      expiresAt: { $lt: new Date() }
    });
    
    console.log(`\n🔄 Reservation Status:`);
    console.log(`  Active reservations: ${activeReservations}`);
    console.log(`  Expired reservations: ${expiredReservations}`);
    
    if (expiredReservations > 0) {
      console.log(`  💡 Run cleanup job to clean ${expiredReservations} expired reservations`);
    }

    console.log("\n\n🎉 All tests completed!");
    console.log("\n📝 Test Summary:");
    console.log("- User promotion availability ✅");
    console.log("- Promotion application logic ✅");
    console.log("- Usage statistics tracking ✅");
    console.log("- Assignment type analytics ✅");
    console.log("- Cleanup monitoring ✅");

    console.log("\n🚀 Ready for frontend testing!");
    console.log("\nSuggested test flows:");
    console.log("1. Login as User 1 - should see WELCOME15 as 'Used'");
    console.log("2. Login as User 2 - should see WELCOME15 + LOYALTY30 available");
    console.log("3. Login as User 3 - should see LOYALTY30 available");
    console.log("4. Login as User 4 - should see BIRTHDAY50 available");
    console.log("5. Login as User 5 - should see VIP100 with 1 remaining use");
    console.log("6. Try booking with different order amounts to test min order validation");
    console.log("7. Check My Account page to see all promotions with correct status");

    process.exit(0);
  } catch (error) {
    console.error("Error testing promotions:", error);
    process.exit(1);
  }
}

testPromotionScenarios();
