const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

async function testPromotionAPI() {
  console.log('🧪 Testing Promotion API');
  console.log('=' .repeat(50));

  // Test 1: Get user promotions
  console.log('\n📋 Test 1: Get User Promotions');
  try {
    const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {
      headers: {
        'Authorization': 'Bearer fake-token' // Mock auth
      }
    });
    console.log('✅ Success:', response.data.promotions?.length || 0, 'promotions found');
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 2: Apply public promotion
  console.log('\n🎯 Test 2: Apply Public Promotion (SUMMER15)');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'SUMMER15',
      orderAmount: 300,
      userId: 1
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 3: Apply private promotion (should fail for wrong user)
  console.log('\n🔒 Test 3: Apply Private Promotion (Wrong User)');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'VIP_USER2',
      orderAmount: 1200,
      userId: 1 // Wrong user
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  // Test 4: Apply private promotion (correct user)
  console.log('\n🔒 Test 4: Apply Private Promotion (Correct User)');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'WELCOME_USER3',
      orderAmount: 300,
      userId: 3 // Correct user
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 5: Apply invalid promotion
  console.log('\n❌ Test 5: Apply Invalid Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'INVALID123',
      orderAmount: 300,
      userId: 1
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  // Test 6: Apply promotion with insufficient order amount
  console.log('\n💰 Test 6: Apply Promotion (Insufficient Order Amount)');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'FAMILY40',
      orderAmount: 1000, // Min is $2000
      userId: 1
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  // Test 7: Claim unassigned private promotion
  console.log('\n🎁 Test 7: Claim Unassigned Private Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'NEWUSER50',
      orderAmount: 300,
      userId: 1 // This should claim the promotion for user 1
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 8: Try to claim already claimed promotion
  console.log('\n🔒 Test 8: Try to Claim Already Claimed Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'NEWUSER50',
      orderAmount: 300,
      userId: 2 // Different user trying to use already claimed promotion
    });
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  console.log('\n🎉 API Testing Complete!');
}

// Run the test
testPromotionAPI().catch(console.error);
