import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { clearPromotion, setError } from '../redux/promotion/promotionSlice';

// Error types and their handling strategies
const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  EXPIRATION_ERROR: 'EXPIRATION_ERROR',
  USAGE_LIMIT_ERROR: 'USAGE_LIMIT_ERROR',
  INVALID_CODE_ERROR: 'INVALID_CODE_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK_ERROR]: 'Network connection issue. Please check your internet connection.',
  [ERROR_TYPES.VALIDATION_ERROR]: 'Promotion validation failed. Please try again.',
  [ERROR_TYPES.EXPIRATION_ERROR]: 'This promotion has expired.',
  [ERROR_TYPES.USAGE_LIMIT_ERROR]: 'You have reached the usage limit for this promotion.',
  [ERROR_TYPES.INVALID_CODE_ERROR]: 'Invalid promotion code. Please check and try again.',
  [ERROR_TYPES.SERVER_ERROR]: 'Server error occurred. Please try again later.',
  [ERROR_TYPES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
};

const RECOVERY_STRATEGIES = {
  [ERROR_TYPES.NETWORK_ERROR]: 'RETRY',
  [ERROR_TYPES.VALIDATION_ERROR]: 'RETRY',
  [ERROR_TYPES.EXPIRATION_ERROR]: 'CLEAR_AND_SUGGEST',
  [ERROR_TYPES.USAGE_LIMIT_ERROR]: 'CLEAR_AND_SUGGEST',
  [ERROR_TYPES.INVALID_CODE_ERROR]: 'CLEAR_AND_PROMPT',
  [ERROR_TYPES.SERVER_ERROR]: 'RETRY_WITH_DELAY',
  [ERROR_TYPES.UNKNOWN_ERROR]: 'CLEAR_AND_REPORT',
};

export const usePromotionErrorHandler = () => {
  const dispatch = useDispatch();

  // Classify error type based on error message or status
  const classifyError = useCallback((error) => {
    if (!error) return ERROR_TYPES.UNKNOWN_ERROR;
    
    const message = typeof error === 'string' ? error : error.message || '';
    const status = error.status || error.response?.status;
    
    // Network errors
    if (!navigator.onLine || message.includes('Network Error') || status === 0) {
      return ERROR_TYPES.NETWORK_ERROR;
    }
    
    // Server errors
    if (status >= 500) {
      return ERROR_TYPES.SERVER_ERROR;
    }
    
    // Specific promotion errors
    if (message.includes('expired') || message.includes('not active')) {
      return ERROR_TYPES.EXPIRATION_ERROR;
    }
    
    if (message.includes('usage limit') || message.includes('reached the limit')) {
      return ERROR_TYPES.USAGE_LIMIT_ERROR;
    }
    
    if (message.includes('Invalid') || message.includes('not found') || status === 404) {
      return ERROR_TYPES.INVALID_CODE_ERROR;
    }
    
    if (message.includes('validation') || status === 400) {
      return ERROR_TYPES.VALIDATION_ERROR;
    }
    
    return ERROR_TYPES.UNKNOWN_ERROR;
  }, []);

  // Get user-friendly error message
  const getErrorMessage = useCallback((errorType, originalMessage) => {
    return ERROR_MESSAGES[errorType] || originalMessage || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN_ERROR];
  }, []);

  // Handle error with appropriate recovery strategy
  const handleError = useCallback((error, options = {}) => {
    const {
      showToast = true,
      autoRetry = false,
      onRetry = null,
      onClear = null,
      onSuggestAlternative = null,
    } = options;
    
    const errorType = classifyError(error);
    const errorMessage = getErrorMessage(errorType, error.message || error);
    const recoveryStrategy = RECOVERY_STRATEGIES[errorType];
    
    console.error('Promotion error:', { error, errorType, recoveryStrategy });
    
    // Set error in Redux state
    dispatch(setError(errorMessage));
    
    // Show toast notification if enabled
    if (showToast) {
      toast.error(errorMessage, {
        position: 'top-right',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
    
    // Execute recovery strategy
    switch (recoveryStrategy) {
      case 'RETRY':
        if (autoRetry && onRetry) {
          setTimeout(() => {
            onRetry();
          }, 1000);
        }
        break;
        
      case 'RETRY_WITH_DELAY':
        if (autoRetry && onRetry) {
          setTimeout(() => {
            onRetry();
          }, 3000);
        }
        break;
        
      case 'CLEAR_AND_SUGGEST':
        dispatch(clearPromotion());
        if (onSuggestAlternative) {
          setTimeout(() => {
            onSuggestAlternative();
          }, 1000);
        }
        break;
        
      case 'CLEAR_AND_PROMPT':
        dispatch(clearPromotion());
        if (onClear) {
          onClear();
        }
        break;
        
      case 'CLEAR_AND_REPORT':
        dispatch(clearPromotion());
        // Report to monitoring service
        if (window.gtag) {
          window.gtag('event', 'exception', {
            description: `Promotion Error: ${errorMessage}`,
            fatal: false,
          });
        }
        break;
        
      default:
        // No specific recovery action
        break;
    }
    
    return {
      errorType,
      errorMessage,
      recoveryStrategy,
      canRetry: ['RETRY', 'RETRY_WITH_DELAY'].includes(recoveryStrategy),
      shouldClear: ['CLEAR_AND_SUGGEST', 'CLEAR_AND_PROMPT', 'CLEAR_AND_REPORT'].includes(recoveryStrategy),
    };
  }, [classifyError, getErrorMessage, dispatch]);

  // Handle network-specific errors
  const handleNetworkError = useCallback((error, retryCallback) => {
    return handleError(error, {
      showToast: true,
      autoRetry: true,
      onRetry: retryCallback,
    });
  }, [handleError]);

  // Handle validation errors
  const handleValidationError = useCallback((error, clearCallback) => {
    return handleError(error, {
      showToast: true,
      onClear: clearCallback,
    });
  }, [handleError]);

  // Handle expiration errors
  const handleExpirationError = useCallback((error, suggestAlternativeCallback) => {
    return handleError(error, {
      showToast: true,
      onSuggestAlternative: suggestAlternativeCallback,
    });
  }, [handleError]);

  // Create error handler for specific contexts
  const createContextualHandler = useCallback((context, callbacks = {}) => {
    return (error) => {
      console.log(`Promotion error in ${context}:`, error);
      return handleError(error, callbacks);
    };
  }, [handleError]);

  return {
    handleError,
    handleNetworkError,
    handleValidationError,
    handleExpirationError,
    createContextualHandler,
    classifyError,
    getErrorMessage,
    ERROR_TYPES,
    ERROR_MESSAGES,
    RECOVERY_STRATEGIES,
  };
};

export default usePromotionErrorHandler;
