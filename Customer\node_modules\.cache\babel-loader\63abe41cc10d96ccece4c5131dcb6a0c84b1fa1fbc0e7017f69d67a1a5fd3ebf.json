{"ast": null, "code": "import { configureStore } from \"@reduxjs/toolkit\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport createSagaMiddleware from \"redux-saga\";\nimport { persistStore, persistReducer } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\";\nimport rootReducer from \"./root-reducer\";\nimport rootSaga from \"./root-saga\";\n\n// Khởi tạo middleware saga\nconst sagaMiddleware = createSagaMiddleware();\n\n// Cấu hình persist KHÔNG dùng expire transform nữa\nconst persistConfig = {\n  key: \"root\",\n  storage,\n  whitelist: [\"Auth\", \"Search\", \"hotel\", \"Room\", \"ChatBox\", \"Socket\", \"promotion\"]\n  // Bỏ transforms vì chúng ta sẽ tự handle expire\n};\n\n// Gộp persist vào reducer\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\n\n// Tạo store\nconst store = configureStore({\n  reducer: persistedReducer,\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: false\n  }).concat(sagaMiddleware)\n});\n\n// Chạy saga\nsagaMiddleware.run(rootSaga);\n\n// Tạo persistor để dùng trong <PersistGate>\nexport const persistor = persistStore(store);\n\n// Custom hooks\nexport const useAppDispatch = useDispatch;\nexport const useAppSelector = useSelector;\nexport default store;", "map": {"version": 3, "names": ["configureStore", "useDispatch", "useSelector", "createSagaMiddleware", "persistStore", "persistReducer", "storage", "rootReducer", "rootSaga", "sagaMiddleware", "persistConfig", "key", "whitelist", "persistedReducer", "store", "reducer", "middleware", "getDefaultMiddleware", "serializableCheck", "concat", "run", "persistor", "useAppDispatch", "useAppSelector"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/store.js"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\"\r\nimport { useDispatch, useSelector } from \"react-redux\"\r\nimport createSagaMiddleware from \"redux-saga\"\r\nimport { persistStore, persistReducer } from \"redux-persist\"\r\nimport storage from \"redux-persist/lib/storage\"\r\n\r\nimport rootReducer from \"./root-reducer\"\r\nimport rootSaga from \"./root-saga\"\r\n\r\n// Khởi tạo middleware saga\r\nconst sagaMiddleware = createSagaMiddleware()\r\n\r\n// Cấu hình persist KHÔNG dùng expire transform nữa\r\nconst persistConfig = {\r\n  key: \"root\",\r\n  storage,\r\n  whitelist: [\"Auth\", \"Search\", \"hotel\", \"Room\", \"ChatBox\", \"Socket\", \"promotion\"],\r\n  // Bỏ transforms vì chúng ta sẽ tự handle expire\r\n}\r\n\r\n// Gộp persist vào reducer\r\nconst persistedReducer = persistReducer(persistConfig, rootReducer)\r\n\r\n// Tạo store\r\nconst store = configureStore({\r\n  reducer: persistedReducer,\r\n  middleware: (getDefaultMiddleware) =>\r\n    getDefaultMiddleware({\r\n      serializableCheck: false,\r\n    }).concat(sagaMiddleware),\r\n})\r\n\r\n// Chạy saga\r\nsagaMiddleware.run(rootSaga)\r\n\r\n// Tạo persistor để dùng trong <PersistGate>\r\nexport const persistor = persistStore(store)\r\n\r\n// Custom hooks\r\nexport const useAppDispatch = useDispatch\r\nexport const useAppSelector = useSelector\r\n\r\nexport default store\r\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,oBAAoB,MAAM,YAAY;AAC7C,SAASC,YAAY,EAAEC,cAAc,QAAQ,eAAe;AAC5D,OAAOC,OAAO,MAAM,2BAA2B;AAE/C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,aAAa;;AAElC;AACA,MAAMC,cAAc,GAAGN,oBAAoB,CAAC,CAAC;;AAE7C;AACA,MAAMO,aAAa,GAAG;EACpBC,GAAG,EAAE,MAAM;EACXL,OAAO;EACPM,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW;EAC/E;AACF,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAGR,cAAc,CAACK,aAAa,EAAEH,WAAW,CAAC;;AAEnE;AACA,MAAMO,KAAK,GAAGd,cAAc,CAAC;EAC3Be,OAAO,EAAEF,gBAAgB;EACzBG,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,CAACC,MAAM,CAACV,cAAc;AAC5B,CAAC,CAAC;;AAEF;AACAA,cAAc,CAACW,GAAG,CAACZ,QAAQ,CAAC;;AAE5B;AACA,OAAO,MAAMa,SAAS,GAAGjB,YAAY,CAACU,KAAK,CAAC;;AAE5C;AACA,OAAO,MAAMQ,cAAc,GAAGrB,WAAW;AACzC,OAAO,MAAMsB,cAAc,GAAGrB,WAAW;AAEzC,eAAeY,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}