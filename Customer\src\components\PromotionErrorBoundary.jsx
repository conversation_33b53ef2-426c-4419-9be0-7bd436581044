import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Container } from 'react-bootstrap';
import { ExclamationTriangleFill, ArrowClockwise, TagFill } from 'react-bootstrap-icons';

class PromotionErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Promotion Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service if available
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: `Promotion Error: ${error.message}`,
        fatal: false,
      });
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
    }));

    // Call onRetry prop if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleClearPromotion = () => {
    if (this.props.onClearPromotion) {
      this.props.onClearPromotion();
    }
    this.handleRetry();
  };

  render() {
    if (this.state.hasError) {
      const { retryCount } = this.state;
      const maxRetries = this.props.maxRetries || 3;
      
      return (
        <Container className="my-4">
          <Card className="border-warning">
            <Card.Header className="bg-warning text-dark">
              <div className="d-flex align-items-center">
                <ExclamationTriangleFill className="me-2" />
                <strong>Promotion System Error</strong>
              </div>
            </Card.Header>
            <Card.Body>
              <Alert variant="warning" className="mb-3">
                <Alert.Heading className="h6">
                  Something went wrong with the promotion system
                </Alert.Heading>
                <p className="mb-0">
                  We encountered an issue while processing your promotion. 
                  You can try again or continue without the promotion.
                </p>
              </Alert>

              {process.env.NODE_ENV === 'development' && (
                <Alert variant="secondary" className="mb-3">
                  <details>
                    <summary>Error Details (Development)</summary>
                    <pre className="mt-2 small">
                      {this.state.error && this.state.error.toString()}
                      {this.state.errorInfo && this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                </Alert>
              )}

              <div className="d-flex gap-2 flex-wrap">
                {retryCount < maxRetries && (
                  <Button
                    variant="outline-primary"
                    onClick={this.handleRetry}
                    className="d-flex align-items-center"
                  >
                    <ArrowClockwise className="me-1" />
                    Try Again {retryCount > 0 && `(${retryCount}/${maxRetries})`}
                  </Button>
                )}
                
                <Button
                  variant="outline-secondary"
                  onClick={this.handleClearPromotion}
                  className="d-flex align-items-center"
                >
                  <TagFill className="me-1" />
                  Clear Promotion
                </Button>
                
                {this.props.onContinueWithoutPromotion && (
                  <Button
                    variant="primary"
                    onClick={this.props.onContinueWithoutPromotion}
                  >
                    Continue Without Promotion
                  </Button>
                )}
              </div>

              {retryCount >= maxRetries && (
                <Alert variant="info" className="mt-3 mb-0">
                  <small>
                    If the problem persists, please contact support or try again later.
                  </small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default PromotionErrorBoundary;
