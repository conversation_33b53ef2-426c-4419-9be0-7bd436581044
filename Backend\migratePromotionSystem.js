const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const UserPromotion = require("./src/models/UserPromotion");
const PromotionUser = require("./src/models/PromotionUser");
const User = require("./src/models/user");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function migratePromotionSystem() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB for migration");

    console.log("\n🔄 Starting Promotion System Migration");
    console.log("=" .repeat(60));

    // Step 1: Update existing promotions to new schema
    console.log("\n📝 Step 1: Migrating Promotion Schema");
    
    const promotions = await Promotion.find({});
    console.log(`Found ${promotions.length} promotions to migrate`);

    for (const promotion of promotions) {
      const updateData = {};
      
      // Convert isPersonalized to isPublic
      if (promotion.isPersonalized !== undefined) {
        updateData.isPublic = !promotion.isPersonalized;
      } else {
        updateData.isPublic = true; // Default to public
      }

      // For personalized promotions, find the assigned user
      if (promotion.isPersonalized) {
        // Find UserPromotion assignments for this promotion
        const assignments = await UserPromotion.find({ promotionId: promotion._id });
        
        if (assignments.length === 1) {
          // Single user assignment - convert to private promotion
          updateData.userId = assignments[0].userId;
          updateData.isPublic = false;
          console.log(`  - ${promotion.code}: Converting to private for User ${assignments[0].userId}`);
        } else if (assignments.length > 1) {
          // Multiple users - create separate private promotions for each
          console.log(`  - ${promotion.code}: Creating ${assignments.length} private copies`);
          
          for (let i = 0; i < assignments.length; i++) {
            const assignment = assignments[i];
            const newCode = `${promotion.code}_U${assignment.userId}`;
            
            const privatePromotion = new Promotion({
              code: newCode,
              name: `${promotion.name} (Private)`,
              description: promotion.description,
              discountType: promotion.discountType,
              discountValue: promotion.discountValue,
              maxDiscountAmount: promotion.maxDiscountAmount,
              minOrderAmount: promotion.minOrderAmount,
              startDate: promotion.startDate,
              endDate: promotion.endDate,
              usageLimit: assignment.maxUsage,
              usedCount: assignment.usageCount,
              userUsageLimit: assignment.maxUsage,
              isActive: promotion.isActive,
              isPublic: false,
              userId: assignment.userId,
              createdBy: promotion.createdBy
            });
            
            await privatePromotion.save();
            console.log(`    Created private promotion: ${newCode} for User ${assignment.userId}`);
          }
          
          // Convert original to public
          updateData.isPublic = true;
          updateData.userId = null;
        } else {
          // No assignments found - keep as public
          updateData.isPublic = true;
          updateData.userId = null;
        }
      }

      // Remove deprecated fields
      await Promotion.updateOne(
        { _id: promotion._id },
        { 
          $set: updateData,
          $unset: { 
            isPersonalized: "",
            targetUserTypes: ""
          }
        }
      );
    }

    // Step 2: Clean up UserPromotion collection (no longer needed)
    console.log("\n🗑️  Step 2: Cleaning up UserPromotion collection");
    const userPromotionCount = await UserPromotion.countDocuments();
    console.log(`Found ${userPromotionCount} UserPromotion records`);
    
    if (userPromotionCount > 0) {
      // Backup UserPromotion data before deletion
      const userPromotions = await UserPromotion.find({}).populate('promotionId');
      console.log("Backing up UserPromotion data...");
      
      // You could save this to a file if needed
      // require('fs').writeFileSync('./userpromotion_backup.json', JSON.stringify(userPromotions, null, 2));
      
      // Delete UserPromotion collection
      await UserPromotion.deleteMany({});
      console.log("✅ UserPromotion collection cleared");
    }

    // Step 3: Verify migration
    console.log("\n✅ Step 3: Verifying Migration");
    
    const publicPromotions = await Promotion.countDocuments({ isPublic: true });
    const privatePromotions = await Promotion.countDocuments({ isPublic: false });
    const totalPromotions = await Promotion.countDocuments();
    
    console.log(`📊 Migration Results:`);
    console.log(`  - Total promotions: ${totalPromotions}`);
    console.log(`  - Public promotions: ${publicPromotions}`);
    console.log(`  - Private promotions: ${privatePromotions}`);

    // Step 4: Test new system
    console.log("\n🧪 Step 4: Testing New System");
    
    // Test public promotion
    const publicPromo = await Promotion.findOne({ isPublic: true });
    if (publicPromo) {
      console.log(`Testing public promotion: ${publicPromo.code}`);
      const canUse = await publicPromo.canUserUse(1);
      console.log(`  User 1 can use: ${canUse.canUse} (${canUse.remainingUses} remaining)`);
    }

    // Test private promotion
    const privatePromo = await Promotion.findOne({ isPublic: false });
    if (privatePromo) {
      console.log(`Testing private promotion: ${privatePromo.code} (User ${privatePromo.userId})`);
      const canUseOwner = await privatePromo.canUserUse(privatePromo.userId);
      const canUseOther = await privatePromo.canUserUse(privatePromo.userId === 1 ? 2 : 1);
      console.log(`  Owner can use: ${canUseOwner.canUse}`);
      console.log(`  Other user can use: ${canUseOther.canUse}`);
    }

    console.log("\n🎉 Migration Completed Successfully!");
    console.log("\n📋 New System Summary:");
    console.log("✅ Simplified to 2 models: Promotion + PromotionUser");
    console.log("✅ Public promotions: isPublic=true, userId=null");
    console.log("✅ Private promotions: isPublic=false, userId=specific_user");
    console.log("✅ Single usage tracking via PromotionUser");
    console.log("✅ Removed complex UserPromotion assignment system");

    console.log("\n🚀 System is ready for testing!");

    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

migratePromotionSystem();
