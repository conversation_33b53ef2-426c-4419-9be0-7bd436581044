const Promotion = require('../../models/Promotion');
const PromotionUser = require('../../models/PromotionUser');
const promotionCleanupJobs = require('../../jobs/promotionCleanupJobs');

// Create new promotion
exports.createPromotion = async (req, res) => {
  try {
    const promotion = new Promotion({ ...req.body, createdBy: req.user._id });
    await promotion.save();
    res.status(201).json(promotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all promotions with pagination
exports.getAllPromotions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status; // 'active', 'inactive', 'expired', 'upcoming', 'all'
    const type = req.query.type; // 'public', 'personalized', 'all'
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    const userId = req.query.userId; // For filtering user-specific promotions

    // Build filter object
    let filter = {};

    // For admin, show all promotions (including personalized)
    // For non-admin users, exclude personalized promotions unless they can use them
    if (!req.user || req.user.role !== 'ADMIN') {
      filter.isPersonalized = { $ne: true };
    }
    // Admin can see all promotions, no additional filter needed

    // Type filter (only for admin)
    if (req.user && req.user.role === 'ADMIN' && type && type !== 'all') {
      if (type === 'public') {
        filter.isPersonalized = { $ne: true };
      } else if (type === 'personalized') {
        filter.isPersonalized = true;
      }
    }

    // Search filter
    if (search) {
      filter.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    const now = new Date();
    if (status === 'active') {
      filter.isActive = true;
      filter.startDate = { $lte: now };
      filter.endDate = { $gte: now };
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: now };
    } else if (status === 'upcoming') {
      // Upcoming: active promotions that haven't started yet
      filter.isActive = true;
      filter.startDate = { $gt: now };
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalPromotions = await Promotion.countDocuments(filter);
    const totalPages = Math.ceil(totalPromotions / limit);

    // Get promotions with pagination
    const promotions = await Promotion.find(filter)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit);

    // Calculate statistics based on current filter
    const baseStatsFilter = {};

    // Apply same base filter as main query for stats
    if (!req.user || req.user.role !== 'ADMIN') {
      baseStatsFilter.isPersonalized = { $ne: true };
    } else if (req.user && req.user.role === 'ADMIN' && type && type !== 'all') {
      if (type === 'public') {
        baseStatsFilter.isPersonalized = { $ne: true };
      } else if (type === 'personalized') {
        baseStatsFilter.isPersonalized = true;
      }
    }

    const stats = {
      total: await Promotion.countDocuments(baseStatsFilter),
      active: await Promotion.countDocuments({
        ...baseStatsFilter,
        isActive: true,
        startDate: { $lte: now },
        endDate: { $gte: now }
      }),
      inactive: await Promotion.countDocuments({
        ...baseStatsFilter,
        isActive: false
      }),
      expired: await Promotion.countDocuments({
        ...baseStatsFilter,
        endDate: { $lt: now }
      }),
      upcoming: await Promotion.countDocuments({
        ...baseStatsFilter,
        isActive: true,
        startDate: { $gt: now }
      })
    };

    res.json({
      promotions,
      pagination: {
        currentPage: page,
        totalPages,
        totalPromotions,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      stats,
      filters: {
        search,
        status,
        sortBy,
        sortOrder: req.query.sortOrder || 'desc'
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get promotion by ID
exports.getPromotionById = async (req, res) => {
  try {
    const promotion = await Promotion.findById(req.params.id);
    if (!promotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(promotion);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update promotion
exports.updatePromotion = async (req, res) => {
  try {
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete promotion
exports.deletePromotion = async (req, res) => {
  try {
    const deleted = await Promotion.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: 'Promotion not found' });
    res.json({ message: 'Promotion deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Toggle promotion status
exports.togglePromotionStatus = async (req, res) => {
  try {
    const { isActive } = req.body;
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Apply promotion code with auto-claim for unassigned private promotions
exports.applyPromotionCode = async (req, res) => {
  try {
    const { code, orderAmount, userId } = req.body;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required for promotion application' });
    }

    // Find promotion by code first
    const promotion = await Promotion.findOne({
      code: code.toUpperCase(),
      isActive: true
    });

    if (!promotion) {
      return res.status(404).json({
        valid: false,
        message: 'Invalid or inactive promotion code'
      });
    }

    // Check if this is an unassigned private promotion that can be claimed
    if (!promotion.isPublic && promotion.userId === null) {
      console.log(`Auto-claiming unassigned private promotion ${promotion.code} for user ${userId}`);

      // Claim the promotion for this user
      await Promotion.findByIdAndUpdate(promotion._id, {
        userId: userId
      });

      // Update the promotion object for further processing
      promotion.userId = userId;
    }

    // Apply with the found promotion ID using optimistic locking
    const applyResult = await Promotion.applyPromotionWithLock(
      promotion._id,
      userId,
      orderAmount
    );

    res.json({
      valid: true,
      discount: applyResult.discount,
      message: promotion.userId === userId && !promotion.isPublic ?
        'Private promotion claimed and applied successfully!' :
        'Promotion applied successfully',
      promotionId: applyResult.promotionId,
      userUsageLimit: applyResult.userUsageLimit,
      remainingUses: applyResult.remainingUses,
      claimed: !promotion.isPublic && promotion.userId === null // Indicate if promotion was just claimed
    });
  } catch (error) {
    console.error('Apply promotion error:', error);
    res.status(400).json({
      valid: false,
      message: error.message || 'Failed to apply promotion'
    });
  }
};

// Get promotion usage statistics (Admin only)
exports.getPromotionStats = async (req, res) => {
  try {
    const { id } = req.params;
    const promotion = await Promotion.findById(id);

    if (!promotion) {
      return res.status(404).json({ message: 'Promotion not found' });
    }

    const stats = await PromotionUser.getPromotionStats(id);

    res.json({
      promotion: {
        _id: promotion._id,
        code: promotion.code,
        name: promotion.name,
        usageLimit: promotion.usageLimit,
        usedCount: promotion.usedCount,
        userUsageLimit: promotion.userUsageLimit
      },
      stats
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get user promotion history (Admin only)
exports.getUserPromotionHistory = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    const history = await PromotionUser.getUserPromotionHistory(userId, {
      page: parseInt(page),
      limit: parseInt(limit),
      status
    });

    const totalCount = await PromotionUser.countDocuments({
      userId: parseInt(userId),
      ...(status && { status })
    });

    res.json({
      history,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / parseInt(limit)),
        totalCount,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all promotion usage records with filters (Admin only)
exports.getAllPromotionUsage = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      promotionId,
      userId,
      status,
      startDate,
      endDate
    } = req.query;

    let filter = {};

    if (promotionId) filter.promotionId = promotionId;
    if (userId) filter.userId = parseInt(userId);
    if (status) filter.status = status;

    if (startDate || endDate) {
      filter.usedAt = {};
      if (startDate) filter.usedAt.$gte = new Date(startDate);
      if (endDate) filter.usedAt.$lte = new Date(endDate);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const usageRecords = await PromotionUser.find(filter)
      .populate('promotionId', 'code name discountType discountValue')
      .populate('userId', 'name email')
      .populate('reservationId', 'totalPrice finalPrice status')
      .sort({ usedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await PromotionUser.countDocuments(filter);

    res.json({
      usageRecords,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / parseInt(limit)),
        totalCount,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Check if user can use promotion (Customer)
exports.checkUserPromotionEligibility = async (req, res) => {
  try {
    const { promotionId } = req.params;
    const userId = req.user._id; // Assuming user is authenticated

    const promotion = await Promotion.findById(promotionId);
    if (!promotion) {
      return res.status(404).json({ message: 'Promotion not found' });
    }

    const eligibility = await promotion.canUserUse(userId);

    res.json({
      promotionId,
      userId,
      ...eligibility
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Generate random voucher code
function generateVoucherCode(prefix = "PERSONAL", length = 6) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Create personalized voucher (Admin only)
exports.createPersonalizedVoucher = async (req, res) => {
  try {
    const {
      codePrefix = "PERSONAL",
      discountType = "PERCENTAGE",
      discountValue,
      maxDiscountAmount,
      minOrderAmount = 0,
      userUsageLimit = 1,
      totalUsageLimit = 1,
      validDays = 30,
      description = "Personalized promotion",
      targetUserId = null
    } = req.body;

    // Validation
    if (!discountValue || discountValue <= 0) {
      return res.status(400).json({ message: 'Discount value is required and must be greater than 0' });
    }

    if (!['PERCENTAGE', 'FIXED_AMOUNT'].includes(discountType)) {
      return res.status(400).json({ message: 'Discount type must be PERCENTAGE or FIXED_AMOUNT' });
    }

    const code = generateVoucherCode(codePrefix);
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + validDays);

    const voucherData = {
      code: code,
      name: `Personalized Voucher - ${code}`,
      description: description,
      discountType: discountType,
      discountValue: discountValue,
      maxDiscountAmount: maxDiscountAmount,
      minOrderAmount: minOrderAmount,
      startDate: startDate,
      endDate: endDate,
      usageLimit: totalUsageLimit,
      usedCount: 0,
      userUsageLimit: userUsageLimit,
      isActive: true,
      isPersonalized: true,
      targetUserTypes: targetUserId ? ['SPECIFIC_USER'] : ['ALL'],
      createdBy: req.user._id || 1
    };

    const voucher = await Promotion.create(voucherData);

    res.status(201).json({
      success: true,
      message: 'Personalized voucher created successfully',
      voucher: {
        id: voucher._id,
        code: voucher.code,
        discountType: voucher.discountType,
        discountValue: voucher.discountValue,
        userUsageLimit: voucher.userUsageLimit,
        totalUsageLimit: voucher.usageLimit,
        validUntil: voucher.endDate,
        minOrderAmount: voucher.minOrderAmount,
        maxDiscountAmount: voucher.maxDiscountAmount
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create bulk campaign vouchers (Admin only)
exports.createCampaignVouchers = async (req, res) => {
  try {
    const {
      count = 10,
      codePrefix = "CAMPAIGN",
      ...voucherOptions
    } = req.body;

    if (count > 100) {
      return res.status(400).json({ message: 'Cannot create more than 100 vouchers at once' });
    }

    const vouchers = [];
    for (let i = 0; i < count; i++) {
      const customPrefix = `${codePrefix}${String(i + 1).padStart(2, '0')}`;
      const code = generateVoucherCode(customPrefix);

      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + (voucherOptions.validDays || 30));

      const voucherData = {
        code: code,
        name: `Campaign Voucher - ${code}`,
        description: voucherOptions.description || "Campaign promotion",
        discountType: voucherOptions.discountType || "PERCENTAGE",
        discountValue: voucherOptions.discountValue,
        maxDiscountAmount: voucherOptions.maxDiscountAmount,
        minOrderAmount: voucherOptions.minOrderAmount || 0,
        startDate: startDate,
        endDate: endDate,
        usageLimit: voucherOptions.totalUsageLimit || 1,
        usedCount: 0,
        userUsageLimit: voucherOptions.userUsageLimit || 1,
        isActive: true,
        isPersonalized: true,
        targetUserTypes: ['ALL'],
        createdBy: req.user._id || 1
      };

      const voucher = await Promotion.create(voucherData);
      vouchers.push({
        id: voucher._id,
        code: voucher.code,
        discountValue: voucher.discountValue,
        discountType: voucher.discountType
      });
    }

    res.status(201).json({
      success: true,
      message: `${count} campaign vouchers created successfully`,
      vouchers: vouchers,
      summary: {
        totalCreated: vouchers.length,
        codes: vouchers.map(v => v.code)
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get user-specific promotions (using simplified isPublic/userId system)
exports.getUserPromotions = async (req, res) => {
  try {
    const userId = req.user?._id;

    let query;
    if (userId) {
      // Authenticated user: get public + their private promotions + unassigned private promotions
      query = {
        $or: [
          { isPublic: true, isActive: true }, // Public promotions
          { isPublic: false, userId: userId, isActive: true }, // Private promotions for this user
          { isPublic: false, userId: null, isActive: true } // Unassigned private promotions (claimable)
        ]
      };
    } else {
      // Guest user: public promotions + unassigned private promotions
      query = {
        $or: [
          { isPublic: true, isActive: true }, // Public promotions
          { isPublic: false, userId: null, isActive: true } // Unassigned private promotions (claimable)
        ]
      };
    }

    const promotions = await Promotion.find(query).sort({ createdAt: -1 });

    // Add usage info for each promotion
    const promotionsWithDetails = await Promise.all(
      promotions.map(async (promotion) => {
        // Get user usage count
        const userUsageCount = await PromotionUser.getUserUsageCount(userId, promotion._id);
        const remainingUses = promotion.userUsageLimit - userUsageCount;
        const usagePercentage = Math.round((userUsageCount / promotion.userUsageLimit) * 100);

        // Check if user can use this promotion
        const eligibility = await promotion.canUserUse(userId);

        return {
          _id: promotion._id,
          code: promotion.code,
          name: promotion.name,
          description: promotion.description,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          maxDiscountAmount: promotion.maxDiscountAmount,
          minOrderAmount: promotion.minOrderAmount,
          startDate: promotion.startDate,
          endDate: promotion.endDate,
          // Usage info
          usageCount: userUsageCount,
          maxUsage: promotion.userUsageLimit,
          usagePercentage,
          remainingUses,
          isAvailable: eligibility.canUse,
          // Type info - include all needed fields
          isPublic: promotion.isPublic,
          userId: promotion.userId,
          isPersonalized: !promotion.isPublic,
          assignmentType: promotion.isPublic ? 'PUBLIC' : (promotion.userId === null ? 'CLAIMABLE' : 'PRIVATE')
        };
      })
    )

    res.json({
      success: true,
      promotions: promotionsWithDetails,
      total: promotionsWithDetails.length
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get public promotions for customers (exclude personalized ones)
exports.getPublicPromotions = async (req, res) => {
  try {
    const now = new Date();
    const userId = req.user?._id; // Get user ID if authenticated

    // Only get non-personalized, active, and currently valid promotions
    const filter = {
      isPersonalized: { $ne: true },
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now },
      // Add usage limit check in query to avoid checking later
      $or: [
        { usageLimit: null },
        { usageLimit: { $exists: false } },
        { $expr: { $lt: ["$usedCount", "$usageLimit"] } }
      ]
    };

    const promotions = await Promotion.find(filter)
      .select('code name description discountType discountValue maxDiscountAmount minOrderAmount userUsageLimit usageLimit usedCount startDate endDate')
      .sort({ createdAt: -1 });

    // Add usage percentage and filter only usable promotions
    const promotionsWithUsage = await Promise.all(promotions.map(async promotion => {
      // Query already filtered for valid promotions, just check user-specific usage
      const usagePercentage = promotion.usageLimit
        ? Math.round((promotion.usedCount / promotion.usageLimit) * 100)
        : 0;

      let userCanUse = true;
      let remainingUses = promotion.userUsageLimit || 0;

      // Check user-specific usage if user is authenticated
      if (userId) {
        const userEligibility = await promotion.canUserUse(userId);
        userCanUse = userEligibility.canUse;
        remainingUses = userEligibility.remainingUses || 0;

        // If user cannot use this promotion OR has no remaining uses, don't include it
        if (!userCanUse || remainingUses === 0) {
          return null;
        }
      }

      return {
        ...promotion.toObject(),
        usagePercentage,
        isAvailable: true, // Only available promotions reach here
        userCanUse: true,  // Only usable promotions reach here
        remainingUses
      };
    }));

    // Remove null entries (promotions that are not available or user cannot use)
    const availablePromotions = promotionsWithUsage.filter(promo => promo !== null);

    res.json({
      success: true,
      promotions: availablePromotions,
      total: availablePromotions.length
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Confirm promotion usage when booking is successful
exports.confirmPromotionUsage = async (req, res) => {
  try {
    const { promotionId, reservationId } = req.body;
    const userId = req.user._id;

    if (!promotionId || !reservationId) {
      return res.status(400).json({ message: 'Promotion ID and Reservation ID are required' });
    }

    const result = await Promotion.confirmPromotionUsage(promotionId, userId, reservationId);

    res.json({
      success: true,
      message: 'Promotion usage confirmed successfully',
      promotion: result
    });
  } catch (error) {
    console.error('Confirm promotion usage error:', error);
    res.status(400).json({ message: error.message });
  }
};

// Cancel promotion reservation - DEPRECATED (no reservation system)
exports.cancelPromotionReservation = async (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Reservation system has been simplified. Promotions are applied directly.'
  });
};

// Get user's active promotion reservations - DEPRECATED
exports.getUserActiveReservations = async (req, res) => {
  res.json({
    success: true,
    reservations: [],
    message: 'Reservation system has been simplified. No active reservations.'
  });
};

// Get all promotion reservations (Admin only)
exports.getAllPromotionReservations = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, promotionId } = req.query;
    const skip = (page - 1) * limit;

    let filter = {};
    if (status) filter.status = status;
    if (promotionId) filter.promotionId = promotionId;

    // DEPRECATED: Return empty results
    const reservations = [];
    const total = 0;

    res.json({
      success: true,
      reservations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get all promotion reservations error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Run cleanup job manually (Admin only)
exports.runCleanupJob = async (req, res) => {
  try {
    const { type } = req.params;
    const validTypes = ['expired', 'cancelled', 'orphaned', 'sync', 'report'];

    if (!validTypes.includes(type)) {
      return res.status(400).json({
        message: `Invalid cleanup type. Valid types: ${validTypes.join(', ')}`
      });
    }

    const result = await promotionCleanupJobs.runCleanup(type);

    res.json({
      success: true,
      message: `Cleanup job '${type}' completed successfully`,
      result
    });
  } catch (error) {
    console.error('Run cleanup job error:', error);
    res.status(500).json({ message: error.message });
  }
};
