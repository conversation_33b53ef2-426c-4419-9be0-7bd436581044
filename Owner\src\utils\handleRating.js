/**
 * Map rating value (1-5) to Vietnamese rating label
 * 1: <PERSON><PERSON> bả<PERSON>
 * 2: <PERSON><PERSON><PERSON><PERSON>
 * 3: <PERSON><PERSON><PERSON>
 * 4: Tốt
 * 5: <PERSON><PERSON><PERSON>
 */
export const ratingLabelMap = {
  1: "<PERSON><PERSON> bản",
  2: "<PERSON><PERSON><PERSON><PERSON>",
  3: "<PERSON><PERSON><PERSON>",
  4: "<PERSON><PERSON><PERSON>",
  5: "<PERSON>ất <PERSON>",
};


export default function handleRating(value) {
  return ratingLabelMap[Math.floor(value)] || "Không xác định";
}
