const express = require('express');
const router = express.Router();
const userPromotionController = require('./UserPromotionController');
const checkCustomer = require('../../middlewares/checkCustomer');
const { isAdmin } = require('../../middlewares/checkAdmin');

// Customer routes
router.get('/my-promotions', checkCustomer, userPromotionController.getUserPromotions);

// Admin routes
router.post('/assign', isAdmin, userPromotionController.assignPromotionToUser);
router.post('/bulk-assign', isAdmin, userPromotionController.bulkAssignPromotion);
router.post('/revoke', isAdmin, userPromotionController.revokePromotionFromUser);
router.get('/all', isAdmin, userPromotionController.getAllUserPromotions);
router.get('/stats/:promotionId', isAdmin, userPromotionController.getPromotionAssignmentStats);

// Auto-assignment (can be called by system or admin)
router.post('/auto-assign', userPromotionController.autoAssignPromotions);

module.exports = router;
