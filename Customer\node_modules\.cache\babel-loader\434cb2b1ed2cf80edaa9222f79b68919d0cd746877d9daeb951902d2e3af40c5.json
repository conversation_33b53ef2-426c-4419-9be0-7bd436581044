{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { applyPromotionCode, validatePromotion, clearPromotion, applyPromotion, selectAppliedPromotion, selectPromotionError, selectPromotionValidating, selectPromotionApplying } from \"../../../redux/promotion/promotionSlice\";\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Redux promotion state\n  const appliedPromotion = useAppSelector(selectAppliedPromotion);\n  const promotionError = useAppSelector(selectPromotionError);\n  const isPromotionValidating = useAppSelector(selectPromotionValidating);\n  const isPromotionApplying = useAppSelector(selectPromotionApplying);\n\n  // Use promotion validation hook\n  const {\n    validatePromotionIfNeeded,\n    forceValidation\n  } = usePromotionValidation();\n\n  // Legacy state for backward compatibility (will be removed gradually)\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n    setIsInitialLoading(false);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        var _bookingData$hotelDet, _bookingData$selected;\n        // Check if this is a new booking (different hotel or rooms)\n        const currentHotelId = (_bookingData$hotelDet = bookingData.hotelDetail) === null || _bookingData$hotelDet === void 0 ? void 0 : _bookingData$hotelDet._id;\n        const savedHotelId = promo.hotelId;\n        const currentRoomsHash = JSON.stringify((_bookingData$selected = bookingData.selectedRooms) === null || _bookingData$selected === void 0 ? void 0 : _bookingData$selected.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort());\n        const savedRoomsHash = promo.roomsHash;\n        if (currentHotelId !== savedHotelId || currentRoomsHash !== savedRoomsHash) {\n          // This is a new booking, clear old promotion\n          sessionStorage.removeItem(\"promotionInfo\");\n          console.log(\"🆕 New booking detected, cleared old promotion\");\n          return;\n        }\n\n        // Check if promotion was saved more than 5 minutes ago\n        const savedTime = promo.savedTime || Date.now();\n        const timeDiff = Date.now() - savedTime;\n        const fiveMinutes = 5 * 60 * 1000;\n        if (timeDiff > fiveMinutes) {\n          // Auto-validate if promotion is old\n          console.log(\"Promotion is old, auto-validating...\");\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(\"Validating promotion...\");\n          setPromotionId(promo.promotionId || null);\n        } else {\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(promo.promotionMessage || \"\");\n          setPromotionId(promo.promotionId || null);\n          console.log(\"🔄 Restored promotion for same booking:\", promo.promotionCode);\n        }\n      }\n    }\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      var _bookingData$hotelDet2, _bookingData$selected2;\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId,\n        savedTime: Date.now(),\n        // Add timestamp for validation\n        // Save booking context to detect new bookings\n        hotelId: (_bookingData$hotelDet2 = bookingData.hotelDetail) === null || _bookingData$hotelDet2 === void 0 ? void 0 : _bookingData$hotelDet2._id,\n        roomsHash: JSON.stringify((_bookingData$selected2 = bookingData.selectedRooms) === null || _bookingData$selected2 === void 0 ? void 0 : _bookingData$selected2.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort())\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes using Redux\n  useEffect(() => {\n    if (!dataRestored) return;\n\n    // If we have applied promotion from Redux, sync with legacy state\n    if (appliedPromotion.code) {\n      setPromotionCode(appliedPromotion.code);\n      setPromotionDiscount(appliedPromotion.discount);\n      setPromotionId(appliedPromotion.promotionId);\n      setPromotionMessage(`Promotion applied: -${Utils.formatCurrency(appliedPromotion.discount)}`);\n    }\n\n    // Validate promotion if subtotal changes significantly\n    if (appliedPromotion.code && subtotal > 0) {\n      validatePromotionIfNeeded(subtotal);\n    }\n  }, [dataRestored, subtotal, appliedPromotion, validatePromotionIfNeeded]);\n\n  // Sync Redux promotion error with legacy state\n  useEffect(() => {\n    if (promotionError) {\n      setPromotionMessage(promotionError);\n      // Clear legacy promotion state if there's an error\n      setPromotionCode(\"\");\n      setPromotionDiscount(0);\n      setPromotionId(null);\n      sessionStorage.removeItem(\"promotionInfo\");\n    }\n  }, [promotionError]);\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\n\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\n  const handleApplyPromotionFromModal = promotionData => {\n    if (promotionData.code) {\n      // Apply promotion using Redux\n      dispatch(applyPromotion({\n        code: promotionData.code,\n        discount: promotionData.discount,\n        promotionId: promotionData.promotionId,\n        remainingUses: promotionData.remainingUses\n      }));\n    } else {\n      // Clear promotion using Redux\n      dispatch(clearPromotion());\n    }\n\n    // Update legacy state for backward compatibility\n    setPromotionCode(promotionData.code || \"\");\n    setPromotionDiscount(promotionData.discount || 0);\n    setPromotionMessage(promotionData.message || \"\");\n    setPromotionId(promotionData.promotionId || null);\n  };\n\n  // Function to validate promotion before booking using Redux\n  const validatePromotionBeforeBooking = async () => {\n    if (!appliedPromotion.code) {\n      return {\n        valid: true\n      }; // No promotion to validate\n    }\n    try {\n      // Use Redux force validation\n      const result = await forceValidation(subtotal);\n      return result;\n    } catch (err) {\n      return {\n        valid: false,\n        message: err.message || \"Promotion validation failed\"\n      };\n    }\n  };\n\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\n  const checkHotelStatusBeforeBooking = async () => {\n    return new Promise((resolve, reject) => {\n      // Only show loading state if check takes longer than 300ms\n      let shouldShowLoading = false;\n      const loadingTimeoutId = setTimeout(() => {\n        shouldShowLoading = true;\n        setIsCheckingHotelStatus(true);\n      }, 300);\n      dispatch({\n        type: HotelActions.FETCH_DETAIL_HOTEL,\n        payload: {\n          hotelId: hotelDetail._id,\n          userId: Auth._id,\n          onSuccess: hotel => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            if (hotel.ownerStatus === \"ACTIVE\") {\n              resolve(hotel);\n            } else {\n              reject(new Error(\"Hotel is currently inactive\"));\n            }\n          },\n          onFailed: error => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(error || \"Failed to check hotel status\"));\n          },\n          onError: () => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(\"Server error while checking hotel status\"));\n          }\n        }\n      });\n    });\n  };\n  const createBooking = async () => {\n    try {\n      // Validate promotion first if there's one applied\n      const promotionValidation = await validatePromotionBeforeBooking();\n      if (!promotionValidation.valid) {\n        // Store error info for modal\n        setPromotionErrorMessage(promotionValidation.message);\n        setInvalidPromotionCode(promotionCode);\n\n        // Clear invalid promotion\n        setPromotionCode(\"\");\n        setPromotionDiscount(0);\n        setPromotionMessage(\"\");\n        setPromotionId(null);\n        sessionStorage.removeItem(\"promotionInfo\");\n\n        // Show error modal\n        setShowPromotionErrorModal(true);\n        return;\n      }\n\n      // Check hotel status\n      const hotel = await checkHotelStatusBeforeBooking();\n      console.log(\"Hotel detail fetched successfully:\", hotel);\n      const totalRoomPrice = selectedRooms.reduce((total, {\n        room,\n        amount\n      }) => total + room.price * amount * numberOfDays, 0);\n      const totalServicePrice = selectedServices.reduce((total, service) => {\n        const selectedDates = service.selectedDates || [];\n        const serviceQuantity = service.quantity * selectedDates.length;\n        return total + service.price * serviceQuantity;\n      }, 0);\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\n      const params = {\n        hotelId: hotelDetail._id,\n        checkOutDate: searchInfo.checkoutDate,\n        checkInDate: searchInfo.checkinDate,\n        totalPrice: bookingSubtotal,\n        // giá gốc\n        finalPrice: finalPrice,\n        // giá sau giảm giá\n        roomDetails: selectedRooms.map(({\n          room,\n          amount\n        }) => ({\n          room: {\n            _id: room._id\n          },\n          amount: amount\n        })),\n        serviceDetails: selectedServices.map(service => {\n          var _service$selectedDate;\n          return {\n            _id: service._id,\n            quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n            selectDate: service.selectedDates || []\n          };\n        }),\n        // Thêm promotionId và promotionDiscount nếu có\n        ...(promotionId && {\n          promotionId\n        }),\n        ...(promotionDiscount > 0 && {\n          promotionDiscount\n        })\n      };\n      console.log(\"params >> \", params);\n\n      // Helper function to save reservationId to bookingStack\n      const saveReservationIdToBookingStack = reservationId => {\n        if (reservationId) {\n          const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n          if (bookingStack.length > 0) {\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\n            sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n          }\n        }\n      };\n      try {\n        let reservationId = null;\n        const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n        if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\n        }\n        const response = await Factories.create_booking({\n          ...params,\n          reservationId\n        });\n        console.log(\"response >> \", response);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          var _response$data, _response$data$unpaid, _responseCheckout$dat;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n          saveReservationIdToBookingStack(reservationId);\n          const unpaidReservationId = reservationId;\n          const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n          console.log(\"responseCheckout >> \", responseCheckout);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n          var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n          saveReservationIdToBookingStack(reservationId);\n          const responseCheckout = await Factories.checkout_booking(reservationId);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else {\n          console.log(\"error create booking\");\n        }\n      } catch (error) {\n        console.error(\"Error create payment: \", error);\n        navigate(Routers.ErrorPage);\n      }\n    } catch (error) {\n      console.error(\"Error checking hotel status:\", error);\n      setShowModalStatusBooking(true);\n    }\n  };\n  const handleAccept = async () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      // Final validation before creating booking\n      await createBooking();\n\n      // Only clear selection if booking was successful\n      // (createBooking will handle errors and not reach this point if failed)\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n\n  // Only show loading spinner during initial load, not during re-renders\n  if (isInitialLoading || !hotelDetail && !dataRestored) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If data is restored but hotelDetail is still missing, redirect back\n  if (!hotelDetail && dataRestored) {\n    navigate(-1);\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 733,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 734,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 732,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 752,\n                          columnNumber: 29\n                        }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"...\" : \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 740,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 21\n                  }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 19\n                }, this), (isValidatingPromotion || isValidatingPromotionBeforeBooking) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-1\",\n                      role: \"status\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"visually-hidden\",\n                        children: \"Loading...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 790,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 789,\n                      columnNumber: 25\n                    }, this), \"Checking promotion validity...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-breakdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Subtotal:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(subtotal)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 19\n                }, this), promotionDiscount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: \"Discount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-success\",\n                    children: [\"-\", Utils.formatCurrency(promotionDiscount)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"booking-divider mb-2\",\n                  style: {\n                    height: \"1px\",\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                    margin: \"10px 0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 889,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    disabled: isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                    children: isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" : isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 945,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: subtotal,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 956,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionErrorModal, {\n      show: showPromotionErrorModal,\n      onClose: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n      },\n      onSelectNewPromotion: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n        setShowPromotionModal(true);\n      },\n      errorMessage: promotionErrorMessage,\n      promotionCode: invalidPromotionCode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 964,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 523,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"h+tOW0srPpRTchN3l7DrFurZg/o=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch, useAppSelector, useAppSelector, useAppSelector, useAppSelector, usePromotionValidation];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionErrorModal", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "applyPromotionCode", "validatePromotion", "clearPromotion", "applyPromotion", "selectAppliedPromotion", "selectPromotionError", "selectPromotionValidating", "selectPromotionApplying", "usePromotionValidation", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "appliedPromotion", "promotionError", "isPromotionValidating", "isPromotionApplying", "validatePromotionIfNeeded", "forceValidation", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "isValidatingPromotionBeforeBooking", "setIsValidatingPromotionBeforeBooking", "isInitialLoading", "setIsInitialLoading", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "_bookingData$hotelDet", "_bookingData$selected", "currentHotelId", "_id", "savedHotelId", "hotelId", "currentRoomsHash", "stringify", "map", "r", "roomId", "room", "amount", "sort", "savedRoomsHash", "roomsHash", "removeItem", "console", "log", "savedTime", "Date", "now", "timeDiff", "fiveMinutes", "_bookingData$hotelDet2", "_bookingData$selected2", "setItem", "calculateNumberOfDays", "checkIn", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "code", "discount", "formatCurrency", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "showPromotionErrorModal", "setShowPromotionErrorModal", "promotionErrorMessage", "setPromotionErrorMessage", "invalidPromotionCode", "setInvalidPromotionCode", "handleApplyPromotionFromModal", "promotionData", "remainingUses", "message", "validatePromotionBeforeBooking", "valid", "result", "err", "checkHotelStatusBeforeBooking", "Promise", "resolve", "reject", "shouldShowLoading", "loadingTimeoutId", "setTimeout", "FETCH_DETAIL_HOTEL", "userId", "onSuccess", "hotel", "clearTimeout", "ownerStatus", "Error", "onFailed", "error", "onError", "createBooking", "promotionValidation", "bookingSubtotal", "params", "checkOutDate", "checkInDate", "totalPrice", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "data", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "ErrorPage", "handleAccept", "handleConfirmBooking", "style", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "disabled", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onSelectNewPromotion", "errorMessage", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\nimport {\r\n  applyPromotionCode,\r\n  validatePromotion,\r\n  clearPromotion,\r\n  applyPromotion,\r\n  selectAppliedPromotion,\r\n  selectPromotionError,\r\n  selectPromotionValidating,\r\n  selectPromotionApplying,\r\n} from \"../../../redux/promotion/promotionSlice\";\r\nimport usePromotionValidation from \"../../../hooks/usePromotionValidation\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Redux promotion state\r\n  const appliedPromotion = useAppSelector(selectAppliedPromotion);\r\n  const promotionError = useAppSelector(selectPromotionError);\r\n  const isPromotionValidating = useAppSelector(selectPromotionValidating);\r\n  const isPromotionApplying = useAppSelector(selectPromotionApplying);\r\n\r\n  // Use promotion validation hook\r\n  const { validatePromotionIfNeeded, forceValidation } = usePromotionValidation();\r\n\r\n  // Legacy state for backward compatibility (will be removed gradually)\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\r\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n    setIsInitialLoading(false);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        // Check if this is a new booking (different hotel or rooms)\r\n        const currentHotelId = bookingData.hotelDetail?._id;\r\n        const savedHotelId = promo.hotelId;\r\n        const currentRoomsHash = JSON.stringify(bookingData.selectedRooms?.map(r => ({ roomId: r.room._id, amount: r.amount })).sort());\r\n        const savedRoomsHash = promo.roomsHash;\r\n\r\n        if (currentHotelId !== savedHotelId || currentRoomsHash !== savedRoomsHash) {\r\n          // This is a new booking, clear old promotion\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n          console.log(\"🆕 New booking detected, cleared old promotion\");\r\n          return;\r\n        }\r\n\r\n        // Check if promotion was saved more than 5 minutes ago\r\n        const savedTime = promo.savedTime || Date.now();\r\n        const timeDiff = Date.now() - savedTime;\r\n        const fiveMinutes = 5 * 60 * 1000;\r\n\r\n        if (timeDiff > fiveMinutes) {\r\n          // Auto-validate if promotion is old\r\n          console.log(\"Promotion is old, auto-validating...\");\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(\"Validating promotion...\");\r\n          setPromotionId(promo.promotionId || null);\r\n        } else {\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(promo.promotionMessage || \"\");\r\n          setPromotionId(promo.promotionId || null);\r\n          console.log(\"🔄 Restored promotion for same booking:\", promo.promotionCode);\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n          savedTime: Date.now(), // Add timestamp for validation\r\n          // Save booking context to detect new bookings\r\n          hotelId: bookingData.hotelDetail?._id,\r\n          roomsHash: JSON.stringify(bookingData.selectedRooms?.map(r => ({ roomId: r.room._id, amount: r.amount })).sort())\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\r\n\r\n\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes using Redux\r\n  useEffect(() => {\r\n    if (!dataRestored) return;\r\n\r\n    // If we have applied promotion from Redux, sync with legacy state\r\n    if (appliedPromotion.code) {\r\n      setPromotionCode(appliedPromotion.code);\r\n      setPromotionDiscount(appliedPromotion.discount);\r\n      setPromotionId(appliedPromotion.promotionId);\r\n      setPromotionMessage(`Promotion applied: -${Utils.formatCurrency(appliedPromotion.discount)}`);\r\n    }\r\n\r\n    // Validate promotion if subtotal changes significantly\r\n    if (appliedPromotion.code && subtotal > 0) {\r\n      validatePromotionIfNeeded(subtotal);\r\n    }\r\n  }, [dataRestored, subtotal, appliedPromotion, validatePromotionIfNeeded]);\r\n\r\n  // Sync Redux promotion error with legacy state\r\n  useEffect(() => {\r\n    if (promotionError) {\r\n      setPromotionMessage(promotionError);\r\n      // Clear legacy promotion state if there's an error\r\n      setPromotionCode(\"\");\r\n      setPromotionDiscount(0);\r\n      setPromotionId(null);\r\n      sessionStorage.removeItem(\"promotionInfo\");\r\n    }\r\n  }, [promotionError]);\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\r\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\r\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    if (promotionData.code) {\r\n      // Apply promotion using Redux\r\n      dispatch(applyPromotion({\r\n        code: promotionData.code,\r\n        discount: promotionData.discount,\r\n        promotionId: promotionData.promotionId,\r\n        remainingUses: promotionData.remainingUses\r\n      }));\r\n    } else {\r\n      // Clear promotion using Redux\r\n      dispatch(clearPromotion());\r\n    }\r\n\r\n    // Update legacy state for backward compatibility\r\n    setPromotionCode(promotionData.code || \"\");\r\n    setPromotionDiscount(promotionData.discount || 0);\r\n    setPromotionMessage(promotionData.message || \"\");\r\n    setPromotionId(promotionData.promotionId || null);\r\n  };\r\n\r\n  // Function to validate promotion before booking using Redux\r\n  const validatePromotionBeforeBooking = async () => {\r\n    if (!appliedPromotion.code) {\r\n      return { valid: true }; // No promotion to validate\r\n    }\r\n\r\n    try {\r\n      // Use Redux force validation\r\n      const result = await forceValidation(subtotal);\r\n      return result;\r\n    } catch (err) {\r\n      return {\r\n        valid: false,\r\n        message: err.message || \"Promotion validation failed\"\r\n      };\r\n    }\r\n  };\r\n\r\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\r\n  const checkHotelStatusBeforeBooking = async () => {\r\n    return new Promise((resolve, reject) => {\r\n      // Only show loading state if check takes longer than 300ms\r\n      let shouldShowLoading = false;\r\n      const loadingTimeoutId = setTimeout(() => {\r\n        shouldShowLoading = true;\r\n        setIsCheckingHotelStatus(true);\r\n      }, 300);\r\n\r\n      dispatch({\r\n        type: HotelActions.FETCH_DETAIL_HOTEL,\r\n        payload: {\r\n          hotelId: hotelDetail._id,\r\n          userId: Auth._id,\r\n          onSuccess: (hotel) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            if (hotel.ownerStatus === \"ACTIVE\") {\r\n              resolve(hotel);\r\n            } else {\r\n              reject(new Error(\"Hotel is currently inactive\"));\r\n            }\r\n          },\r\n          onFailed: (error) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(error || \"Failed to check hotel status\"));\r\n          },\r\n          onError: () => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(\"Server error while checking hotel status\"));\r\n          }\r\n        },\r\n      });\r\n    });\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    try {\r\n      // Validate promotion first if there's one applied\r\n      const promotionValidation = await validatePromotionBeforeBooking();\r\n      if (!promotionValidation.valid) {\r\n        // Store error info for modal\r\n        setPromotionErrorMessage(promotionValidation.message);\r\n        setInvalidPromotionCode(promotionCode);\r\n\r\n        // Clear invalid promotion\r\n        setPromotionCode(\"\");\r\n        setPromotionDiscount(0);\r\n        setPromotionMessage(\"\");\r\n        setPromotionId(null);\r\n        sessionStorage.removeItem(\"promotionInfo\");\r\n\r\n        // Show error modal\r\n        setShowPromotionErrorModal(true);\r\n        return;\r\n      }\r\n\r\n      // Check hotel status\r\n      const hotel = await checkHotelStatusBeforeBooking();\r\n      console.log(\"Hotel detail fetched successfully:\", hotel);\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const bookingSubtotal = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: bookingSubtotal, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n    } catch (error) {\r\n      console.error(\"Error checking hotel status:\", error);\r\n      setShowModalStatusBooking(true);\r\n    }\r\n  };\r\n\r\n  const handleAccept = async () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      // Final validation before creating booking\r\n      await createBooking();\r\n\r\n      // Only clear selection if booking was successful\r\n      // (createBooking will handle errors and not reach this point if failed)\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n\r\n\r\n  // Only show loading spinner during initial load, not during re-renders\r\n  if (isInitialLoading || (!hotelDetail && !dataRestored)) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If data is restored but hotelDetail is still missing, redirect back\r\n  if (!hotelDetail && dataRestored) {\r\n    navigate(-1);\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card\r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{\r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\",\r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                            disabled={isValidatingPromotion || isValidatingPromotionBeforeBooking}\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            {isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"...\" : \"Remove\"}\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{\r\n                      border: \"2px dashed rgba(255,255,255,0.3)\",\r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{\r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                    disabled={isValidatingPromotion || isValidatingPromotionBeforeBooking}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : (promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\")}\r\n                  </Button>\r\n\r\n                  {/* Validation status indicator */}\r\n                  {(isValidatingPromotion || isValidatingPromotionBeforeBooking) && (\r\n                    <div className=\"text-center mt-2\">\r\n                      <small className=\"text-info\">\r\n                        <div className=\"spinner-border spinner-border-sm me-1\" role=\"status\">\r\n                          <span className=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                        Checking promotion validity...\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Price breakdown section */}\r\n                <div className=\"price-breakdown\">\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <span>Subtotal:</span>\r\n                    <span className=\"fw-bold\">{Utils.formatCurrency(subtotal)}</span>\r\n                  </div>\r\n\r\n                  {promotionDiscount > 0 && (\r\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <span className=\"text-success\">Discount:</span>\r\n                      <span className=\"fw-bold text-success\">-{Utils.formatCurrency(promotionDiscount)}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div\r\n                    className=\"booking-divider mb-2\"\r\n                    style={{\r\n                      height: \"1px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      margin: \"10px 0\",\r\n                    }}\r\n                  ></div>\r\n\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                      disabled={isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking}\r\n                    >\r\n                      {isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" :\r\n                       isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"}\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={subtotal}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Error Modal */}\r\n      <PromotionErrorModal\r\n        show={showPromotionErrorModal}\r\n        onClose={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n        }}\r\n        onSelectNewPromotion={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n          setShowPromotionModal(true);\r\n        }}\r\n        errorMessage={promotionErrorMessage}\r\n        promotionCode={invalidPromotionCode}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SACEC,kBAAkB,EAClBC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,EACzBC,uBAAuB,QAClB,yCAAyC;AAChD,OAAOC,sBAAsB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAM+C,IAAI,GAAGzB,cAAc,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAG3B,cAAc,CACrC0B,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAG7B,cAAc,CACtC0B,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAG/B,cAAc,CAC7C0B,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGjC,cAAc,CACxC0B,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAMwC,QAAQ,GAAGnC,cAAc,CAAC,CAAC;EACjC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM6D,gBAAgB,GAAGvC,cAAc,CAACY,sBAAsB,CAAC;EAC/D,MAAM4B,cAAc,GAAGxC,cAAc,CAACa,oBAAoB,CAAC;EAC3D,MAAM4B,qBAAqB,GAAGzC,cAAc,CAACc,yBAAyB,CAAC;EACvE,MAAM4B,mBAAmB,GAAG1C,cAAc,CAACe,uBAAuB,CAAC;;EAEnE;EACA,MAAM;IAAE4B,yBAAyB;IAAEC;EAAgB,CAAC,GAAG5B,sBAAsB,CAAC,CAAC;;EAE/E;EACA,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC;IAC7CoD,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCsB,UAAU,EAAE5B;EACd,CAAC,CAAC;EAEF,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACoF,kCAAkC,EAAEC,qCAAqC,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACnG,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuF,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DjB,cAAc,CAACkB,cAAc,CAAC;;MAE9B;MACApC,QAAQ,CAAC;QACPqC,IAAI,EAAEpE,aAAa,CAACqE,mBAAmB;QACvCC,OAAO,EAAE;UACP7C,aAAa,EAAE0C,cAAc,CAAC1C,aAAa;UAC3CE,gBAAgB,EAAEwC,cAAc,CAACxC,gBAAgB;UACjDE,WAAW,EAAEsC,cAAc,CAACtC;QAC9B;MACF,CAAC,CAAC;IACJ;IACAuB,eAAe,CAAC,IAAI,CAAC;IACrBQ,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAAC7B,QAAQ,CAAC,CAAC;;EAEd;EACAzD,SAAS,CAAC,MAAM;IACd,IAAI6E,YAAY,EAAE;MAChB,MAAMoB,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA;QACT;QACA,MAAMC,cAAc,IAAAF,qBAAA,GAAGxB,WAAW,CAACnB,WAAW,cAAA2C,qBAAA,uBAAvBA,qBAAA,CAAyBG,GAAG;QACnD,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;QAClC,MAAMC,gBAAgB,GAAGhB,IAAI,CAACiB,SAAS,EAAAN,qBAAA,GAACzB,WAAW,CAACvB,aAAa,cAAAgD,qBAAA,uBAAzBA,qBAAA,CAA2BO,GAAG,CAACC,CAAC,KAAK;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;QAC/H,MAAMC,cAAc,GAAGf,KAAK,CAACgB,SAAS;QAEtC,IAAIb,cAAc,KAAKE,YAAY,IAAIE,gBAAgB,KAAKQ,cAAc,EAAE;UAC1E;UACAtB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;UAC1CC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7D;QACF;;QAEA;QACA,MAAMC,SAAS,GAAGpB,KAAK,CAACoB,SAAS,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QACvC,MAAMI,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;QAEjC,IAAID,QAAQ,GAAGC,WAAW,EAAE;UAC1B;UACAN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnDjD,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC,yBAAyB,CAAC;UAC9CE,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;QAC3C,CAAC,MAAM;UACLL,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC0B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE,CAAC;UACjDG,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;UACzC2C,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEnB,KAAK,CAAC/B,aAAa,CAAC;QAC7E;MACF;IACF;EACF,CAAC,EAAE,CAACW,YAAY,EAAEH,WAAW,CAACnB,WAAW,EAAEmB,WAAW,CAACvB,aAAa,CAAC,CAAC;;EAEtE;EACAnD,SAAS,CAAC,MAAM;IACd,IAAI6E,YAAY,EAAE;MAAA,IAAA6C,sBAAA,EAAAC,sBAAA;MAAE;MAClBjC,cAAc,CAACkC,OAAO,CACpB,eAAe,EACfpC,IAAI,CAACiB,SAAS,CAAC;QACbvC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE,WAAW;QACX6C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QAAE;QACvB;QACAhB,OAAO,GAAAmB,sBAAA,GAAEhD,WAAW,CAACnB,WAAW,cAAAmE,sBAAA,uBAAvBA,sBAAA,CAAyBrB,GAAG;QACrCY,SAAS,EAAEzB,IAAI,CAACiB,SAAS,EAAAkB,sBAAA,GAACjD,WAAW,CAACvB,aAAa,cAAAwE,sBAAA,uBAAzBA,sBAAA,CAA2BjB,GAAG,CAACC,CAAC,KAAK;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAClH,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAAC7C,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEK,YAAY,EAAEH,WAAW,CAACnB,WAAW,EAAEmB,WAAW,CAACvB,aAAa,CAAC,CAAC;;EAIvI;EACA,MAAMA,aAAa,GAAGuB,WAAW,CAACvB,aAAa;EAC/C,MAAME,gBAAgB,GAAGqB,WAAW,CAACrB,gBAAgB;EACrD,MAAME,WAAW,GAAGmB,WAAW,CAACnB,WAAW;EAC3C,MAAMqB,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMiD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIR,IAAI,CAAC1C,UAAU,CAACmD,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIV,IAAI,CAAC1C,UAAU,CAACqD,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGF,OAAO,CAAC;IAC7C,MAAMO,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGV,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMW,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;IAAE7B,IAAI;IAAEC;EAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;EACD,MAAMK,iBAAiB,GAAGvF,gBAAgB,CAACoF,MAAM,CAAC,CAACC,KAAK,EAAEG,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;IAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGT,cAAc,GAAGI,iBAAiB;EACnD,MAAMM,UAAU,GAAGf,IAAI,CAACgB,GAAG,CAACF,QAAQ,GAAG7E,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACApE,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6E,YAAY,EAAE;;IAEnB;IACA,IAAIjB,gBAAgB,CAACwF,IAAI,EAAE;MACzBjF,gBAAgB,CAACP,gBAAgB,CAACwF,IAAI,CAAC;MACvC/E,oBAAoB,CAACT,gBAAgB,CAACyF,QAAQ,CAAC;MAC/C5E,cAAc,CAACb,gBAAgB,CAACY,WAAW,CAAC;MAC5CD,mBAAmB,CAAC,uBAAuBhD,KAAK,CAAC+H,cAAc,CAAC1F,gBAAgB,CAACyF,QAAQ,CAAC,EAAE,CAAC;IAC/F;;IAEA;IACA,IAAIzF,gBAAgB,CAACwF,IAAI,IAAIH,QAAQ,GAAG,CAAC,EAAE;MACzCjF,yBAAyB,CAACiF,QAAQ,CAAC;IACrC;EACF,CAAC,EAAE,CAACpE,YAAY,EAAEoE,QAAQ,EAAErF,gBAAgB,EAAEI,yBAAyB,CAAC,CAAC;;EAEzE;EACAhE,SAAS,CAAC,MAAM;IACd,IAAI6D,cAAc,EAAE;MAClBU,mBAAmB,CAACV,cAAc,CAAC;MACnC;MACAM,gBAAgB,CAAC,EAAE,CAAC;MACpBE,oBAAoB,CAAC,CAAC,CAAC;MACvBI,cAAc,CAAC,IAAI,CAAC;MACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;IAC5C;EACF,CAAC,EAAE,CAACrD,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM0F,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMhE,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACiE,GAAG,CAAC,CAAC;MAClB9D,cAAc,CAACkC,OAAO,CAAC,cAAc,EAAEpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAAC,CAAC;IACtE;IACA/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMiG,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACEnH,OAAA;MAAKoH,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACnD,GAAG,CAAC,CAACoD,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGL,MAAM,gBACZnH,OAAA,CAAC9B,MAAM;QAAakJ,SAAS,EAAC;MAAa,GAA9BI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9C5H,OAAA,CAAC7B,SAAS;QAAaiJ,SAAS,EAAC;MAAM,GAAvBI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyK,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAAC2K,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAAC6K,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACA,MAAM+K,6BAA6B,GAAIC,aAAa,IAAK;IACvD,IAAIA,aAAa,CAAC3B,IAAI,EAAE;MACtB;MACA3F,QAAQ,CAACzB,cAAc,CAAC;QACtBoH,IAAI,EAAE2B,aAAa,CAAC3B,IAAI;QACxBC,QAAQ,EAAE0B,aAAa,CAAC1B,QAAQ;QAChC7E,WAAW,EAAEuG,aAAa,CAACvG,WAAW;QACtCwG,aAAa,EAAED,aAAa,CAACC;MAC/B,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACAvH,QAAQ,CAAC1B,cAAc,CAAC,CAAC,CAAC;IAC5B;;IAEA;IACAoC,gBAAgB,CAAC4G,aAAa,CAAC3B,IAAI,IAAI,EAAE,CAAC;IAC1C/E,oBAAoB,CAAC0G,aAAa,CAAC1B,QAAQ,IAAI,CAAC,CAAC;IACjD9E,mBAAmB,CAACwG,aAAa,CAACE,OAAO,IAAI,EAAE,CAAC;IAChDxG,cAAc,CAACsG,aAAa,CAACvG,WAAW,IAAI,IAAI,CAAC;EACnD,CAAC;;EAED;EACA,MAAM0G,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,IAAI,CAACtH,gBAAgB,CAACwF,IAAI,EAAE;MAC1B,OAAO;QAAE+B,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IAC1B;IAEA,IAAI;MACF;MACA,MAAMC,MAAM,GAAG,MAAMnH,eAAe,CAACgF,QAAQ,CAAC;MAC9C,OAAOmC,MAAM;IACf,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ,OAAO;QACLF,KAAK,EAAE,KAAK;QACZF,OAAO,EAAEI,GAAG,CAACJ,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMK,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,MAAMC,gBAAgB,GAAGC,UAAU,CAAC,MAAM;QACxCF,iBAAiB,GAAG,IAAI;QACxBxG,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;MAEPzB,QAAQ,CAAC;QACPqC,IAAI,EAAEnE,YAAY,CAACkK,kBAAkB;QACrC7F,OAAO,EAAE;UACPO,OAAO,EAAEhD,WAAW,CAAC8C,GAAG;UACxByF,MAAM,EAAEhJ,IAAI,CAACuD,GAAG;UAChB0F,SAAS,EAAGC,KAAK,IAAK;YACpBC,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAID,iBAAiB,EAAE;cACrBxG,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACA,IAAI8G,KAAK,CAACE,WAAW,KAAK,QAAQ,EAAE;cAClCV,OAAO,CAACQ,KAAK,CAAC;YAChB,CAAC,MAAM;cACLP,MAAM,CAAC,IAAIU,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD;UACF,CAAC;UACDC,QAAQ,EAAGC,KAAK,IAAK;YACnBJ,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAID,iBAAiB,EAAE;cACrBxG,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAuG,MAAM,CAAC,IAAIU,KAAK,CAACE,KAAK,IAAI,8BAA8B,CAAC,CAAC;UAC5D,CAAC;UACDC,OAAO,EAAEA,CAAA,KAAM;YACbL,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAID,iBAAiB,EAAE;cACrBxG,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAuG,MAAM,CAAC,IAAIU,KAAK,CAAC,0CAA0C,CAAC,CAAC;UAC/D;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,mBAAmB,GAAG,MAAMtB,8BAA8B,CAAC,CAAC;MAClE,IAAI,CAACsB,mBAAmB,CAACrB,KAAK,EAAE;QAC9B;QACAR,wBAAwB,CAAC6B,mBAAmB,CAACvB,OAAO,CAAC;QACrDJ,uBAAuB,CAAC3G,aAAa,CAAC;;QAEtC;QACAC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,oBAAoB,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,EAAE,CAAC;QACvBE,cAAc,CAAC,IAAI,CAAC;QACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;;QAE1C;QACAuD,0BAA0B,CAAC,IAAI,CAAC;QAChC;MACF;;MAEA;MACA,MAAMuB,KAAK,GAAG,MAAMV,6BAA6B,CAAC,CAAC;MACnDnE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE4E,KAAK,CAAC;MAClD,MAAMxD,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;QAAE7B,IAAI;QAAEC;MAAO,CAAC,KACtB4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EAC5C,CACF,CAAC;MAED,MAAMK,iBAAiB,GAAGvF,gBAAgB,CAACoF,MAAM,CAC/C,CAACC,KAAK,EAAEG,OAAO,KAAK;QAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;QAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;MAChD,CAAC,EACD,CACF,CAAC;MAED,MAAM0D,eAAe,GAAGjE,cAAc,GAAGI,iBAAiB;MAE1D,MAAM8D,MAAM,GAAG;QACbnG,OAAO,EAAEhD,WAAW,CAAC8C,GAAG;QACxBsG,YAAY,EAAE/H,UAAU,CAACqD,YAAY;QACrC2E,WAAW,EAAEhI,UAAU,CAACmD,WAAW;QACnC8E,UAAU,EAAEJ,eAAe;QAAE;QAC7BvD,UAAU,EAAEA,UAAU;QAAE;QACxB4D,WAAW,EAAE3J,aAAa,CAACuD,GAAG,CAAC,CAAC;UAAEG,IAAI;UAAEC;QAAO,CAAC,MAAM;UACpDD,IAAI,EAAE;YACJR,GAAG,EAAEQ,IAAI,CAACR;UACZ,CAAC;UACDS,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;QACHiG,cAAc,EAAE1J,gBAAgB,CAACqD,GAAG,CAAEmC,OAAO;UAAA,IAAAmE,qBAAA;UAAA,OAAM;YACjD3G,GAAG,EAAEwC,OAAO,CAACxC,GAAG;YAChB2C,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAAgE,qBAAA,GAAAnE,OAAO,CAACC,aAAa,cAAAkE,qBAAA,uBAArBA,qBAAA,CAAuBpH,MAAM,KAAI,CAAC,CAAC;YACzDqH,UAAU,EAAEpE,OAAO,CAACC,aAAa,IAAI;UACvC,CAAC;QAAA,CAAC,CAAC;QACH;QACA,IAAItE,WAAW,IAAI;UAAEA;QAAY,CAAC,CAAC;QACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;UAAEA;QAAkB,CAAC;MACpD,CAAC;MAED+C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsF,MAAM,CAAC;;MAEjC;MACA,MAAMQ,+BAA+B,GAAIC,aAAa,IAAK;QACzD,IAAIA,aAAa,EAAE;UACjB,MAAM5H,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;UAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;YAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuH,aAAa,GAAGA,aAAa;YACnEzH,cAAc,CAACkC,OAAO,CAAC,cAAc,EAAEpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAAC,CAAC;UACtE;QACF;MACF,CAAC;MACD,IAAI;QACF,IAAI4H,aAAa,GAAG,IAAI;QACxB,MAAM5H,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuH,aAAa,EAAE;UAClFA,aAAa,GAAG5H,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACuH,aAAa;QACrE;QACA,MAAMC,QAAQ,GAAG,MAAM5L,SAAS,CAAC6L,cAAc,CAAC;UAAE,GAAGX,MAAM;UAAES;QAAc,CAAC,CAAC;QAC7EhG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgG,QAAQ,CAAC;QACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAEM,IAAI,cAAAH,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBI,iBAAiB,cAAAH,qBAAA,uBAAjCA,qBAAA,CAAmCnH,GAAG;UACtD6G,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMS,mBAAmB,GAAGT,aAAa;UACzC,MAAMU,gBAAgB,GAAG,MAAMrM,SAAS,CAACsM,gBAAgB,CACvDF,mBACF,CAAC;UACDzG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyG,gBAAgB,CAAC;UACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAJ,qBAAA,GAAhBI,gBAAgB,CAAEH,IAAI,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBO,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM,IAAI,CAAAX,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAc,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACnCnB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAgB,eAAA,GAARhB,QAAQ,CAAEM,IAAI,cAAAU,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BhI,GAAG;UAChD6G,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMU,gBAAgB,GAAG,MAAMrM,SAAS,CAACsM,gBAAgB,CACvDX,aACF,CAAC;UACD,MAAMY,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEH,IAAI,cAAAY,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM;UACL5G,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC;MACF,CAAC,CAAC,OAAOiF,KAAK,EAAE;QACdlF,OAAO,CAACkF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C7I,QAAQ,CAACxC,OAAO,CAACwN,SAAS,CAAC;MAC7B;IACR,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdlF,OAAO,CAACkF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDxJ,yBAAyB,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAM4L,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMjG,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;MAAE7B,IAAI;MAAEC;IAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtB;MACA,MAAM+D,aAAa,CAAC,CAAC;;MAErB;MACA;MACA9I,QAAQ,CAAC;QACPqC,IAAI,EAAEpE,aAAa,CAACqE,mBAAmB;QACvCC,OAAO,EAAE;UACP7C,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMmL,oBAAoB,GAAGA,CAAA,KAAM;IACjCrE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,IAAIhF,gBAAgB,IAAK,CAAC9B,WAAW,IAAI,CAACsB,YAAa,EAAE;IACvD,oBACEtC,OAAA;MACEoH,SAAS,EAAC,kDAAkD;MAC5DgF,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAhF,QAAA,eAE3BrH,OAAA;QAAKoH,SAAS,EAAC,6BAA6B;QAACkF,IAAI,EAAC,QAAQ;QAAAjF,QAAA,eACxDrH,OAAA;UAAMoH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC5G,WAAW,IAAIsB,YAAY,EAAE;IAChCrB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,IAAI;EACb;EAEA,oBACEjB,OAAA;IACEoH,SAAS,EAAC,+BAA+B;IACzCgF,KAAK,EAAE;MACLG,eAAe,EAAE,OAAOjO,MAAM,GAAG;MACjCkO,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAApF,QAAA,gBAEFrH,OAAA,CAACzB,MAAM;MAAAkJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV5H,OAAA;MACEoH,SAAS,EAAC,8EAA8E;MACxFgF,KAAK,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAtF,QAAA,gBAErDrH,OAAA,CAACrC,SAAS;QAACyJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBrH,OAAA,CAACpC,GAAG;UAACwJ,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCrH,OAAA,CAACnC,GAAG;YAAC+O,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxF,QAAA,eAChBrH,OAAA,CAAClC,IAAI;cACHsJ,SAAS,EAAC,yBAAyB;cACnCgF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA5F,QAAA,gBAEFrH,OAAA;gBACEoH,SAAS,EAAC,YAAY;gBACtBgF,KAAK,EAAE;kBACLc,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAA9F,QAAA,eAEFrH,OAAA,CAACkH,UAAU;kBAACC,MAAM,EAAEnG,WAAW,CAACoM;gBAAK;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEN5H,OAAA;gBAAIoH,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAAlH,qBAAA,GAC5Ba,WAAW,CAACqM,SAAS,cAAAlN,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEL5H,OAAA;gBAAGoH,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAAjH,oBAAA,GACpCY,WAAW,CAACsM,OAAO,cAAAlN,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJ5H,OAAA;gBACEoH,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP5H,OAAA;gBAAIoH,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7C5H,OAAA,CAACpC,GAAG;gBAACwJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrH,OAAA,CAACnC,GAAG;kBAAC2P,EAAE,EAAE,CAAE;kBAAAnG,QAAA,eACTrH,OAAA;oBAAKoH,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBrH,OAAA;sBACEoH,SAAS,EAAC,oBAAoB;sBAC9BgF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAApG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5H,OAAA;sBAAKoH,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBrI,KAAK,CAAC0O,OAAO,CAACrL,UAAU,CAACmD,WAAW,EAAE,CAAC;oBAAC;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA,CAACnC,GAAG;kBAAC2P,EAAE,EAAE,CAAE;kBAAAnG,QAAA,eACTrH,OAAA;oBAAKoH,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBrH,OAAA;sBACEoH,SAAS,EAAC,oBAAoB;sBAC9BgF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAApG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5H,OAAA;sBAAKoH,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBrI,KAAK,CAAC0O,OAAO,CAACrL,UAAU,CAACqD,YAAY,EAAE,CAAC;oBAAC;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5H,OAAA;gBAAKoH,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrH,OAAA;kBAAKoH,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDrH,OAAA;oBAAAqH,QAAA,EAAM;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClC5H,OAAA;oBAAMoH,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAErB,YAAY,EAAC,QAAM;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN5H,OAAA;kBAAKoH,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDrH,OAAA;oBAAAqH,QAAA,EAAM;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpC5H,OAAA;oBAAMoH,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtBhF,UAAU,CAACsL,MAAM,EAAC,YAAU,EAACtL,UAAU,CAACuL,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5H,OAAA;gBACEoH,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP5H,OAAA;gBAAKoH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCrH,OAAA;kBAAIoH,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErChH,aAAa,CAACuD,GAAG,CAAC,CAAC;kBAAEG,IAAI;kBAAEC;gBAAO,CAAC,kBAClCvE,OAAA;kBAEEoH,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElErH,OAAA;oBAAAqH,QAAA,GACG9C,MAAM,EAAC,KAAG,EAACD,IAAI,CAACuJ,IAAI,EAAC,IAAE,EAAC7H,YAAY,EAAC,SACxC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP5H,OAAA;oBAAMoH,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBrI,KAAK,CAAC+H,cAAc,CACnBzC,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YACxB;kBAAC;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFtD,IAAI,CAACR,GAAG;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEF5H,OAAA;kBAAKoH,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBrH,OAAA;oBACEoH,SAAS,EAAC,gCAAgC;oBAC1CgF,KAAK,EAAE;sBAAE0B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAE/G,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL9G,gBAAgB,CAACuC,MAAM,GAAG,CAAC,iBAC1BrD,OAAA;gBAAKoH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCrH,OAAA;kBAAIoH,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1C9G,gBAAgB,CAACqD,GAAG,CAAEmC,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;kBACzC,MAAM2K,YAAY,GAAG1H,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACExG,OAAA;oBAEEoH,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElErH,OAAA;sBAAAqH,QAAA,GACGf,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAACuH,IAAI,EAAC,IACnC,EAACtH,aAAa,CAAClD,MAAM,EAAC,SACxB;oBAAA;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP5H,OAAA;sBAAMoH,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBrI,KAAK,CAAC+H,cAAc,CAACiH,YAAY;oBAAC;sBAAAvG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFtB,OAAO,CAACxC,GAAG;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEF5H,OAAA;kBAAKoH,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBrH,OAAA;oBACEoH,SAAS,EAAC,gCAAgC;oBAC1CgF,KAAK,EAAE;sBAAE0B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACb7M,QAAQ,CAAC;wBACPqC,IAAI,EAAEpE,aAAa,CAACqE,mBAAmB;wBACvCC,OAAO,EAAE;0BACP7C,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAoG,QAAA,EACH;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAED5H,OAAA;gBACEoH,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP5H,OAAA;gBAAKoH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpCxF,iBAAiB,GAAG,CAAC,gBACpB7B,OAAA,CAAClC,IAAI;kBACHsJ,SAAS,EAAC,wBAAwB;kBAClCgF,KAAK,EAAE;oBACLU,eAAe,EAAE,wBAAwB;oBACzCmB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAA7G,QAAA,eAEFrH,OAAA,CAAClC,IAAI,CAACqQ,IAAI;oBAAC/G,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBrH,OAAA;sBAAKoH,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChErH,OAAA;wBAAAqH,QAAA,gBACErH,OAAA;0BAAKoH,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxCrH,OAAA,CAAC5B,KAAK;4BAACgJ,SAAS,EAAC;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvC5H,OAAA;4BAAMoH,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAE1F;0BAAa;4BAAA8F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACN5H,OAAA;0BAAOoH,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAACrI,KAAK,CAAC+H,cAAc,CAAClF,iBAAiB,CAAC;wBAAA;0BAAA4F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN5H,OAAA,CAAChC,MAAM;wBACLoQ,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAMxF,6BAA6B,CAAC;0BAC3C1B,IAAI,EAAE,EAAE;0BACRC,QAAQ,EAAE,CAAC;0BACX4B,OAAO,EAAE,EAAE;0BACXzG,WAAW,EAAE;wBACf,CAAC,CAAE;wBACHmF,SAAS,EAAC,2BAA2B;wBACrCkH,QAAQ,EAAE9L,qBAAqB,IAAII,kCAAmC;wBAAAyE,QAAA,gBAEtErH,OAAA,CAAC3B,OAAO;0BAAC+I,SAAS,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC3BpF,qBAAqB,IAAII,kCAAkC,GAAG,KAAK,GAAG,QAAQ;sBAAA;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEP5H,OAAA;kBAAKoH,SAAS,EAAC,uBAAuB;kBAACgF,KAAK,EAAE;oBAC5C8B,MAAM,EAAE,kCAAkC;oBAC1CnB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAAzF,QAAA,gBACArH,OAAA,CAAC5B,KAAK;oBAACgJ,SAAS,EAAC,iBAAiB;oBAACiH,IAAI,EAAE;kBAAG;oBAAA5G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/C5H,OAAA;oBAAKoH,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGD5H,OAAA,CAAChC,MAAM;kBACLoQ,OAAO,EAAC,eAAe;kBACvBhH,SAAS,EAAC,wDAAwD;kBAClE2G,OAAO,EAAEA,CAAA,KAAM/F,qBAAqB,CAAC,IAAI,CAAE;kBAC3CoE,KAAK,EAAE;oBACLmC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBxB,OAAO,EAAE;kBACX,CAAE;kBACFsB,QAAQ,EAAE9L,qBAAqB,IAAII,kCAAmC;kBAAAyE,QAAA,gBAEtErH,OAAA,CAAC5B,KAAK;oBAACgJ,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzBpF,qBAAqB,IAAII,kCAAkC,GAAG,eAAe,GAAIf,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAmB;gBAAA;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5I,CAAC,EAGR,CAACpF,qBAAqB,IAAII,kCAAkC,kBAC3D5C,OAAA;kBAAKoH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BrH,OAAA;oBAAOoH,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAC1BrH,OAAA;sBAAKoH,SAAS,EAAC,uCAAuC;sBAACkF,IAAI,EAAC,QAAQ;sBAAAjF,QAAA,eAClErH,OAAA;wBAAMoH,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,EAAC;sBAAU;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,kCAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN5H,OAAA;gBAAKoH,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrH,OAAA;kBAAKoH,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrErH,OAAA;oBAAAqH,QAAA,EAAM;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtB5H,OAAA;oBAAMoH,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAErI,KAAK,CAAC+H,cAAc,CAACL,QAAQ;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,EAEL/F,iBAAiB,GAAG,CAAC,iBACpB7B,OAAA;kBAAKoH,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrErH,OAAA;oBAAMoH,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C5H,OAAA;oBAAMoH,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,GAAC,EAACrI,KAAK,CAAC+H,cAAc,CAAClF,iBAAiB,CAAC;kBAAA;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CACN,eAED5H,OAAA;kBACEoH,SAAS,EAAC,sBAAsB;kBAChCgF,KAAK,EAAE;oBACLC,MAAM,EAAE,KAAK;oBACbS,eAAe,EAAE,uBAAuB;oBACxCS,MAAM,EAAE;kBACV;gBAAE;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEP5H,OAAA;kBAAKoH,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChErH,OAAA;oBAAIoH,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAACrI,KAAK,CAAC+H,cAAc,CAACJ,UAAU,CAAC;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN5H,OAAA;kBAAKoH,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN5H,OAAA,CAACnC,GAAG;YAAC+O,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxF,QAAA,eAChBrH,OAAA,CAAClC,IAAI;cACHsJ,SAAS,EAAC,WAAW;cACrBgF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfyB,KAAK,EAAE;cACT,CAAE;cAAApH,QAAA,gBAEFrH,OAAA;gBAAIoH,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhD5H,OAAA,CAACjC,IAAI;gBAAAsJ,QAAA,gBACHrH,OAAA,CAACjC,IAAI,CAAC2Q,KAAK;kBAACtH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BrH,OAAA,CAACjC,IAAI,CAAC4Q,KAAK;oBAAAtH,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClC5H,OAAA,CAACjC,IAAI,CAAC6Q,OAAO;oBACXrL,IAAI,EAAC,MAAM;oBACXsL,KAAK,EAAEtO,IAAI,CAACsN,IAAK;oBACjBzG,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL8B,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb5H,OAAA,CAACjC,IAAI,CAAC2Q,KAAK;kBAACtH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BrH,OAAA,CAACjC,IAAI,CAAC4Q,KAAK;oBAAAtH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9B5H,OAAA,CAACjC,IAAI,CAAC6Q,OAAO;oBACXrL,IAAI,EAAC,OAAO;oBACZsL,KAAK,EAAEtO,IAAI,CAACuO,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClC3H,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL8B,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb5H,OAAA,CAACjC,IAAI,CAAC2Q,KAAK;kBAACtH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BrH,OAAA,CAACjC,IAAI,CAAC4Q,KAAK;oBAAAtH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9B5H,OAAA,CAACjC,IAAI,CAAC6Q,OAAO;oBACXrL,IAAI,EAAC,KAAK;oBACVsL,KAAK,EAAEtO,IAAI,CAACyO,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxB3H,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL8B,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb5H,OAAA,CAACjC,IAAI,CAAC2Q,KAAK;kBAACtH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BrH,OAAA,CAACjC,IAAI,CAAC4Q,KAAK;oBAAAtH,QAAA,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjD5H,OAAA;oBAAAqH,QAAA,gBACErH,OAAA,CAACjC,IAAI,CAACkR,KAAK;sBACT1L,IAAI,EAAC,OAAO;sBACZ2L,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BtB,IAAI,EAAC,YAAY;sBACjBuB,OAAO,EAAEjO,UAAU,KAAK,WAAY;sBACpCkO,QAAQ,EAAEA,CAAA,KAAMjO,aAAa,CAAC,WAAW,CAAE;sBAC3CgG,SAAS,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACF5H,OAAA,CAACjC,IAAI,CAACkR,KAAK;sBACT1L,IAAI,EAAC,OAAO;sBACZ2L,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCtB,IAAI,EAAC,YAAY;sBACjBuB,OAAO,EAAEjO,UAAU,KAAK,aAAc;sBACtCkO,QAAQ,EAAEA,CAAA,KAAMjO,aAAa,CAAC,aAAa;oBAAE;sBAAAqG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEb5H,OAAA;kBAAKoH,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BrH,OAAA,CAAChC,MAAM;oBACLoJ,SAAS,EAAC,WAAW;oBACrBgF,KAAK,EAAE;sBACLW,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB2B,KAAK,EAAE,SAAS;sBAChBP,MAAM,EAAE,MAAM;sBACdoB,UAAU,EAAE;oBACd,CAAE;oBACFvB,OAAO,EAAE5B,oBAAqB;oBAC9BmC,QAAQ,EAAE5L,qBAAqB,IAAIF,qBAAqB,IAAII,kCAAmC;oBAAAyE,QAAA,EAE9FzE,kCAAkC,GAAG,yBAAyB,GAC9DF,qBAAqB,GAAG,mBAAmB,GAAG;kBAAS;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eAET5H,OAAA,CAACrB,iBAAiB;oBAChB4Q,IAAI,EAAE1H,eAAgB;oBACtB2H,MAAM,EAAEA,CAAA,KAAM1H,kBAAkB,CAAC,KAAK,CAAE;oBACxC2H,SAAS,EAAEvD,YAAa;oBACxBwD,KAAK,EAAC,oBAAoB;oBAC1BhH,OAAO,EAAC,wDAAwD;oBAChEiH,iBAAiB,EAAC,QAAQ;oBAC1BpM,IAAI,EAAC;kBAAQ;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZ5H,OAAA;QAAAqH,QAAA,eACErH,OAAA,CAACd,OAAO;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5H,OAAA,CAACxB,MAAM;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGV5H,OAAA,CAACpB,cAAc;MACb2Q,IAAI,EAAExH,kBAAmB;MACzByH,MAAM,EAAEA,CAAA,KAAMxH,qBAAqB,CAAC,KAAK,CAAE;MAC3CsC,UAAU,EAAE5D,QAAS;MACrBkJ,gBAAgB,EAAErH,6BAA8B;MAChDsH,kBAAkB,EAAE5N;IAAY;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEF5H,OAAA,CAACX,gBAAgB;MACfkQ,IAAI,EAAElP,sBAAuB;MAC7ByP,OAAO,EAAEA,CAAA,KAAM;QACbxP,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF5H,OAAA,CAACnB,mBAAmB;MAClB0Q,IAAI,EAAEtH,uBAAwB;MAC9B6H,OAAO,EAAEA,CAAA,KAAM;QACb5H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAE;MACFyH,oBAAoB,EAAEA,CAAA,KAAM;QAC1B7H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;QAC3BN,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFgI,YAAY,EAAE7H,qBAAsB;MACpCxG,aAAa,EAAE0G;IAAqB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1H,EAAA,CA76BID,gBAAgB;EAAA,QAGPnB,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BJ,WAAW,EACXK,cAAc,EAIND,cAAc,EAChBA,cAAc,EACPA,cAAc,EAChBA,cAAc,EAGagB,sBAAsB;AAAA;AAAAmQ,EAAA,GA3BzEhQ,gBAAgB;AA+6BtB,eAAeA,gBAAgB;AAAC,IAAAgQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}