import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Button, Form, Alert, Badge, ProgressBar, Container, Row, Col } from 'react-bootstrap';
import { FaTag, FaClock, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import {
  applyPromotionCode,
  validatePromotion,
  clearPromotion,
  selectAppliedPromotion,
  selectPromotionError,
  selectPromotionValidating,
  selectPromotionApplying,
  selectIsPromotionExpired,
} from '../redux/promotion/promotionSlice';
import usePromotionValidation from '../hooks/usePromotionValidation';
import usePromotionErrorHandler from '../hooks/usePromotionErrorHandler';
import PromotionErrorBoundary from './PromotionErrorBoundary';

const PromotionTestComponent = () => {
  const dispatch = useDispatch();
  const appliedPromotion = useSelector(selectAppliedPromotion);
  const promotionError = useSelector(selectPromotionError);
  const isValidating = useSelector(selectPromotionValidating);
  const isApplying = useSelector(selectPromotionApplying);
  const isExpired = useSelector(selectIsPromotionExpired);

  const [testCode, setTestCode] = useState('');
  const [orderAmount, setOrderAmount] = useState(500000); // 500k VND
  const [testResults, setTestResults] = useState([]);

  const {
    validatePromotionIfNeeded,
    forceValidation,
    clearPromotionWithCleanup,
    formatTimeRemaining,
    isAboutToExpire,
  } = usePromotionValidation();

  const { handleError, createContextualHandler } = usePromotionErrorHandler();

  // Test codes for different scenarios
  const testCodes = [
    { code: 'SUMMER15', description: 'Valid 15% discount' },
    { code: 'EXPIRED', description: 'Expired promotion' },
    { code: 'INVALID', description: 'Invalid code' },
    { code: 'LIMIT', description: 'Usage limit reached' },
  ];

  useEffect(() => {
    // Validate promotion when order amount changes
    if (appliedPromotion.code) {
      validatePromotionIfNeeded(orderAmount);
    }
  }, [orderAmount, appliedPromotion.code, validatePromotionIfNeeded]);

  const handleApplyPromotion = async (code) => {
    try {
      setTestCode(code);
      const result = await dispatch(applyPromotionCode({
        code,
        orderAmount,
      })).unwrap();
      
      addTestResult(`✅ Applied ${code}: ${result.discount} VND discount`, 'success');
    } catch (error) {
      const errorHandler = createContextualHandler('test-apply');
      errorHandler(error);
      addTestResult(`❌ Failed to apply ${code}: ${error}`, 'error');
    }
  };

  const handleForceValidation = async () => {
    if (!appliedPromotion.code) {
      addTestResult('❌ No promotion to validate', 'warning');
      return;
    }

    try {
      const result = await forceValidation(orderAmount);
      if (result.valid) {
        addTestResult('✅ Validation passed', 'success');
      } else {
        addTestResult(`❌ Validation failed: ${result.message}`, 'error');
      }
    } catch (error) {
      addTestResult(`❌ Validation error: ${error}`, 'error');
    }
  };

  const handleClearPromotion = () => {
    clearPromotionWithCleanup();
    addTestResult('🧹 Promotion cleared', 'info');
  };

  const addTestResult = (message, type) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [
      { message, type, timestamp, id: Date.now() },
      ...prev.slice(0, 9) // Keep only last 10 results
    ]);
  };

  const getStatusBadge = () => {
    if (isExpired) return <Badge bg="danger">Expired</Badge>;
    if (isAboutToExpire) return <Badge bg="warning">Expiring Soon</Badge>;
    if (appliedPromotion.code) return <Badge bg="success">Active</Badge>;
    return <Badge bg="secondary">None</Badge>;
  };

  const getProgressValue = () => {
    if (!appliedPromotion.expiresAt) return 0;
    
    const now = Date.now();
    const expires = new Date(appliedPromotion.expiresAt).getTime();
    const reserved = new Date(appliedPromotion.expiresAt).getTime() - (15 * 60 * 1000); // 15 min ago
    
    const total = expires - reserved;
    const remaining = expires - now;
    
    return Math.max(0, Math.min(100, (remaining / total) * 100));
  };

  return (
    <PromotionErrorBoundary
      onRetry={() => addTestResult('🔄 Retrying after error', 'info')}
      onClearPromotion={handleClearPromotion}
    >
      <Container className="my-4">
        <h3>🧪 Promotion System Test Component</h3>
        
        <Row>
          <Col md={6}>
            <Card className="mb-3">
              <Card.Header>
                <h5>Test Controls</h5>
              </Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Order Amount (VND)</Form.Label>
                  <Form.Control
                    type="number"
                    value={orderAmount}
                    onChange={(e) => setOrderAmount(Number(e.target.value))}
                    min="0"
                    step="10000"
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Manual Code Test</Form.Label>
                  <div className="d-flex gap-2">
                    <Form.Control
                      type="text"
                      value={testCode}
                      onChange={(e) => setTestCode(e.target.value)}
                      placeholder="Enter promotion code"
                    />
                    <Button
                      variant="primary"
                      onClick={() => handleApplyPromotion(testCode)}
                      disabled={isApplying || !testCode}
                    >
                      {isApplying ? 'Applying...' : 'Apply'}
                    </Button>
                  </div>
                </Form.Group>

                <div className="mb-3">
                  <h6>Quick Test Codes:</h6>
                  {testCodes.map(({ code, description }) => (
                    <Button
                      key={code}
                      variant="outline-secondary"
                      size="sm"
                      className="me-2 mb-2"
                      onClick={() => handleApplyPromotion(code)}
                      disabled={isApplying}
                    >
                      {code}
                      <br />
                      <small>{description}</small>
                    </Button>
                  ))}
                </div>

                <div className="d-flex gap-2">
                  <Button
                    variant="warning"
                    onClick={handleForceValidation}
                    disabled={isValidating || !appliedPromotion.code}
                  >
                    {isValidating ? 'Validating...' : 'Force Validate'}
                  </Button>
                  <Button
                    variant="danger"
                    onClick={handleClearPromotion}
                    disabled={!appliedPromotion.code}
                  >
                    Clear
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col md={6}>
            <Card className="mb-3">
              <Card.Header>
                <h5>Current Promotion Status</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span>Status:</span>
                  {getStatusBadge()}
                </div>

                {appliedPromotion.code && (
                  <>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Code:</span>
                      <Badge bg="info">{appliedPromotion.code}</Badge>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Discount:</span>
                      <span>{appliedPromotion.discount?.toLocaleString()} VND</span>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Remaining Uses:</span>
                      <span>{appliedPromotion.remainingUses || 'N/A'}</span>
                    </div>
                    
                    {appliedPromotion.expiresAt && (
                      <>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Time Remaining:</span>
                          <span className={isAboutToExpire ? 'text-warning' : ''}>
                            <FaClock className="me-1" />
                            {formatTimeRemaining()}
                          </span>
                        </div>
                        <ProgressBar
                          now={getProgressValue()}
                          variant={isAboutToExpire ? 'warning' : 'success'}
                          className="mb-2"
                        />
                      </>
                    )}
                  </>
                )}

                {promotionError && (
                  <Alert variant="danger" className="mt-2">
                    <FaTimesCircle className="me-2" />
                    {promotionError}
                  </Alert>
                )}

                {(isValidating || isApplying) && (
                  <Alert variant="info" className="mt-2">
                    <div className="d-flex align-items-center">
                      <div className="spinner-border spinner-border-sm me-2" />
                      {isValidating ? 'Validating promotion...' : 'Applying promotion...'}
                    </div>
                  </Alert>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Card>
          <Card.Header>
            <h5>Test Results Log</h5>
          </Card.Header>
          <Card.Body style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {testResults.length === 0 ? (
              <p className="text-muted">No test results yet. Try applying a promotion code.</p>
            ) : (
              testResults.map((result) => (
                <Alert
                  key={result.id}
                  variant={result.type}
                  className="py-2 mb-2"
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <span>{result.message}</span>
                    <small className="text-muted">{result.timestamp}</small>
                  </div>
                </Alert>
              ))
            )}
          </Card.Body>
        </Card>
      </Container>
    </PromotionErrorBoundary>
  );
};

export default PromotionTestComponent;
