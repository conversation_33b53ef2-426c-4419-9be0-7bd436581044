const jwt = require("jsonwebtoken");

// Optional authentication middleware
// Sets req.user if valid token is provided, but doesn't block if no token
const optionalAuth = (req, res, next) => {
  const token = req.headers.authorization?.split(" ")[1];

  // If no token, continue without setting req.user
  if (!token) {
    req.user = null;
    return next();
  }

  // If token exists, try to verify it
  jwt.verify(token, process.env.SECRET_KEY, (err, decoded) => {
    if (err) {
      // If token is invalid, continue without setting req.user
      req.user = null;
    } else {
      // If token is valid, set req.user
      req.user = decoded.user;
    }
    next();
  });
};

module.exports = { optionalAuth };
