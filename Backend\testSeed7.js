const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function testSeed7() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB for testing seed7 data");

    console.log("\n🧪 Testing Seed7 Promotion Data");
    console.log("=" .repeat(50));

    // Test 1: Verify promotion counts
    const totalPromotions = await Promotion.countDocuments();
    const publicPromotions = await Promotion.countDocuments({ isPublic: true });
    const privatePromotions = await Promotion.countDocuments({ isPublic: false });

    console.log(`\n📊 Database Statistics:`);
    console.log(`   Total promotions: ${totalPromotions}`);
    console.log(`   Public promotions: ${publicPromotions}`);
    console.log(`   Private promotions: ${privatePromotions}`);

    // Test 2: Test user-specific promotion access
    console.log(`\n👥 User-Specific Promotion Access:`);
    
    for (let userId = 1; userId <= 5; userId++) {
      const userPromotions = await Promotion.find({
        $or: [
          { isPublic: true, isActive: true },
          { isPublic: false, userId: userId, isActive: true }
        ]
      });

      const publicCount = userPromotions.filter(p => p.isPublic).length;
      const privateCount = userPromotions.filter(p => !p.isPublic).length;
      
      console.log(`   User ${userId}: ${userPromotions.length} total (${publicCount} public + ${privateCount} private)`);
      
      // Show private promotions
      const privatePromotions = userPromotions.filter(p => !p.isPublic);
      if (privatePromotions.length > 0) {
        privatePromotions.forEach(promo => {
          const discount = promo.discountType === 'PERCENTAGE' ? `${promo.discountValue}%` : `${promo.discountValue.toLocaleString()} VND`;
          console.log(`     🔒 ${promo.code}: ${discount} off (${promo.usedCount}/${promo.usageLimit} used)`);
        });
      }
    }

    // Test 3: Test promotion validation
    console.log(`\n🔍 Promotion Validation Tests:`);
    
    const testCases = [
      { userId: 2, code: "VIP_USER2", amount: 1500000, expected: "SUCCESS" },
      { userId: 1, code: "VIP_USER2", amount: 1500000, expected: "FAIL - Not owner" },
      { userId: 3, code: "WELCOME_USER3", amount: 300000, expected: "SUCCESS" },
      { userId: 1, code: "SUMMER15", amount: 300000, expected: "SUCCESS - Public" },
      { userId: 1, code: "FLASH30", amount: 600000, expected: "SUCCESS - Public" },
      { userId: 1, code: "FAMILY40", amount: 1000000, expected: "FAIL - Min order not met" }
    ];

    for (const test of testCases) {
      console.log(`\n🎯 ${test.code} → User ${test.userId} (${test.amount.toLocaleString()} VND)`);
      
      try {
        const promotion = await Promotion.findOne({ code: test.code });
        if (!promotion) {
          console.log(`   ❌ Promotion not found`);
          continue;
        }

        const eligibility = await promotion.canUserUse(test.userId);
        const validPromotion = promotion.isValid();
        const meetsMinOrder = test.amount >= promotion.minOrderAmount;
        
        console.log(`   📋 Promotion valid: ${validPromotion}`);
        console.log(`   👤 User can use: ${eligibility.canUse}`);
        console.log(`   💰 Meets min order: ${meetsMinOrder}`);
        console.log(`   🔢 Remaining uses: ${eligibility.remainingUses}`);
        
        if (eligibility.canUse && validPromotion && meetsMinOrder) {
          // Calculate discount
          let discount = 0;
          if (promotion.discountType === 'PERCENTAGE') {
            discount = (test.amount * promotion.discountValue) / 100;
            if (promotion.maxDiscountAmount) {
              discount = Math.min(discount, promotion.maxDiscountAmount);
            }
          } else {
            discount = promotion.discountValue;
          }
          console.log(`   ✅ Result: SUCCESS - ${discount.toLocaleString()} VND discount`);
        } else {
          console.log(`   ❌ Result: FAIL - ${eligibility.reason || 'Validation failed'}`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }

    // Test 4: Check data integrity
    console.log(`\n🔍 Data Integrity Check:`);
    
    const inconsistentPromotions = await Promotion.find({
      $or: [
        { isPublic: true, userId: { $ne: null } },
        { isPublic: false, userId: null }
      ]
    });

    if (inconsistentPromotions.length > 0) {
      console.log(`   ⚠️  Data inconsistencies found: ${inconsistentPromotions.length}`);
      inconsistentPromotions.forEach(promo => {
        console.log(`     - ${promo.code}: isPublic=${promo.isPublic}, userId=${promo.userId}`);
      });
    } else {
      console.log(`   ✅ Data consistency: Perfect`);
    }

    // Test 5: Usage statistics
    console.log(`\n📈 Usage Statistics:`);
    
    const promotionStats = await Promotion.aggregate([
      {
        $group: {
          _id: '$isPublic',
          count: { $sum: 1 },
          totalUsage: { $sum: '$usedCount' },
          totalLimit: { $sum: '$usageLimit' },
          avgUsagePercent: { 
            $avg: { 
              $multiply: [
                { $divide: ['$usedCount', '$usageLimit'] }, 
                100
              ] 
            } 
          }
        }
      }
    ]);

    promotionStats.forEach(stat => {
      const type = stat._id ? 'Public' : 'Private';
      console.log(`   ${type} promotions: ${stat.count} total, ${stat.totalUsage}/${stat.totalLimit} used (${Math.round(stat.avgUsagePercent)}% avg)`);
    });

    console.log(`\n🎉 Seed7 Test Complete!`);
    console.log(`\n📋 Summary:`);
    console.log(`✅ Database structure correct`);
    console.log(`✅ User access control working`);
    console.log(`✅ Promotion validation functional`);
    console.log(`✅ Data integrity maintained`);
    console.log(`✅ Usage tracking accurate`);

    console.log(`\n🚀 Ready for frontend testing!`);
    console.log(`\nRecommended test flow:`);
    console.log(`1. Login as User 2 → Should see VIP_USER2 (50% off)`);
    console.log(`2. Login as User 3 → Should see WELCOME_USER3 (100k VND off)`);
    console.log(`3. Login as User 4 → Should see BIRTHDAY_USER4 (75% off)`);
    console.log(`4. Login as User 5 → Should see LOYALTY_USER5 (200k VND off)`);
    console.log(`5. Test public promotions with all users`);
    console.log(`6. Test scarcity with FLASH30 (15 uses left)`);
    console.log(`7. Test high minimum order with FAMILY40 (2M VND)`);

    process.exit(0);
  } catch (error) {
    console.error("Test failed:", error);
    process.exit(1);
  }
}

testSeed7();
