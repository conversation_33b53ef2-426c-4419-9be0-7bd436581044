import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ge, Spinner } from "react-bootstrap";
import { FaTag, FaTimes, FaCheck } from "react-icons/fa";
import axios from "axios";
import Utils from "../../../../utils/Utils";
import getApiUrl from "../../../../utils/apiConfig"; // Add this import
import { useAppSelector } from "../../../../redux/store"; // Add this import
import "../../../../css/PromotionModal.css";

const PromotionModal = ({ show, onHide, totalPrice, onApplyPromotion, currentPromotionId }) => {
  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState(null);
  const [applying, setApplying] = useState(false);
  const [manualCode, setManualCode] = useState("");
  const [manualCodeError, setManualCodeError] = useState("");
  const [applyingManual, setApplyingManual] = useState(false);

  const API_BASE_URL = getApiUrl(); // Add this line
  const Auth = useAppSelector((state) => state.Auth.Auth); // Add this line

  useEffect(() => {
    if (show && totalPrice > 0) {
      fetchPromotions();
    }
  }, [show, totalPrice]);

  const fetchPromotions = async () => {
    setLoading(true);
    try {
      let promotionList = [];
      try {
        console.log("Fetching user promotions from API...");
        // Use user-specific promotions API that includes both assigned and public promotions
        const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {
          headers: Auth?.token ? { Authorization: `Bearer ${Auth.token}` } : {}
        });
        console.log("API Response:", response.data);

        promotionList = response.data.promotions || response.data.data || response.data || [];
        console.log("User promotion list from API:", promotionList);

        if (!Array.isArray(promotionList) || promotionList.length === 0) {
          console.log("API returned empty or invalid data");
          setPromotions([]);
          setLoading(false);
          return;
        }
      } catch (apiError) {
        console.log("API Error:", apiError.message, "- Using mock promotion data");
        // Fallback to mock data only in development
        promotionList = [
          {
            _id: "1",
            code: "SAVE20",
            description: "Save $20 on orders over $100",
            discountType: "FIXED_AMOUNT",
            discountValue: 20,
            minOrderAmount: 100,
            maxDiscountAmount: 20,
            endDate: "2025-12-31",
            isAvailable: true,
            userCanUse: true,
            remainingUses: 2,
            usageLimit: 100,
            usedCount: 25,
            usagePercentage: 25
          }
        ];
      }
      
      console.log("Total price for processing:", totalPrice);
      console.log("Processing", promotionList.length, "available promotions");
      
      // Backend already filters promotions appropriately:
      // - Public promotions for everyone
      // - User's assigned private promotions only
      // - No unassigned private promotions (they must be claimed via manual code entry)
      console.log(`Processing ${promotionList.length} promotions from backend`);

      // Process promotions with proper availability checking
      const processedPromotions = promotionList.map((promo) => {
        console.log(`Processing promotion:`, promo.code, `(${promo.assignmentType})`);

        // Check if current order meets minimum requirement
        const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);

        // Check if user has remaining uses for this promotion
        const hasRemainingUses = promo.remainingUses > 0;
        const isAvailable = promo.isAvailable !== false; // Default to true if not specified

        let discount = 0;
        let isValid = meetsMinOrder && isAvailable && hasRemainingUses;
        let message = "";

        if (isValid) {
          // Calculate discount for display
          if (promo.discountType === "PERCENTAGE") {
            discount = (totalPrice * promo.discountValue) / 100;
            if (promo.maxDiscountAmount) {
              discount = Math.min(discount, promo.maxDiscountAmount);
            }
          } else if (promo.discountType === "FIXED_AMOUNT") {
            discount = promo.discountValue;
          }
          message = "Ready to apply";
        } else {
          if (!meetsMinOrder) {
            message = `Minimum order amount: ${Utils.formatCurrency(promo.minOrderAmount || 0)}`;
          } else if (!hasRemainingUses) {
            message = "No remaining uses";
          } else if (!isAvailable) {
            message = "Not available";
          } else {
            message = "Cannot be used";
          }
          isValid = false;
        }

        // Use backend-provided promotion type (only PUBLIC or PRIVATE should appear in modal)
        const promotionType = promo.assignmentType || 'PUBLIC';
        const isClaimable = false; // No claimable promotions in modal
        const isUserPrivate = promotionType === 'PRIVATE';

        return {
          ...promo,
          isValid,
          discount,
          message,
          isClaimable,
          isUserPrivate,
          promotionType
        };
      });
      
      console.log("Final processed promotions:", processedPromotions);
      
      // Sort promotions: Valid ones first, then by discount amount
      const sortedPromotions = processedPromotions.sort((a, b) => {
        // Valid promotions first
        if (a.isValid && !b.isValid) return -1;
        if (!a.isValid && b.isValid) return 1;
        
        // Within same validity, sort by discount descending
        return b.discount - a.discount;
      });
      
      setPromotions(sortedPromotions);
    } catch (error) {
      console.error("Error fetching promotions:", error);
      setPromotions([]);
    }
    setLoading(false);
  };

  const handleApplyPromotion = async (promotion) => {
    if (!promotion.isValid || promotion.remainingUses === 0) return;
    
    setApplying(true);
    try {
      try {
        // Replace hardcoded URL with environment-based URL
        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
          code: promotion.code,
          orderAmount: totalPrice,
          userId: Auth?._id, // Thêm userId để kiểm tra user-specific usage limit
        });
        
        if (response.data.valid) {
          onApplyPromotion({
            code: promotion.code,
            discount: response.data.discount,
            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,
            promotionId: response.data.promotionId,
            remainingUses: response.data.remainingUses,
          });
          onHide();
        }
      } catch (apiError) {
        // Mock logic remains the same
        console.log("Using mock promotion application");
        onApplyPromotion({
          code: promotion.code,
          discount: promotion.discount,
          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,
          promotionId: promotion._id,
        });
        onHide();
      }
    } catch (error) {
      console.error("Error applying promotion:", error);
    }
    setApplying(false);
  };

  const handleRemovePromotion = () => {
    onApplyPromotion({
      code: "",
      discount: 0,
      message: "",
      promotionId: null,
    });
    onHide();
  };

  const handleApplyManualCode = async () => {
    if (!manualCode.trim()) {
      setManualCodeError("Please enter a promotion code");
      return;
    }

    setApplyingManual(true);
    setManualCodeError("");

    try {
      const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
        code: manualCode.trim().toUpperCase(),
        orderAmount: totalPrice,
        userId: Auth?._id,
      });

      if (response.data.valid) {
        // Show special message if promotion was claimed
        const message = response.data.claimed
          ? `🎉 Private promotion claimed and applied: -${Utils.formatCurrency(response.data.discount)}`
          : `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`;

        onApplyPromotion({
          code: response.data.promotionId ? manualCode.trim().toUpperCase() : manualCode.trim(),
          discount: response.data.discount,
          message: message,
          promotionId: response.data.promotionId,
          remainingUses: response.data.remainingUses,
          claimed: response.data.claimed
        });
        onHide();
      } else {
        setManualCodeError(response.data.message || "Invalid promotion code");
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Failed to apply promotion code";
      setManualCodeError(errorMessage);
    }

    setApplyingManual(false);
  };

  const handleManualCodeChange = (e) => {
    setManualCode(e.target.value);
    if (manualCodeError) {
      setManualCodeError("");
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header 
        closeButton 
        style={{ 
          backgroundColor: "rgba(20, 30, 70, 0.95)", 
          borderColor: "rgba(255,255,255,0.2)",
          color: "white"
        }}
      >
        <Modal.Title className="d-flex align-items-center">
          <FaTag className="me-2" />
          Select Promotion
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body
        style={{
          backgroundColor: "rgba(20, 30, 70, 0.95)",
          color: "white",
          maxHeight: "60vh",
          overflowY: "auto"
        }}
      >
        {/* Manual Promotion Code Input */}
        <div className="mb-4 p-3 manual-code-section">
          <h6 className="mb-3">
            <FaTag className="me-2" />
            Enter Private Promotion Code
          </h6>
          <p className="small mb-3" style={{color: 'rgba(255,255,255,0.7)'}}>
            Have a special promotion code? Enter it here to claim exclusive discounts!
          </p>
          <div className="d-flex gap-2 align-items-start">
            <div className="flex-grow-1">
              <input
                type="text"
                className="form-control manual-code-input"
                placeholder="Enter private promotion code (e.g., EXCLUSIVE75)"
                value={manualCode}
                onChange={handleManualCodeChange}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleApplyManualCode();
                  }
                }}
              />
            </div>
            <Button
              variant="outline-light"
              onClick={handleApplyManualCode}
              disabled={applyingManual || !manualCode.trim()}
              style={{ minWidth: "100px", height: "38px" }}
            >
              {applyingManual ? (
                <Spinner animation="border" size="sm" />
              ) : (
                "Apply"
              )}
            </Button>
          </div>
          {manualCodeError && (
            <small className="text-danger mt-2 d-block">{manualCodeError}</small>
          )}
        </div>

        <hr className="promotion-divider" />

        {loading ? (
          <div className="text-center py-4">
            <Spinner animation="border" variant="light" />
            <div className="mt-2">Loading promotions...</div>
          </div>
        ) : (
          <>
            {/* Current promotion section */}
            {currentPromotionId && (
              <div className="mb-4">
                <h6 className="mb-3">Current Applied Promotion</h6>
                <Card 
                  className="promotion-card current-promotion"
                  style={{ 
                    backgroundColor: "rgba(40, 167, 69, 0.2)", 
                    borderColor: "#28a745",
                    border: "2px solid #28a745"
                  }}
                >
                  <Card.Body className="py-3">
                    <div className="d-flex justify-content-between align-items-center">
                      <div className="d-flex align-items-center">
                        <FaCheck className="text-success me-2" />
                        <span className="text-success fw-bold">Applied</span>
                      </div>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={handleRemovePromotion}
                        disabled={applying}
                      >
                        <FaTimes className="me-1" />
                        Remove
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            )}

            {/* Promotions section */}
            <h6 className="mb-3">
              Available Promotions 
              <span className="small ms-2" style={{color: 'rgba(255,255,255,0.6)'}}>
                ({promotions.filter(p => p.isValid).length} ready, {promotions.filter(p => !p.isValid).length} require higher order)
              </span>
            </h6>
            {promotions.length === 0 ? (
              <div className="text-center py-4" style={{color: 'rgba(255,255,255,0.7)'}}>
                <FaTag size={48} className="mb-3" style={{opacity: 0.5}} />
                <div>No promotions available</div>
              </div>
            ) : (
              <div className="row g-3">
                {promotions.map((promotion) => (
                  <div key={promotion._id} className="col-12">
                    <Card
                      className={`promotion-card ${currentPromotionId === promotion._id ? 'current' : ''} ${!promotion.isValid || promotion.remainingUses === 0 ? 'disabled' : ''}`}
                      style={{
                        backgroundColor: currentPromotionId === promotion._id 
                          ? "rgba(40, 167, 69, 0.2)" 
                          : promotion.isValid 
                            ? "rgba(255,255,255,0.1)" 
                            : "rgba(255, 193, 7, 0.1)",
                        borderColor: currentPromotionId === promotion._id 
                          ? "#28a745" 
                          : promotion.isValid 
                            ? "rgba(255,255,255,0.3)" 
                            : "rgba(255, 193, 7, 0.5)",
                        cursor: !promotion.isValid || promotion.remainingUses === 0 ? "not-allowed" : "pointer",
                        transition: "all 0.3s ease",
                        opacity: !promotion.isValid || promotion.remainingUses === 0 ? 0.7 : 1
                      }}
                      onClick={() => promotion.isValid && promotion.remainingUses > 0 && handleApplyPromotion(promotion)}
                    >
                      <Card.Body className="py-3">
                        <div className="d-flex justify-content-between align-items-start">
                          <div className="flex-grow-1">
                            <div className="d-flex align-items-center mb-2">
                              <FaTag className="me-2 text-primary" />
                              <h6 className="mb-0 fw-bold">{promotion.code}</h6>
                              {currentPromotionId === promotion._id && (
                                <Badge bg="success" className="ms-2">Applied</Badge>
                              )}

                              {/* Promotion Type Badge */}
                              {promotion.promotionType === 'PRIVATE' && (
                                <Badge bg="secondary" className="ms-2">
                                  <i className="fas fa-lock me-1"></i>Your Private
                                </Badge>
                              )}
                              {promotion.promotionType === 'PUBLIC' && (
                                <Badge bg="primary" className="ms-2">
                                  <i className="fas fa-globe me-1"></i>Public
                                </Badge>
                              )}

                              <Badge bg={promotion.isValid ? "success" : "warning"} className="ms-2">
                                {promotion.isValid ? "Available" : "Requires Higher Order"}
                              </Badge>
                            </div>
                            
                            <p className="mb-2 small" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>

                            {/* Usage Progress Bar */}
                            {promotion.usageLimit && (
                              <div className="mb-2">
                                <div className="d-flex justify-content-end align-items-center mb-1">
                                  <small style={{color: 'rgba(255,255,255,0.6)', fontSize: '0.7rem'}}>
                                    {promotion.usagePercentage || 0}%
                                  </small>
                                </div>
                                <div
                                  className="progress"
                                  style={{
                                    height: '3px',
                                    backgroundColor: 'rgba(255,255,255,0.15)',
                                    borderRadius: '2px'
                                  }}
                                >
                                  <div
                                    className="progress-bar"
                                    role="progressbar"
                                    style={{
                                      width: `${promotion.usagePercentage || 0}%`,
                                      backgroundColor: promotion.usagePercentage >= 90
                                        ? '#dc3545'
                                        : promotion.usagePercentage >= 70
                                        ? '#ffc107'
                                        : '#28a745',
                                      transition: 'width 0.3s ease',
                                      borderRadius: '2px'
                                    }}
                                    aria-valuenow={promotion.usagePercentage || 0}
                                    aria-valuemin="0"
                                    aria-valuemax="100"
                                  ></div>
                                </div>
                              </div>
                            )}
                            
                            <div className="d-flex justify-content-between align-items-center">
                              <div>
                                <span className={`fw-bold ${promotion.isValid ? 'text-success' : 'text-warning'}`}>
                                  {promotion.isValid ? `Save ${Utils.formatCurrency(promotion.discount)}` : promotion.message}
                                </span>
                                {/* Usage Information */}
                                {promotion.remainingUses !== undefined && (
                                  <div className="small mt-1 usage-info">
                                    <Badge
                                      bg={promotion.remainingUses > 0 ? "info" : "warning"}
                                      className="me-1 usage-badge"
                                    >
                                      {promotion.remainingUses > 0
                                        ? `${promotion.remainingUses} uses left`
                                        : "Usage limit reached"
                                      }
                                    </Badge>
                                    {promotion.userUsageLimit && (
                                      <span className="usage-info">
                                        (Max: {promotion.userUsageLimit}/user)
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>

                              <div className="text-end">
                                <div className="small">
                                  {promotion.minOrderAmount && (
                                    <div className={promotion.isValid ? 'text-success' : 'text-warning'}>
                                      Min: {Utils.formatCurrency(promotion.minOrderAmount)} {promotion.isValid ? '✓' : '✗'}
                                    </div>
                                  )}
                                  {promotion.maxDiscountAmount && (
                                    <div style={{color: 'rgba(255,255,255,0.6)'}}>
                                      Max: {Utils.formatCurrency(promotion.maxDiscountAmount)}
                                    </div>
                                  )}
                                  {promotion.endDate && (
                                    <div className="text-success">
                                      Expires: {new Date(promotion.endDate).toLocaleDateString()} ✓
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </Modal.Body>
      
      <Modal.Footer 
        style={{ 
          backgroundColor: "rgba(20, 30, 70, 0.95)", 
          borderColor: "rgba(255,255,255,0.2)"
        }}
      >
        <Button variant="outline-light" onClick={onHide} disabled={applying}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default PromotionModal;
