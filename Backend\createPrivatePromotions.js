const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const User = require("./src/models/user");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function createPrivatePromotions() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB");

    // Get admin user
    const adminUser = await User.findOne({ _id: 1 });
    if (!adminUser) {
      console.error("Admin user not found");
      process.exit(1);
    }

    console.log("\n🎯 Creating Private Promotions for Testing");
    console.log("=" .repeat(50));

    // Create private promotions for different users
    const privatePromotions = [
      {
        code: "PRIVATE_USER1",
        name: "Personal Discount for User 1",
        description: "Exclusive 30% off for User 1 only",
        discountType: "PERCENTAGE",
        discountValue: 30,
        maxDiscountAmount: 500000,
        minOrderAmount: 800000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 5,
        usedCount: 0,
        userUsageLimit: 5,
        isActive: true,
        isPublic: false,
        userId: 1,
        createdBy: adminUser._id
      },
      {
        code: "VIP_USER2",
        name: "VIP Exclusive for User 2",
        description: "Special VIP treatment - 40% off",
        discountType: "PERCENTAGE",
        discountValue: 40,
        maxDiscountAmount: 800000,
        minOrderAmount: 1200000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 3,
        usedCount: 1, // Already used once
        userUsageLimit: 3,
        isActive: true,
        isPublic: false,
        userId: 2,
        createdBy: adminUser._id
      },
      {
        code: "BIRTHDAY_USER3",
        name: "Happy Birthday User 3!",
        description: "Birthday special - 50% off your celebration",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 1000000,
        minOrderAmount: 1000000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-03-31"), // Limited time
        usageLimit: 1,
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPublic: false,
        userId: 3,
        createdBy: adminUser._id
      },
      {
        code: "LOYALTY_USER4",
        name: "Loyalty Reward for User 4",
        description: "Thank you for your loyalty - 200,000 VND off",
        discountType: "FIXED_AMOUNT",
        discountValue: 200000,
        minOrderAmount: 1500000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 2,
        usedCount: 0,
        userUsageLimit: 2,
        isActive: true,
        isPublic: false,
        userId: 4,
        createdBy: adminUser._id
      },
      {
        code: "PREMIUM_USER5",
        name: "Premium Member Exclusive",
        description: "Premium member benefits - 35% off",
        discountType: "PERCENTAGE",
        discountValue: 35,
        maxDiscountAmount: 750000,
        minOrderAmount: 2000000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        usedCount: 3, // Used 3 times
        userUsageLimit: 10,
        isActive: true,
        isPublic: false,
        userId: 5,
        createdBy: adminUser._id
      },
      {
        code: "EXPIRED_USER1",
        name: "Expired Private Promotion",
        description: "This private promotion has expired",
        discountType: "PERCENTAGE",
        discountValue: 25,
        minOrderAmount: 500000,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-12-31"), // Expired
        usageLimit: 5,
        usedCount: 2,
        userUsageLimit: 5,
        isActive: true,
        isPublic: false,
        userId: 1,
        createdBy: adminUser._id
      },
      {
        code: "INACTIVE_USER2",
        name: "Inactive Private Promotion",
        description: "This private promotion is inactive",
        discountType: "FIXED_AMOUNT",
        discountValue: 100000,
        minOrderAmount: 600000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 3,
        usedCount: 0,
        userUsageLimit: 3,
        isActive: false, // Inactive
        isPublic: false,
        userId: 2,
        createdBy: adminUser._id
      }
    ];

    // Insert private promotions
    const createdPromotions = await Promotion.insertMany(privatePromotions);
    console.log(`✅ Created ${createdPromotions.length} private promotions`);

    console.log("\n📋 Private Promotions Created:");
    createdPromotions.forEach(promo => {
      const status = promo.isActive ? "🟢 Active" : "🔴 Inactive";
      const expiry = new Date(promo.endDate) < new Date() ? "⏰ Expired" : "✅ Valid";
      console.log(`${status} ${expiry} ${promo.code} → User ${promo.userId}`);
      console.log(`   ${promo.discountType === 'PERCENTAGE' ? promo.discountValue + '%' : promo.discountValue + ' VND'} off (${promo.usedCount}/${promo.usageLimit} used)`);
    });

    // Test the new system
    console.log("\n🧪 Testing Private Promotion System");
    console.log("=" .repeat(50));

    for (let userId = 1; userId <= 5; userId++) {
      console.log(`\n👤 User ${userId} promotions:`);
      
      const userPromotions = await Promotion.find({
        $or: [
          { isPublic: true, isActive: true },
          { isPublic: false, userId: userId, isActive: true }
        ]
      });

      const privateCount = userPromotions.filter(p => !p.isPublic).length;
      const publicCount = userPromotions.filter(p => p.isPublic).length;
      
      console.log(`  📊 Total: ${userPromotions.length} (${publicCount} public + ${privateCount} private)`);
      
      // Show private promotions
      const privatePromotions = userPromotions.filter(p => !p.isPublic);
      if (privatePromotions.length > 0) {
        privatePromotions.forEach(promo => {
          console.log(`  🔒 ${promo.code}: ${promo.name}`);
        });
      } else {
        console.log(`  🔒 No private promotions`);
      }
    }

    // Test cross-user access (should fail)
    console.log("\n🔒 Testing Private Promotion Security");
    console.log("=" .repeat(50));

    const user1PrivatePromo = await Promotion.findOne({ code: "PRIVATE_USER1" });
    if (user1PrivatePromo) {
      console.log(`\n🎯 Testing ${user1PrivatePromo.code} (belongs to User 1):`);
      
      // Test owner access
      const ownerAccess = await user1PrivatePromo.canUserUse(1);
      console.log(`  ✅ User 1 (owner): ${ownerAccess.canUse ? 'CAN USE' : 'CANNOT USE'}`);
      
      // Test non-owner access
      const nonOwnerAccess = await user1PrivatePromo.canUserUse(2);
      console.log(`  ❌ User 2 (other): ${nonOwnerAccess.canUse ? 'CAN USE' : 'CANNOT USE'} - ${nonOwnerAccess.reason || 'OK'}`);
    }

    console.log("\n🎉 Private Promotion System Ready!");
    console.log("\n📝 Test Scenarios Available:");
    console.log("1. User 1: Has PRIVATE_USER1 (30% off, 5 uses)");
    console.log("2. User 2: Has VIP_USER2 (40% off, 2 remaining uses)");
    console.log("3. User 3: Has BIRTHDAY_USER3 (50% off, limited time)");
    console.log("4. User 4: Has LOYALTY_USER4 (200k VND off, 2 uses)");
    console.log("5. User 5: Has PREMIUM_USER5 (35% off, 7 remaining uses)");
    console.log("6. Cross-user access should be blocked");
    console.log("7. Expired/inactive private promotions should not show");

    process.exit(0);
  } catch (error) {
    console.error("Error creating private promotions:", error);
    process.exit(1);
  }
}

createPrivatePromotions();
