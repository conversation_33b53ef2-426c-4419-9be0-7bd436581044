const RoomUnitActions = {
  // Room actions
  FETCH_ROOMS: 'FETCH_ROOMS',
  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',
  FETCH_ROOMS_FAILED: 'FETCH_ROOMS_FAILED',
  
  ADD_ROOM: 'ADD_ROOM',
  ADD_ROOM_SUCCESS: 'ADD_ROOM_SUCCESS',
  ADD_ROOM_FAILED: 'ADD_ROOM_FAILED',
  
  UPDATE_ROOM: 'UPDATE_ROOM',
  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',
  UPDATE_ROOM_FAILED: 'UPDATE_ROOM_FAILED',
  
  DELETE_ROOM: 'DELETE_ROOM',
  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',
  DELETE_ROOM_FAILED: 'DELETE_ROOM_FAILED',
  
  // Booking actions
  FETCH_BOOKINGS: 'FET<PERSON>_BOOKINGS',
  FETCH_BOOKINGS_SUCCESS: 'FETCH_BOOKINGS_SUCCESS',
  FETCH_BOOKINGS_FAILED: 'FETCH_BOOKINGS_FAILED',
  
  ADD_BOOKING: 'ADD_BOOKING',
  ADD_BOOKING_SUCCESS: 'ADD_BOOKING_SUCCESS',
  ADD_BOOKING_FAILED: 'ADD_BOOKING_FAILED',
  
  UPDATE_BOOKING: 'UPDATE_BOOKING',
  UPDATE_BOOKING_SUCCESS: 'UPDATE_BOOKING_SUCCESS',
  UPDATE_BOOKING_FAILED: 'UPDATE_BOOKING_FAILED',
  
  DELETE_BOOKING: 'DELETE_BOOKING',
  DELETE_BOOKING_SUCCESS: 'DELETE_BOOKING_SUCCESS',
  DELETE_BOOKING_FAILED: 'DELETE_BOOKING_FAILED',
  
  // Check-in/Check-out actions
  CHECK_IN: 'CHECK_IN',
  CHECK_IN_SUCCESS: 'CHECK_IN_SUCCESS',
  CHECK_IN_FAILED: 'CHECK_IN_FAILED',
  
  CHECK_OUT: 'CHECK_OUT',
  CHECK_OUT_SUCCESS: 'CHECK_OUT_SUCCESS',
  CHECK_OUT_FAILED: 'CHECK_OUT_FAILED',
  
  // Filter actions
  SET_ROOM_FILTER: 'SET_ROOM_FILTER',
  SET_DATE_RANGE: 'SET_DATE_RANGE',
  
  // UI state actions
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

export default RoomUnitActions;