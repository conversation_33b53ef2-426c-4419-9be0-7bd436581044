const RoomActions = {
  // Room CRUD Actions
  FETCH_ROOMS_SUCCESS: 'FETCH_ROOMS_SUCCESS',
  FETCH_ROOM_DETAIL_SUCCESS: 'FETCH_ROOM_DETAIL_SUCCESS',
  CREATE_ROOM_SUCCESS: 'CREATE_ROOM_SUCCESS',
  UPDATE_ROOM_SUCCESS: 'UPDATE_ROOM_SUCCESS',
  DELETE_ROOM_SUCCESS: 'DELETE_ROOM_SUCCESS',
  
  // Room Create Form Actions
  SAVE_ROOM_NAME_CREATE: 'SAVE_ROOM_NAME_CREATE',
  SAVE_ROOM_IMAGES_CREATE: 'SAVE_ROOM_IMAGES_CREATE', 
  SAVE_ROOM_PRICING_CREATE: 'SAVE_ROOM_PRICING_CREATE',
  SAVE_ROOM_DETAILS_CREATE: 'SAVE_ROOM_DETAILS_CREATE',
  CLEAR_ROOM_CREATE: 'CLEAR_ROOM_CREATE',
  
  // Room Create List Management (for hotel creation process)
  SAVE_ROOM_TO_CREATE_LIST: 'SAVE_ROOM_TO_CREATE_LIST',
  EDIT_ROOM_IN_CREATE_LIST: 'EDIT_ROOM_IN_CREATE_LIST',
  DELETE_ROOM_FROM_CREATE_LIST: 'DELETE_ROOM_FROM_CREATE_LIST',
  CLEAR_ROOM_CREATE_LIST: 'CLEAR_ROOM_CREATE_LIST',
  
  // Room Status Actions
  TOGGLE_ROOM_STATUS: 'TOGGLE_ROOM_STATUS',
  SET_ROOM_AVAILABILITY: 'SET_ROOM_AVAILABILITY',
  
  // Loading States
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',

  FETCH_ROOM:"FETCH_ROOM",
  FETCH_ROOM_SUCCESS:"FETCH_ROOM_SUCCESS",
};

export default RoomActions;
