import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { getApiUrl } from '../../utils/getApiUrl';

const API_BASE_URL = getApiUrl();

// Async thunks
export const fetchUserPromotions = createAsyncThunk(
  'promotion/fetchUserPromotions',
  async (_, { rejectWithValue, getState }) => {
    try {
      const { Auth } = getState();
      const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {
        headers: {
          Authorization: `Bearer ${Auth.token}`,
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch promotions');
    }
  }
);

export const applyPromotionCode = createAsyncThunk(
  'promotion/applyPromotionCode',
  async ({ code, orderAmount }, { rejectWithValue, getState }) => {
    try {
      const { Auth } = getState();
      const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
        code: code.trim().toUpperCase(),
        orderAmount,
        userId: Auth.Auth?._id,
      });
      
      if (response.data.valid) {
        return {
          code: code.trim().toUpperCase(),
          discount: response.data.discount,
          promotionId: response.data.promotionId,
          reservationId: response.data.reservationId,
          expiresAt: response.data.expiresAt,
          remainingUses: response.data.remainingUses,
        };
      } else {
        return rejectWithValue(response.data.message || 'Invalid promotion code');
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to apply promotion');
    }
  }
);

export const validatePromotion = createAsyncThunk(
  'promotion/validatePromotion',
  async ({ code, orderAmount }, { rejectWithValue, getState }) => {
    try {
      const { Auth } = getState();
      const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
        code,
        orderAmount,
        userId: Auth.Auth?._id,
      });
      
      return {
        valid: response.data.valid,
        discount: response.data.discount,
        message: response.data.message,
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Validation failed');
    }
  }
);

const promotionSlice = createSlice({
  name: 'promotion',
  initialState: {
    // Applied promotion state
    applied: {
      code: '',
      discount: 0,
      promotionId: null,
      reservationId: null,
      expiresAt: null,
      remainingUses: null,
    },
    
    // Available promotions
    available: [],
    
    // UI states
    loading: false,
    validating: false,
    applying: false,
    error: null,
    
    // Validation state
    lastValidatedAmount: 0,
    lastValidatedAt: null,
    
    // Session persistence
    sessionKey: null,
  },
  reducers: {
    // Apply promotion manually (from modal or direct input)
    applyPromotion: (state, action) => {
      const { code, discount, promotionId, reservationId, expiresAt, remainingUses } = action.payload;
      state.applied = {
        code,
        discount,
        promotionId,
        reservationId,
        expiresAt,
        remainingUses,
      };
      state.error = null;
      state.sessionKey = `promotion_${Date.now()}`;
    },
    
    // Clear applied promotion
    clearPromotion: (state) => {
      state.applied = {
        code: '',
        discount: 0,
        promotionId: null,
        reservationId: null,
        expiresAt: null,
        remainingUses: null,
      };
      state.error = null;
      state.sessionKey = null;
    },
    
    // Set validation state
    setValidating: (state, action) => {
      state.validating = action.payload;
    },
    
    // Set applying state
    setApplying: (state, action) => {
      state.applying = action.payload;
    },
    
    // Set error
    setError: (state, action) => {
      state.error = action.payload;
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Update last validated amount
    updateLastValidatedAmount: (state, action) => {
      state.lastValidatedAmount = action.payload;
      state.lastValidatedAt = Date.now();
    },
    
    // Restore from session storage
    restoreFromSession: (state, action) => {
      const { promotionData, bookingContext } = action.payload;
      if (promotionData && promotionData.sessionKey) {
        state.applied = {
          code: promotionData.code || '',
          discount: promotionData.discount || 0,
          promotionId: promotionData.promotionId || null,
          reservationId: promotionData.reservationId || null,
          expiresAt: promotionData.expiresAt || null,
          remainingUses: promotionData.remainingUses || null,
        };
        state.sessionKey = promotionData.sessionKey;
        state.lastValidatedAmount = bookingContext?.subtotal || 0;
      }
    },
    
    // Check if promotion is expired
    checkExpiration: (state) => {
      if (state.applied.expiresAt && new Date(state.applied.expiresAt) < new Date()) {
        state.applied = {
          code: '',
          discount: 0,
          promotionId: null,
          reservationId: null,
          expiresAt: null,
          remainingUses: null,
        };
        state.error = 'Promotion has expired';
        state.sessionKey = null;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch user promotions
      .addCase(fetchUserPromotions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserPromotions.fulfilled, (state, action) => {
        state.loading = false;
        state.available = action.payload;
      })
      .addCase(fetchUserPromotions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Apply promotion code
      .addCase(applyPromotionCode.pending, (state) => {
        state.applying = true;
        state.error = null;
      })
      .addCase(applyPromotionCode.fulfilled, (state, action) => {
        state.applying = false;
        state.applied = action.payload;
        state.sessionKey = `promotion_${Date.now()}`;
      })
      .addCase(applyPromotionCode.rejected, (state, action) => {
        state.applying = false;
        state.error = action.payload;
      })
      
      // Validate promotion
      .addCase(validatePromotion.pending, (state) => {
        state.validating = true;
      })
      .addCase(validatePromotion.fulfilled, (state, action) => {
        state.validating = false;
        if (!action.payload.valid) {
          state.applied = {
            code: '',
            discount: 0,
            promotionId: null,
            reservationId: null,
            expiresAt: null,
            remainingUses: null,
          };
          state.error = action.payload.message;
          state.sessionKey = null;
        }
      })
      .addCase(validatePromotion.rejected, (state, action) => {
        state.validating = false;
        state.applied = {
          code: '',
          discount: 0,
          promotionId: null,
          reservationId: null,
          expiresAt: null,
          remainingUses: null,
        };
        state.error = action.payload;
        state.sessionKey = null;
      });
  },
});

export const {
  applyPromotion,
  clearPromotion,
  setValidating,
  setApplying,
  setError,
  clearError,
  updateLastValidatedAmount,
  restoreFromSession,
  checkExpiration,
} = promotionSlice.actions;

// Selectors
export const selectAppliedPromotion = (state) => state.promotion.applied;
export const selectAvailablePromotions = (state) => state.promotion.available;
export const selectPromotionLoading = (state) => state.promotion.loading;
export const selectPromotionValidating = (state) => state.promotion.validating;
export const selectPromotionApplying = (state) => state.promotion.applying;
export const selectPromotionError = (state) => state.promotion.error;
export const selectPromotionSessionKey = (state) => state.promotion.sessionKey;
export const selectLastValidatedAmount = (state) => state.promotion.lastValidatedAmount;

// Helper selectors
export const selectHasAppliedPromotion = (state) => !!state.promotion.applied.code;
export const selectPromotionDiscount = (state) => state.promotion.applied.discount || 0;
export const selectIsPromotionExpired = (state) => {
  const { expiresAt } = state.promotion.applied;
  return expiresAt && new Date(expiresAt) < new Date();
};

export default promotionSlice.reducer;
