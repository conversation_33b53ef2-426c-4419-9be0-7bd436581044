.calendar-container {
  overflow-x: auto;
  padding-bottom: 1rem;
  width: 100%;
}

.calendar-grid {
  display: grid;
  grid-template-columns: 200px repeat(14, 1fr); /* 200px cho cột ph<PERSON>ng, các cột ng<PERSON>y chia đ<PERSON>u */
  gap: 1px;
  background-color: #dee2e6;
  min-width: 1200px;
  width: 100%;
}

.calendar-header,
.calendar-row {
  display: contents; /* Để các cell con hiển thị như grid items */
}

.calendar-cell {
  background-color: white;
  border: none;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 80px;
  overflow: hidden;
}

.room-header {
  background-color: #f8f9fa;
  font-weight: bold;
  text-align: center;
  justify-content: center;
  align-items: center;
}

.date-header {
  background-color: #f8f9fa;
  text-align: center;
  font-weight: bold;
  padding: 10px;
}

.date-header.weekend {
  background-color: #e9ecef;
}

.room-info {
  background-color: #f8f9fa;
  position: relative;
  text-align: center;
  padding: 10px 5px;
}

.date-cell {
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.booking-info {
  font-size: 0.75rem;
  line-height: 1.2;
  position: relative;
  width: 100%;
  text-align: center;
}

.guest-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Responsive cho mobile */
@media (max-width: 768px) {
  .calendar-grid {
    grid-template-columns: 150px repeat(14, minmax(80px, 1fr));
    min-width: 1000px;
  }
  
  .calendar-cell {
    padding: 4px;
    min-height: 60px;
    font-size: 0.75rem;
  }
}

/* Các class khác giữ nguyên */
.date-cell:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.date-cell.weekend {
  background-color: rgba(0, 0, 0, 0.02);
}

.date-cell.booked {
  background-color: rgba(220, 53, 69, 0.1);
}

.date-cell.booked:hover {
  background-color: rgba(220, 53, 69, 0.2);
}

.date-cell.available {
  background-color: rgba(40, 167, 69, 0.05);
}

.date-cell.available:hover {
  background-color: rgba(40, 167, 69, 0.15);
}

.date-cell.pending {
  background-color: rgba(255, 193, 7, 0.1);
}

.date-cell.pending:hover {
  background-color: rgba(255, 193, 7, 0.2);
}

.status-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 0.7rem;
}

.pending-icon {
  color: #ffc107;
}

.booked-icon {
  color: #dc3545;
}

.check-action-btn {
  margin-top: 5px;
  font-size: 0.7rem;
  padding: 2px 5px;
  width: 100%;
}

.available-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.available-icon {
  color: #28a745;
  opacity: 0.5;
}

.legend {
  margin-top: 0.5rem;
}

.legend-box {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  border: 1px solid #dee2e6;
  vertical-align: middle;
}

.legend-box.available {
  background-color: rgba(40, 167, 69, 0.05);
}

.legend-box.booked {
  background-color: rgba(220, 53, 69, 0.1);
}

.legend-box.pending {
  background-color: rgba(255, 193, 7, 0.1);
}

.booking-details {
  margin-top: 1rem;
}

.detail-item {
  display: flex;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.detail-label {
  font-weight: bold;
  width: 100px;
}

.detail-value {
  flex: 1;
}

.empty-state {
  display: table-row;
}

.empty-message {
  display: table-cell;
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.availability-summary {
  border-radius: 0.5rem;

}





