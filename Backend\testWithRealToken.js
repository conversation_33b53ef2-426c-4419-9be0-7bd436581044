const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

// Token từ logs (User 11)
const REAL_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.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.rBCDQqe8DgPIj7sqCK-AfxwmQeHTGGYKMf9_JlZ-g6M';

async function testWithRealToken() {
  console.log('🧪 Testing with Real User Token (User 11)');
  console.log('=' .repeat(50));

  try {
    console.log('\n👤 Testing with User 11 token:');
    const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {
      headers: {
        'Authorization': `Bearer ${REAL_TOKEN}`
      }
    });
    
    console.log('✅ Success! Total promotions:', response.data.promotions?.length || 0);
    
    const types = response.data.promotions?.reduce((acc, promo) => {
      acc[promo.assignmentType] = (acc[promo.assignmentType] || 0) + 1;
      return acc;
    }, {});
    
    console.log('   Types:', Object.entries(types || {}).map(([type, count]) => `${type}: ${count}`).join(', '));
    
    console.log('\n📋 Promotion Details:');
    response.data.promotions?.forEach((promo, index) => {
      console.log(`${index + 1}. ${promo.code} (${promo.assignmentType}) - ${promo.name}`);
      if (promo.assignmentType === 'PRIVATE') {
        console.log(`   👤 Assigned to User: ${promo.userId}`);
      }
    });

    // Check if User 11 has any private promotions
    const privatePromotions = response.data.promotions?.filter(p => p.assignmentType === 'PRIVATE') || [];
    console.log(`\n🔒 Private promotions for User 11: ${privatePromotions.length}`);
    
    if (privatePromotions.length === 0) {
      console.log('❌ ISSUE: User 11 should have private promotions but none found!');
      console.log('   Expected: VIP_USER2, BIRTHDAY_USER4, LOYALTY_USER5, FIRSTLOVE, MYSTERY80');
    } else {
      console.log('✅ User 11 has private promotions as expected');
    }

  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }
}

testWithRealToken();
