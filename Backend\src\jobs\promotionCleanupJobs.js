const cron = require('node-cron');
const mongoose = require('mongoose');
const Promotion = require('../models/Promotion');
const PromotionUser = require('../models/PromotionUser');
const Reservation = require('../models/reservation');

class PromotionCleanupJobs {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
  }

  // Start all cleanup jobs
  start() {
    if (this.isRunning) {
      console.log('Promotion cleanup jobs are already running');
      return;
    }

    console.log('Starting promotion cleanup jobs...');
    this.isRunning = true;

    // Cleanup expired reservations every 5 minutes
    this.jobs.set('expiredReservations', cron.schedule('*/5 * * * *', async () => {
      await this.cleanupExpiredReservations();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    }));

    // Rollback cancelled bookings every 10 minutes
    this.jobs.set('cancelledBookings', cron.schedule('*/10 * * * *', async () => {
      await this.rollbackCancelledBookings();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    }));

    // Cleanup orphaned promotion users every hour
    this.jobs.set('orphanedPromotionUsers', cron.schedule('0 * * * *', async () => {
      await this.cleanupOrphanedPromotionUsers();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    }));

    // Sync promotion usage counts every 6 hours
    this.jobs.set('syncUsageCounts', cron.schedule('0 */6 * * *', async () => {
      await this.syncPromotionUsageCounts();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    }));

    // Generate daily cleanup report at midnight
    this.jobs.set('dailyReport', cron.schedule('0 0 * * *', async () => {
      await this.generateDailyCleanupReport();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    }));

    console.log(`Started ${this.jobs.size} promotion cleanup jobs`);
  }

  // Stop all cleanup jobs
  stop() {
    if (!this.isRunning) {
      console.log('Promotion cleanup jobs are not running');
      return;
    }

    console.log('Stopping promotion cleanup jobs...');
    
    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`Stopped job: ${name}`);
    });
    
    this.jobs.clear();
    this.isRunning = false;
    console.log('All promotion cleanup jobs stopped');
  }

  // Cleanup expired promotion reservations
  async cleanupExpiredReservations() {
    try {
      console.log('🧹 Starting cleanup of expired promotion reservations...');
      
      // DEPRECATED: No reservation system
      const expiredCount = 0;
      
      if (expiredCount > 0) {
        console.log(`✅ Cleaned up ${expiredCount} expired promotion reservations`);
      }
      
      return expiredCount;
    } catch (error) {
      console.error('❌ Error cleaning up expired reservations:', error);
      return 0;
    }
  }

  // Rollback promotion usage for cancelled bookings
  async rollbackCancelledBookings() {
    try {
      console.log('🔄 Starting rollback of cancelled bookings...');

      let rolledBackCount = 0;

      // Find cancelled reservations that haven't been rolled back
      const cancelledReservations = await Reservation.find({
        status: 'CANCELLED',
        promotionId: { $ne: null },
        'metadata.promotionRolledBack': { $ne: true }
      });

      for (const reservation of cancelledReservations) {
        try {
          // Rollback promotion usage (updated method signature)
          await Promotion.rollbackPromotionUsage(
            reservation.promotionId,
            reservation.user
          );

          // Mark as rolled back
          reservation.metadata = {
            ...reservation.metadata,
            promotionRolledBack: true,
            rolledBackAt: new Date()
          };
          await reservation.save();

          rolledBackCount++;
        } catch (error) {
          console.error(`Failed to rollback reservation ${reservation._id}:`, error);
        }
      }
      
      if (rolledBackCount > 0) {
        console.log(`✅ Rolled back ${rolledBackCount} cancelled bookings`);
      }
      
      return rolledBackCount;
    } catch (error) {
      console.error('❌ Error rolling back cancelled bookings:', error);
      return 0;
    }
  }

  // Cleanup orphaned promotion user records
  async cleanupOrphanedPromotionUsers() {
    try {
      console.log('🧹 Starting cleanup of orphaned promotion users...');
      
      // Find PromotionUser records with non-existent reservations
      const orphanedUsers = await PromotionUser.aggregate([
        {
          $lookup: {
            from: 'reservations',
            localField: 'reservationId',
            foreignField: '_id',
            as: 'reservation'
          }
        },
        {
          $match: {
            reservation: { $size: 0 }
          }
        }
      ]);
      
      let cleanedCount = 0;
      
      for (const orphan of orphanedUsers) {
        try {
          // Mark as cancelled instead of deleting for audit purposes
          await PromotionUser.findByIdAndUpdate(orphan._id, {
            status: 'CANCELLED',
            updatedAt: new Date()
          });
          cleanedCount++;
        } catch (error) {
          console.error(`Failed to cleanup orphaned promotion user ${orphan._id}:`, error);
        }
      }
      
      if (cleanedCount > 0) {
        console.log(`✅ Cleaned up ${cleanedCount} orphaned promotion users`);
      }
      
      return cleanedCount;
    } catch (error) {
      console.error('❌ Error cleaning up orphaned promotion users:', error);
      return 0;
    }
  }

  // Sync promotion usage counts with actual usage
  async syncPromotionUsageCounts() {
    try {
      console.log('🔄 Starting sync of promotion usage counts...');
      
      const promotions = await Promotion.find({ isActive: true });
      let syncedCount = 0;
      
      for (const promotion of promotions) {
        try {
          // Count actual usage
          const actualUsage = await PromotionUser.countDocuments({
            promotionId: promotion._id,
            status: 'USED'
          });
          
          // Update if different
          if (promotion.usedCount !== actualUsage) {
            await Promotion.findByIdAndUpdate(promotion._id, {
              usedCount: actualUsage
            });
            
            console.log(`📊 Synced promotion ${promotion.code}: ${promotion.usedCount} -> ${actualUsage}`);
            syncedCount++;
          }
        } catch (error) {
          console.error(`Failed to sync promotion ${promotion._id}:`, error);
        }
      }
      
      if (syncedCount > 0) {
        console.log(`✅ Synced ${syncedCount} promotion usage counts`);
      }
      
      return syncedCount;
    } catch (error) {
      console.error('❌ Error syncing promotion usage counts:', error);
      return 0;
    }
  }

  // Generate daily cleanup report
  async generateDailyCleanupReport() {
    try {
      console.log('📊 Generating daily promotion cleanup report...');
      
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // Gather statistics (simplified - no reservation system)
      const stats = {
        expiredReservations: 0, // No reservation system
        cancelledReservations: 0, // No reservation system
        confirmedReservations: 0, // No reservation system
        activeReservations: 0, // No reservation system
        totalPromotionUsage: await PromotionUser.countDocuments({
          usedAt: { $gte: yesterday, $lt: today },
          status: 'USED'
        }),
        activePromotions: await Promotion.countDocuments({ isActive: true }),
        date: yesterday.toISOString().split('T')[0]
      };
      
      console.log('📈 Daily Promotion Cleanup Report:', stats);
      
      // You can extend this to send to monitoring service, email, etc.
      
      return stats;
    } catch (error) {
      console.error('❌ Error generating daily cleanup report:', error);
      return null;
    }
  }

  // Get job status
  getStatus() {
    return {
      isRunning: this.isRunning,
      jobCount: this.jobs.size,
      jobs: Array.from(this.jobs.keys())
    };
  }

  // Run specific cleanup manually
  async runCleanup(type) {
    switch (type) {
      case 'expired':
        return await this.cleanupExpiredReservations();
      case 'cancelled':
        return await this.rollbackCancelledBookings();
      case 'orphaned':
        return await this.cleanupOrphanedPromotionUsers();
      case 'sync':
        return await this.syncPromotionUsageCounts();
      case 'report':
        return await this.generateDailyCleanupReport();
      default:
        throw new Error(`Unknown cleanup type: ${type}`);
    }
  }
}

// Create singleton instance
const promotionCleanupJobs = new PromotionCleanupJobs();

module.exports = promotionCleanupJobs;
