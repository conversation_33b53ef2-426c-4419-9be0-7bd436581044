const UserPromotion = require('../../models/UserPromotion');
const Promotion = require('../../models/Promotion');
const User = require('../../models/user');

// Assign promotion to specific user (Admin only)
exports.assignPromotionToUser = async (req, res) => {
  try {
    const {
      userId,
      promotionId,
      assignmentType = 'ASSIGNED',
      maxUsage = 1,
      customExpiryDate = null,
      metadata = {}
    } = req.body;

    if (!userId || !promotionId) {
      return res.status(400).json({ message: 'User ID and Promotion ID are required' });
    }

    // Verify user exists
    const user = await User.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify promotion exists
    const promotion = await Promotion.findById(promotionId);
    if (!promotion) {
      return res.status(404).json({ message: 'Promotion not found' });
    }

    const assignment = await UserPromotion.assignToUser({
      userId,
      promotionId,
      assignmentType,
      assignedBy: req.user._id,
      maxUsage,
      customExpiryDate,
      metadata
    });

    res.status(201).json({
      success: true,
      message: 'Promotion assigned successfully',
      assignment
    });
  } catch (error) {
    console.error('Assign promotion error:', error);
    res.status(400).json({ message: error.message });
  }
};

// Bulk assign promotion to multiple users (Admin only)
exports.bulkAssignPromotion = async (req, res) => {
  try {
    const {
      userIds,
      promotionId,
      assignmentType = 'CAMPAIGN',
      maxUsage = 1,
      metadata = {}
    } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ message: 'User IDs array is required' });
    }

    if (!promotionId) {
      return res.status(400).json({ message: 'Promotion ID is required' });
    }

    // Verify promotion exists
    const promotion = await Promotion.findById(promotionId);
    if (!promotion) {
      return res.status(404).json({ message: 'Promotion not found' });
    }

    const result = await UserPromotion.bulkAssign(userIds, promotionId, {
      assignmentType,
      assignedBy: req.user._id,
      maxUsage,
      metadata
    });

    res.json({
      success: true,
      message: `Promotion assigned to ${result.success} out of ${result.total} users`,
      result
    });
  } catch (error) {
    console.error('Bulk assign promotion error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get user's assigned promotions (Customer)
exports.getUserPromotions = async (req, res) => {
  try {
    const userId = req.user._id;
    const { includePublic = true, status = 'ASSIGNED' } = req.query;

    const promotions = await UserPromotion.getUserAvailablePromotions(userId, {
      includePublic: includePublic === 'true',
      status
    });

    // Add usage percentage and availability info
    const promotionsWithDetails = promotions.map(assignment => {
      const promotion = assignment.promotionId;
      const usagePercentage = assignment.maxUsage > 0 
        ? Math.round((assignment.usageCount / assignment.maxUsage) * 100)
        : 0;

      return {
        ...assignment.toObject ? assignment.toObject() : assignment,
        promotion,
        usagePercentage,
        remainingUses: assignment.maxUsage - assignment.usageCount,
        isAvailable: assignment.isAvailable || (
          assignment.status === 'ASSIGNED' && 
          assignment.usageCount < assignment.maxUsage
        )
      };
    });

    res.json({
      success: true,
      promotions: promotionsWithDetails
    });
  } catch (error) {
    console.error('Get user promotions error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Revoke promotion from user (Admin only)
exports.revokePromotionFromUser = async (req, res) => {
  try {
    const { userId, promotionId } = req.body;

    if (!userId || !promotionId) {
      return res.status(400).json({ message: 'User ID and Promotion ID are required' });
    }

    const assignment = await UserPromotion.revokePromotion(
      userId,
      promotionId,
      req.user._id
    );

    res.json({
      success: true,
      message: 'Promotion revoked successfully',
      assignment
    });
  } catch (error) {
    console.error('Revoke promotion error:', error);
    res.status(400).json({ message: error.message });
  }
};

// Get all user promotion assignments (Admin only)
exports.getAllUserPromotions = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      promotionId,
      status,
      assignmentType
    } = req.query;

    const skip = (page - 1) * limit;
    let filter = {};

    if (userId) filter.userId = userId;
    if (promotionId) filter.promotionId = promotionId;
    if (status) filter.status = status;
    if (assignmentType) filter.assignmentType = assignmentType;

    const assignments = await UserPromotion.find(filter)
      .populate('userId', 'fullName email')
      .populate('promotionId', 'code name discountType discountValue')
      .populate('assignedBy', 'fullName email')
      .sort({ assignedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await UserPromotion.countDocuments(filter);

    res.json({
      success: true,
      assignments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get all user promotions error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Auto-assign promotions based on user behavior
exports.autoAssignPromotions = async (req, res) => {
  try {
    const { trigger, userId, metadata = {} } = req.body;

    if (!trigger || !userId) {
      return res.status(400).json({ message: 'Trigger and User ID are required' });
    }

    let assignmentReason;
    let promotionCriteria = {};

    // Define auto-assignment rules
    switch (trigger) {
      case 'USER_REGISTRATION':
        assignmentReason = 'WELCOME_BONUS';
        promotionCriteria = { 'metadata.autoTrigger': 'welcome' };
        break;
      
      case 'FIRST_BOOKING':
        assignmentReason = 'MILESTONE_REWARD';
        promotionCriteria = { 'metadata.autoTrigger': 'first_booking' };
        break;
      
      case 'LOYALTY_MILESTONE':
        assignmentReason = 'LOYALTY_REWARD';
        promotionCriteria = { 'metadata.autoTrigger': 'loyalty' };
        break;
      
      case 'BIRTHDAY':
        assignmentReason = 'BIRTHDAY_GIFT';
        promotionCriteria = { 'metadata.autoTrigger': 'birthday' };
        break;
      
      default:
        return res.status(400).json({ message: 'Invalid trigger type' });
    }

    // Find eligible promotions
    const eligiblePromotions = await Promotion.find({
      isActive: true,
      isPersonalized: true,
      ...promotionCriteria
    });

    const assignments = [];
    for (const promotion of eligiblePromotions) {
      try {
        const assignment = await UserPromotion.assignToUser({
          userId,
          promotionId: promotion._id,
          assignmentType: 'EARNED',
          metadata: {
            assignmentReason,
            triggerEvent: trigger,
            ...metadata
          }
        });
        assignments.push(assignment);
      } catch (error) {
        // Skip if already assigned
        console.log(`Skipping promotion ${promotion._id} for user ${userId}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `Auto-assigned ${assignments.length} promotions`,
      assignments
    });
  } catch (error) {
    console.error('Auto assign promotions error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get promotion assignment statistics (Admin only)
exports.getPromotionAssignmentStats = async (req, res) => {
  try {
    const { promotionId } = req.params;

    if (!promotionId) {
      return res.status(400).json({ message: 'Promotion ID is required' });
    }

    const stats = await UserPromotion.aggregate([
      { $match: { promotionId: new mongoose.Types.ObjectId(promotionId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalUsage: { $sum: '$usageCount' }
        }
      }
    ]);

    const assignmentTypes = await UserPromotion.aggregate([
      { $match: { promotionId: new mongoose.Types.ObjectId(promotionId) } },
      {
        $group: {
          _id: '$assignmentType',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        byStatus: stats,
        byAssignmentType: assignmentTypes
      }
    });
  } catch (error) {
    console.error('Get promotion assignment stats error:', error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = exports;
