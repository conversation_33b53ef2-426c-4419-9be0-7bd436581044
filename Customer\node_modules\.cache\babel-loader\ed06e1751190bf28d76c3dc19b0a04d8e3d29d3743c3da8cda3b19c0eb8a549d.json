{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\components\\\\PromotionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON>, Card, Badge, Spinner } from \"react-bootstrap\";\nimport { FaTag, FaTimes, FaCheck } from \"react-icons/fa\";\nimport axios from \"axios\";\nimport Utils from \"../../../../utils/Utils\";\nimport getApiUrl from \"../../../../utils/apiConfig\"; // Add this import\nimport { useAppSelector } from \"../../../../redux/store\"; // Add this import\nimport \"../../../../css/PromotionModal.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PromotionModal = ({\n  show,\n  onHide,\n  totalPrice,\n  onApplyPromotion,\n  currentPromotionId\n}) => {\n  _s();\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\n  const [applying, setApplying] = useState(false);\n  const [manualCode, setManualCode] = useState(\"\");\n  const [manualCodeError, setManualCodeError] = useState(\"\");\n  const [applyingManual, setApplyingManual] = useState(false);\n  const API_BASE_URL = getApiUrl(); // Add this line\n  const Auth = useAppSelector(state => state.Auth.Auth); // Add this line\n\n  useEffect(() => {\n    if (show && totalPrice > 0) {\n      fetchPromotions();\n    }\n  }, [show, totalPrice]);\n  const fetchPromotions = async () => {\n    setLoading(true);\n    try {\n      let promotionList = [];\n      try {\n        console.log(\"Fetching user promotions from API...\");\n        // Use user-specific promotions API that includes both assigned and public promotions\n        const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {\n          headers: Auth !== null && Auth !== void 0 && Auth.token ? {\n            Authorization: `Bearer ${Auth.token}`\n          } : {}\n        });\n        console.log(\"API Response:\", response.data);\n        promotionList = response.data.promotions || response.data.data || response.data || [];\n        console.log(\"User promotion list from API:\", promotionList);\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\n          console.log(\"API returned empty or invalid data\");\n          setPromotions([]);\n          setLoading(false);\n          return;\n        }\n      } catch (apiError) {\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\n        // Fallback to mock data only in development\n        promotionList = [{\n          _id: \"1\",\n          code: \"SAVE20\",\n          description: \"Save $20 on orders over $100\",\n          discountType: \"FIXED_AMOUNT\",\n          discountValue: 20,\n          minOrderAmount: 100,\n          maxDiscountAmount: 20,\n          endDate: \"2025-12-31\",\n          isAvailable: true,\n          userCanUse: true,\n          remainingUses: 2,\n          usageLimit: 100,\n          usedCount: 25,\n          usagePercentage: 25\n        }];\n      }\n      console.log(\"Total price for processing:\", totalPrice);\n      console.log(\"Processing\", promotionList.length, \"available promotions\");\n\n      // Process user promotions with proper availability checking\n      const processedPromotions = promotionList.map(promo => {\n        console.log(`Processing promotion:`, promo.code || promo._id);\n\n        // Check if current order meets minimum requirement\n        const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\n\n        // Check if user has remaining uses for this promotion\n        const hasRemainingUses = promo.remainingUses > 0;\n        const isAvailable = promo.isAvailable !== false; // Default to true if not specified\n\n        let discount = 0;\n        let isValid = meetsMinOrder && isAvailable && hasRemainingUses;\n        let message = \"\";\n        if (isValid) {\n          // Calculate discount for display\n          if (promo.discountType === \"PERCENTAGE\") {\n            discount = totalPrice * promo.discountValue / 100;\n            if (promo.maxDiscountAmount) {\n              discount = Math.min(discount, promo.maxDiscountAmount);\n            }\n          } else if (promo.discountType === \"FIXED_AMOUNT\") {\n            discount = promo.discountValue;\n          }\n          message = \"Ready to apply\";\n        } else {\n          if (!meetsMinOrder) {\n            message = `Minimum order amount: ${Utils.formatCurrency(promo.minOrderAmount || 0)}`;\n          } else if (!hasRemainingUses) {\n            message = \"No remaining uses\";\n          } else if (!isAvailable) {\n            message = \"Not available\";\n          } else {\n            message = \"Cannot be used\";\n          }\n          isValid = false;\n        }\n        return {\n          ...promo,\n          isValid,\n          discount,\n          message\n        };\n      });\n      console.log(\"Final processed promotions:\", processedPromotions);\n\n      // Sort promotions: Valid ones first, then by discount amount\n      const sortedPromotions = processedPromotions.sort((a, b) => {\n        // Valid promotions first\n        if (a.isValid && !b.isValid) return -1;\n        if (!a.isValid && b.isValid) return 1;\n\n        // Within same validity, sort by discount descending\n        return b.discount - a.discount;\n      });\n      setPromotions(sortedPromotions);\n    } catch (error) {\n      console.error(\"Error fetching promotions:\", error);\n      setPromotions([]);\n    }\n    setLoading(false);\n  };\n  const handleApplyPromotion = async promotion => {\n    if (!promotion.isValid || promotion.remainingUses === 0) return;\n    setApplying(true);\n    try {\n      try {\n        // Replace hardcoded URL with environment-based URL\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n          code: promotion.code,\n          orderAmount: totalPrice,\n          userId: Auth === null || Auth === void 0 ? void 0 : Auth._id // Thêm userId để kiểm tra user-specific usage limit\n        });\n        if (response.data.valid) {\n          onApplyPromotion({\n            code: promotion.code,\n            discount: response.data.discount,\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\n            promotionId: response.data.promotionId,\n            remainingUses: response.data.remainingUses\n          });\n          onHide();\n        }\n      } catch (apiError) {\n        // Mock logic remains the same\n        console.log(\"Using mock promotion application\");\n        onApplyPromotion({\n          code: promotion.code,\n          discount: promotion.discount,\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\n          promotionId: promotion._id\n        });\n        onHide();\n      }\n    } catch (error) {\n      console.error(\"Error applying promotion:\", error);\n    }\n    setApplying(false);\n  };\n  const handleRemovePromotion = () => {\n    onApplyPromotion({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n    onHide();\n  };\n  const handleApplyManualCode = async () => {\n    if (!manualCode.trim()) {\n      setManualCodeError(\"Please enter a promotion code\");\n      return;\n    }\n    setApplyingManual(true);\n    setManualCodeError(\"\");\n    try {\n      const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n        code: manualCode.trim().toUpperCase(),\n        orderAmount: totalPrice,\n        userId: Auth === null || Auth === void 0 ? void 0 : Auth._id\n      });\n      if (response.data.valid) {\n        onApplyPromotion({\n          code: response.data.promotionId ? manualCode.trim().toUpperCase() : manualCode.trim(),\n          discount: response.data.discount,\n          message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\n          promotionId: response.data.promotionId\n        });\n        onHide();\n      } else {\n        setManualCodeError(response.data.message || \"Invalid promotion code\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Failed to apply promotion code\";\n      setManualCodeError(errorMessage);\n    }\n    setApplyingManual(false);\n  };\n  const handleManualCodeChange = e => {\n    setManualCode(e.target.value);\n    if (manualCodeError) {\n      setManualCodeError(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\",\n        color: \"white\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), \"Select Promotion\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        color: \"white\",\n        maxHeight: \"60vh\",\n        overflowY: \"auto\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-3 manual-code-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), \"Enter Promotion Code\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-2 align-items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-grow-1\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control manual-code-input\",\n              placeholder: \"Enter promotion code (e.g., SAVE20)\",\n              value: manualCode,\n              onChange: handleManualCodeChange,\n              onKeyPress: e => {\n                if (e.key === 'Enter') {\n                  handleApplyManualCode();\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-light\",\n            onClick: handleApplyManualCode,\n            disabled: applyingManual || !manualCode.trim(),\n            style: {\n              minWidth: \"100px\",\n              height: \"38px\"\n            },\n            children: applyingManual ? /*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this) : \"Apply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), manualCodeError && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-danger mt-2 d-block\",\n          children: manualCodeError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"promotion-divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          variant: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: \"Loading promotions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [currentPromotionId && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Current Applied Promotion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"promotion-card current-promotion\",\n            style: {\n              backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n              borderColor: \"#28a745\",\n              border: \"2px solid #28a745\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"py-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"text-success me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: \"Applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-danger\",\n                  size: \"sm\",\n                  onClick: handleRemovePromotion,\n                  disabled: applying,\n                  children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 25\n                  }, this), \"Remove\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-3\",\n          children: [\"Available Promotions\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"small ms-2\",\n            style: {\n              color: 'rgba(255,255,255,0.6)'\n            },\n            children: [\"(\", promotions.filter(p => p.isValid).length, \" ready, \", promotions.filter(p => !p.isValid).length, \" require higher order)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), promotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          style: {\n            color: 'rgba(255,255,255,0.7)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n            size: 48,\n            className: \"mb-3\",\n            style: {\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No promotions available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-3\",\n          children: promotions.map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `promotion-card ${currentPromotionId === promotion._id ? 'current' : ''} ${!promotion.isValid || promotion.remainingUses === 0 ? 'disabled' : ''}`,\n              style: {\n                backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : promotion.isValid ? \"rgba(255,255,255,0.1)\" : \"rgba(255, 193, 7, 0.1)\",\n                borderColor: currentPromotionId === promotion._id ? \"#28a745\" : promotion.isValid ? \"rgba(255,255,255,0.3)\" : \"rgba(255, 193, 7, 0.5)\",\n                cursor: !promotion.isValid || promotion.remainingUses === 0 ? \"not-allowed\" : \"pointer\",\n                transition: \"all 0.3s ease\",\n                opacity: !promotion.isValid || promotion.remainingUses === 0 ? 0.7 : 1\n              },\n              onClick: () => promotion.isValid && promotion.remainingUses > 0 && handleApplyPromotion(promotion),\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"py-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-grow-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                        className: \"me-2 text-primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-0 fw-bold\",\n                        children: promotion.code\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 31\n                      }, this), currentPromotionId === promotion._id && /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"success\",\n                        className: \"ms-2\",\n                        children: \"Applied\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: promotion.isValid ? \"success\" : \"warning\",\n                        className: \"ms-2\",\n                        children: promotion.isValid ? \"Available\" : \"Requires Higher Order\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-2 small\",\n                      style: {\n                        color: 'rgba(255,255,255,0.7)'\n                      },\n                      children: promotion.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 29\n                    }, this), promotion.usageLimit && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-end align-items-center mb-1\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          style: {\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.7rem'\n                          },\n                          children: [promotion.usagePercentage || 0, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 394,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"progress\",\n                        style: {\n                          height: '3px',\n                          backgroundColor: 'rgba(255,255,255,0.15)',\n                          borderRadius: '2px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"progress-bar\",\n                          role: \"progressbar\",\n                          style: {\n                            width: `${promotion.usagePercentage || 0}%`,\n                            backgroundColor: promotion.usagePercentage >= 90 ? '#dc3545' : promotion.usagePercentage >= 70 ? '#ffc107' : '#28a745',\n                            transition: 'width 0.3s ease',\n                            borderRadius: '2px'\n                          },\n                          \"aria-valuenow\": promotion.usagePercentage || 0,\n                          \"aria-valuemin\": \"0\",\n                          \"aria-valuemax\": \"100\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `fw-bold ${promotion.isValid ? 'text-success' : 'text-warning'}`,\n                          children: promotion.isValid ? `Save ${Utils.formatCurrency(promotion.discount)}` : promotion.message\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 33\n                        }, this), promotion.remainingUses !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"small mt-1 usage-info\",\n                          children: [/*#__PURE__*/_jsxDEV(Badge, {\n                            bg: promotion.remainingUses > 0 ? \"info\" : \"warning\",\n                            className: \"me-1 usage-badge\",\n                            children: promotion.remainingUses > 0 ? `${promotion.remainingUses} uses left` : \"Usage limit reached\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 435,\n                            columnNumber: 37\n                          }, this), promotion.userUsageLimit && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"usage-info\",\n                            children: [\"(Max: \", promotion.userUsageLimit, \"/user)\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 445,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 434,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-end\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"small\",\n                          children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: promotion.isValid ? 'text-success' : 'text-warning',\n                            children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), \" \", promotion.isValid ? '✓' : '✗']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 37\n                          }, this), promotion.maxDiscountAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              color: 'rgba(255,255,255,0.6)'\n                            },\n                            children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscountAmount)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 461,\n                            columnNumber: 37\n                          }, this), promotion.endDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-success\",\n                            children: [\"Expires: \", new Date(promotion.endDate).toLocaleDateString(), \" \\u2713\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 466,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 21\n            }, this)\n          }, promotion._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-light\",\n        onClick: onHide,\n        disabled: applying,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n_s(PromotionModal, \"u81S65+qRu4IDuja4+ec+njrDP0=\", false, function () {\n  return [useAppSelector];\n});\n_c = PromotionModal;\nexport default PromotionModal;\nvar _c;\n$RefreshReg$(_c, \"PromotionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "<PERSON><PERSON>", "Card", "Badge", "Spinner", "FaTag", "FaTimes", "FaCheck", "axios", "Utils", "getApiUrl", "useAppSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PromotionModal", "show", "onHide", "totalPrice", "onApplyPromotion", "currentPromotionId", "_s", "promotions", "setPromotions", "loading", "setLoading", "selectedPromotion", "setSelectedPromotion", "applying", "setApplying", "manualCode", "setManualCode", "manualCodeError", "setManualCodeError", "<PERSON><PERSON><PERSON><PERSON>", "setApplyingManual", "API_BASE_URL", "<PERSON><PERSON>", "state", "fetchPromotions", "promotionList", "console", "log", "response", "get", "headers", "token", "Authorization", "data", "Array", "isArray", "length", "apiError", "message", "_id", "code", "description", "discountType", "discountValue", "minOrderAmount", "maxDiscountAmount", "endDate", "isAvailable", "userCanUse", "remainingUses", "usageLimit", "usedCount", "usagePercentage", "processedPromotions", "map", "promo", "meetsMinOrder", "hasRemainingUses", "discount", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "formatCurrency", "sortedPromotions", "sort", "a", "b", "error", "handleApplyPromotion", "promotion", "post", "orderAmount", "userId", "valid", "promotionId", "handleRemovePromotion", "handleApplyManualCode", "trim", "toUpperCase", "_error$response", "_error$response$data", "errorMessage", "handleManualCodeChange", "e", "target", "value", "size", "centered", "children", "Header", "closeButton", "style", "backgroundColor", "borderColor", "color", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "maxHeight", "overflowY", "type", "placeholder", "onChange", "onKeyPress", "key", "variant", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "height", "animation", "border", "filter", "p", "opacity", "cursor", "transition", "bg", "fontSize", "borderRadius", "role", "width", "undefined", "userUsageLimit", "Date", "toLocaleDateString", "Footer", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>/PromotionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ge, Spinner } from \"react-bootstrap\";\r\nimport { FaTag, FaTimes, FaCheck } from \"react-icons/fa\";\r\nimport axios from \"axios\";\r\nimport Utils from \"../../../../utils/Utils\";\r\nimport getApiUrl from \"../../../../utils/apiConfig\"; // Add this import\r\nimport { useAppSelector } from \"../../../../redux/store\"; // Add this import\r\nimport \"../../../../css/PromotionModal.css\";\r\n\r\nconst PromotionModal = ({ show, onHide, totalPrice, onApplyPromotion, currentPromotionId }) => {\r\n  const [promotions, setPromotions] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\r\n  const [applying, setApplying] = useState(false);\r\n  const [manualCode, setManualCode] = useState(\"\");\r\n  const [manualCodeError, setManualCodeError] = useState(\"\");\r\n  const [applyingManual, setApplyingManual] = useState(false);\r\n\r\n  const API_BASE_URL = getApiUrl(); // Add this line\r\n  const Auth = useAppSelector((state) => state.Auth.Auth); // Add this line\r\n\r\n  useEffect(() => {\r\n    if (show && totalPrice > 0) {\r\n      fetchPromotions();\r\n    }\r\n  }, [show, totalPrice]);\r\n\r\n  const fetchPromotions = async () => {\r\n    setLoading(true);\r\n    try {\r\n      let promotionList = [];\r\n      try {\r\n        console.log(\"Fetching user promotions from API...\");\r\n        // Use user-specific promotions API that includes both assigned and public promotions\r\n        const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {\r\n          headers: Auth?.token ? { Authorization: `Bearer ${Auth.token}` } : {}\r\n        });\r\n        console.log(\"API Response:\", response.data);\r\n\r\n        promotionList = response.data.promotions || response.data.data || response.data || [];\r\n        console.log(\"User promotion list from API:\", promotionList);\r\n\r\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\r\n          console.log(\"API returned empty or invalid data\");\r\n          setPromotions([]);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      } catch (apiError) {\r\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\r\n        // Fallback to mock data only in development\r\n        promotionList = [\r\n          {\r\n            _id: \"1\",\r\n            code: \"SAVE20\",\r\n            description: \"Save $20 on orders over $100\",\r\n            discountType: \"FIXED_AMOUNT\",\r\n            discountValue: 20,\r\n            minOrderAmount: 100,\r\n            maxDiscountAmount: 20,\r\n            endDate: \"2025-12-31\",\r\n            isAvailable: true,\r\n            userCanUse: true,\r\n            remainingUses: 2,\r\n            usageLimit: 100,\r\n            usedCount: 25,\r\n            usagePercentage: 25\r\n          }\r\n        ];\r\n      }\r\n      \r\n      console.log(\"Total price for processing:\", totalPrice);\r\n      console.log(\"Processing\", promotionList.length, \"available promotions\");\r\n      \r\n      // Process user promotions with proper availability checking\r\n      const processedPromotions = promotionList.map((promo) => {\r\n        console.log(`Processing promotion:`, promo.code || promo._id);\r\n\r\n        // Check if current order meets minimum requirement\r\n        const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\r\n\r\n        // Check if user has remaining uses for this promotion\r\n        const hasRemainingUses = promo.remainingUses > 0;\r\n        const isAvailable = promo.isAvailable !== false; // Default to true if not specified\r\n\r\n        let discount = 0;\r\n        let isValid = meetsMinOrder && isAvailable && hasRemainingUses;\r\n        let message = \"\";\r\n\r\n        if (isValid) {\r\n          // Calculate discount for display\r\n          if (promo.discountType === \"PERCENTAGE\") {\r\n            discount = (totalPrice * promo.discountValue) / 100;\r\n            if (promo.maxDiscountAmount) {\r\n              discount = Math.min(discount, promo.maxDiscountAmount);\r\n            }\r\n          } else if (promo.discountType === \"FIXED_AMOUNT\") {\r\n            discount = promo.discountValue;\r\n          }\r\n          message = \"Ready to apply\";\r\n        } else {\r\n          if (!meetsMinOrder) {\r\n            message = `Minimum order amount: ${Utils.formatCurrency(promo.minOrderAmount || 0)}`;\r\n          } else if (!hasRemainingUses) {\r\n            message = \"No remaining uses\";\r\n          } else if (!isAvailable) {\r\n            message = \"Not available\";\r\n          } else {\r\n            message = \"Cannot be used\";\r\n          }\r\n          isValid = false;\r\n        }\r\n        \r\n        return {\r\n          ...promo,\r\n          isValid,\r\n          discount,\r\n          message\r\n        };\r\n      });\r\n      \r\n      console.log(\"Final processed promotions:\", processedPromotions);\r\n      \r\n      // Sort promotions: Valid ones first, then by discount amount\r\n      const sortedPromotions = processedPromotions.sort((a, b) => {\r\n        // Valid promotions first\r\n        if (a.isValid && !b.isValid) return -1;\r\n        if (!a.isValid && b.isValid) return 1;\r\n        \r\n        // Within same validity, sort by discount descending\r\n        return b.discount - a.discount;\r\n      });\r\n      \r\n      setPromotions(sortedPromotions);\r\n    } catch (error) {\r\n      console.error(\"Error fetching promotions:\", error);\r\n      setPromotions([]);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleApplyPromotion = async (promotion) => {\r\n    if (!promotion.isValid || promotion.remainingUses === 0) return;\r\n    \r\n    setApplying(true);\r\n    try {\r\n      try {\r\n        // Replace hardcoded URL with environment-based URL\r\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n          code: promotion.code,\r\n          orderAmount: totalPrice,\r\n          userId: Auth?._id, // Thêm userId để kiểm tra user-specific usage limit\r\n        });\r\n        \r\n        if (response.data.valid) {\r\n          onApplyPromotion({\r\n            code: promotion.code,\r\n            discount: response.data.discount,\r\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\r\n            promotionId: response.data.promotionId,\r\n            remainingUses: response.data.remainingUses,\r\n          });\r\n          onHide();\r\n        }\r\n      } catch (apiError) {\r\n        // Mock logic remains the same\r\n        console.log(\"Using mock promotion application\");\r\n        onApplyPromotion({\r\n          code: promotion.code,\r\n          discount: promotion.discount,\r\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\r\n          promotionId: promotion._id,\r\n        });\r\n        onHide();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error applying promotion:\", error);\r\n    }\r\n    setApplying(false);\r\n  };\r\n\r\n  const handleRemovePromotion = () => {\r\n    onApplyPromotion({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null,\r\n    });\r\n    onHide();\r\n  };\r\n\r\n  const handleApplyManualCode = async () => {\r\n    if (!manualCode.trim()) {\r\n      setManualCodeError(\"Please enter a promotion code\");\r\n      return;\r\n    }\r\n\r\n    setApplyingManual(true);\r\n    setManualCodeError(\"\");\r\n\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n        code: manualCode.trim().toUpperCase(),\r\n        orderAmount: totalPrice,\r\n        userId: Auth?._id,\r\n      });\r\n\r\n      if (response.data.valid) {\r\n        onApplyPromotion({\r\n          code: response.data.promotionId ? manualCode.trim().toUpperCase() : manualCode.trim(),\r\n          discount: response.data.discount,\r\n          message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\r\n          promotionId: response.data.promotionId,\r\n        });\r\n        onHide();\r\n      } else {\r\n        setManualCodeError(response.data.message || \"Invalid promotion code\");\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || \"Failed to apply promotion code\";\r\n      setManualCodeError(errorMessage);\r\n    }\r\n\r\n    setApplyingManual(false);\r\n  };\r\n\r\n  const handleManualCodeChange = (e) => {\r\n    setManualCode(e.target.value);\r\n    if (manualCodeError) {\r\n      setManualCodeError(\"\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\r\n      <Modal.Header \r\n        closeButton \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\",\r\n          color: \"white\"\r\n        }}\r\n      >\r\n        <Modal.Title className=\"d-flex align-items-center\">\r\n          <FaTag className=\"me-2\" />\r\n          Select Promotion\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      \r\n      <Modal.Body\r\n        style={{\r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\",\r\n          color: \"white\",\r\n          maxHeight: \"60vh\",\r\n          overflowY: \"auto\"\r\n        }}\r\n      >\r\n        {/* Manual Promotion Code Input */}\r\n        <div className=\"mb-4 p-3 manual-code-section\">\r\n          <h6 className=\"mb-3\">\r\n            <FaTag className=\"me-2\" />\r\n            Enter Promotion Code\r\n          </h6>\r\n          <div className=\"d-flex gap-2 align-items-start\">\r\n            <div className=\"flex-grow-1\">\r\n              <input\r\n                type=\"text\"\r\n                className=\"form-control manual-code-input\"\r\n                placeholder=\"Enter promotion code (e.g., SAVE20)\"\r\n                value={manualCode}\r\n                onChange={handleManualCodeChange}\r\n                onKeyPress={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    handleApplyManualCode();\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n            <Button\r\n              variant=\"outline-light\"\r\n              onClick={handleApplyManualCode}\r\n              disabled={applyingManual || !manualCode.trim()}\r\n              style={{ minWidth: \"100px\", height: \"38px\" }}\r\n            >\r\n              {applyingManual ? (\r\n                <Spinner animation=\"border\" size=\"sm\" />\r\n              ) : (\r\n                \"Apply\"\r\n              )}\r\n            </Button>\r\n          </div>\r\n          {manualCodeError && (\r\n            <small className=\"text-danger mt-2 d-block\">{manualCodeError}</small>\r\n          )}\r\n        </div>\r\n\r\n        <hr className=\"promotion-divider\" />\r\n\r\n        {loading ? (\r\n          <div className=\"text-center py-4\">\r\n            <Spinner animation=\"border\" variant=\"light\" />\r\n            <div className=\"mt-2\">Loading promotions...</div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Current promotion section */}\r\n            {currentPromotionId && (\r\n              <div className=\"mb-4\">\r\n                <h6 className=\"mb-3\">Current Applied Promotion</h6>\r\n                <Card \r\n                  className=\"promotion-card current-promotion\"\r\n                  style={{ \r\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                    borderColor: \"#28a745\",\r\n                    border: \"2px solid #28a745\"\r\n                  }}\r\n                >\r\n                  <Card.Body className=\"py-3\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <FaCheck className=\"text-success me-2\" />\r\n                        <span className=\"text-success fw-bold\">Applied</span>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={handleRemovePromotion}\r\n                        disabled={applying}\r\n                      >\r\n                        <FaTimes className=\"me-1\" />\r\n                        Remove\r\n                      </Button>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            )}\r\n\r\n            {/* Promotions section */}\r\n            <h6 className=\"mb-3\">\r\n              Available Promotions \r\n              <span className=\"small ms-2\" style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                ({promotions.filter(p => p.isValid).length} ready, {promotions.filter(p => !p.isValid).length} require higher order)\r\n              </span>\r\n            </h6>\r\n            {promotions.length === 0 ? (\r\n              <div className=\"text-center py-4\" style={{color: 'rgba(255,255,255,0.7)'}}>\r\n                <FaTag size={48} className=\"mb-3\" style={{opacity: 0.5}} />\r\n                <div>No promotions available</div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"row g-3\">\r\n                {promotions.map((promotion) => (\r\n                  <div key={promotion._id} className=\"col-12\">\r\n                    <Card\r\n                      className={`promotion-card ${currentPromotionId === promotion._id ? 'current' : ''} ${!promotion.isValid || promotion.remainingUses === 0 ? 'disabled' : ''}`}\r\n                      style={{\r\n                        backgroundColor: currentPromotionId === promotion._id \r\n                          ? \"rgba(40, 167, 69, 0.2)\" \r\n                          : promotion.isValid \r\n                            ? \"rgba(255,255,255,0.1)\" \r\n                            : \"rgba(255, 193, 7, 0.1)\",\r\n                        borderColor: currentPromotionId === promotion._id \r\n                          ? \"#28a745\" \r\n                          : promotion.isValid \r\n                            ? \"rgba(255,255,255,0.3)\" \r\n                            : \"rgba(255, 193, 7, 0.5)\",\r\n                        cursor: !promotion.isValid || promotion.remainingUses === 0 ? \"not-allowed\" : \"pointer\",\r\n                        transition: \"all 0.3s ease\",\r\n                        opacity: !promotion.isValid || promotion.remainingUses === 0 ? 0.7 : 1\r\n                      }}\r\n                      onClick={() => promotion.isValid && promotion.remainingUses > 0 && handleApplyPromotion(promotion)}\r\n                    >\r\n                      <Card.Body className=\"py-3\">\r\n                        <div className=\"d-flex justify-content-between align-items-start\">\r\n                          <div className=\"flex-grow-1\">\r\n                            <div className=\"d-flex align-items-center mb-2\">\r\n                              <FaTag className=\"me-2 text-primary\" />\r\n                              <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                              {currentPromotionId === promotion._id && (\r\n                                <Badge bg=\"success\" className=\"ms-2\">Applied</Badge>\r\n                              )}\r\n                              <Badge bg={promotion.isValid ? \"success\" : \"warning\"} className=\"ms-2\">\r\n                                {promotion.isValid ? \"Available\" : \"Requires Higher Order\"}\r\n                              </Badge>\r\n                            </div>\r\n                            \r\n                            <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n\r\n                            {/* Usage Progress Bar */}\r\n                            {promotion.usageLimit && (\r\n                              <div className=\"mb-2\">\r\n                                <div className=\"d-flex justify-content-end align-items-center mb-1\">\r\n                                  <small style={{color: 'rgba(255,255,255,0.6)', fontSize: '0.7rem'}}>\r\n                                    {promotion.usagePercentage || 0}%\r\n                                  </small>\r\n                                </div>\r\n                                <div\r\n                                  className=\"progress\"\r\n                                  style={{\r\n                                    height: '3px',\r\n                                    backgroundColor: 'rgba(255,255,255,0.15)',\r\n                                    borderRadius: '2px'\r\n                                  }}\r\n                                >\r\n                                  <div\r\n                                    className=\"progress-bar\"\r\n                                    role=\"progressbar\"\r\n                                    style={{\r\n                                      width: `${promotion.usagePercentage || 0}%`,\r\n                                      backgroundColor: promotion.usagePercentage >= 90\r\n                                        ? '#dc3545'\r\n                                        : promotion.usagePercentage >= 70\r\n                                        ? '#ffc107'\r\n                                        : '#28a745',\r\n                                      transition: 'width 0.3s ease',\r\n                                      borderRadius: '2px'\r\n                                    }}\r\n                                    aria-valuenow={promotion.usagePercentage || 0}\r\n                                    aria-valuemin=\"0\"\r\n                                    aria-valuemax=\"100\"\r\n                                  ></div>\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n                            \r\n                            <div className=\"d-flex justify-content-between align-items-center\">\r\n                              <div>\r\n                                <span className={`fw-bold ${promotion.isValid ? 'text-success' : 'text-warning'}`}>\r\n                                  {promotion.isValid ? `Save ${Utils.formatCurrency(promotion.discount)}` : promotion.message}\r\n                                </span>\r\n                                {/* Usage Information */}\r\n                                {promotion.remainingUses !== undefined && (\r\n                                  <div className=\"small mt-1 usage-info\">\r\n                                    <Badge\r\n                                      bg={promotion.remainingUses > 0 ? \"info\" : \"warning\"}\r\n                                      className=\"me-1 usage-badge\"\r\n                                    >\r\n                                      {promotion.remainingUses > 0\r\n                                        ? `${promotion.remainingUses} uses left`\r\n                                        : \"Usage limit reached\"\r\n                                      }\r\n                                    </Badge>\r\n                                    {promotion.userUsageLimit && (\r\n                                      <span className=\"usage-info\">\r\n                                        (Max: {promotion.userUsageLimit}/user)\r\n                                      </span>\r\n                                    )}\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n\r\n                              <div className=\"text-end\">\r\n                                <div className=\"small\">\r\n                                  {promotion.minOrderAmount && (\r\n                                    <div className={promotion.isValid ? 'text-success' : 'text-warning'}>\r\n                                      Min: {Utils.formatCurrency(promotion.minOrderAmount)} {promotion.isValid ? '✓' : '✗'}\r\n                                    </div>\r\n                                  )}\r\n                                  {promotion.maxDiscountAmount && (\r\n                                    <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                      Max: {Utils.formatCurrency(promotion.maxDiscountAmount)}\r\n                                    </div>\r\n                                  )}\r\n                                  {promotion.endDate && (\r\n                                    <div className=\"text-success\">\r\n                                      Expires: {new Date(promotion.endDate).toLocaleDateString()} ✓\r\n                                    </div>\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </Modal.Body>\r\n      \r\n      <Modal.Footer \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\"\r\n        }}\r\n      >\r\n        <Button variant=\"outline-light\" onClick={onHide} disabled={applying}>\r\n          Close\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default PromotionModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACrE,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACxD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,6BAA6B,CAAC,CAAC;AACrD,SAASC,cAAc,QAAQ,yBAAyB,CAAC,CAAC;AAC1D,OAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,UAAU;EAAEC,gBAAgB;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMuC,YAAY,GAAG3B,SAAS,CAAC,CAAC,CAAC,CAAC;EAClC,MAAM4B,IAAI,GAAG3B,cAAc,CAAE4B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC;;EAEzDvC,SAAS,CAAC,MAAM;IACd,IAAIkB,IAAI,IAAIE,UAAU,GAAG,CAAC,EAAE;MAC1BqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIe,aAAa,GAAG,EAAE;MACtB,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD;QACA,MAAMC,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGR,YAAY,sBAAsB,EAAE;UACtES,OAAO,EAAER,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAES,KAAK,GAAG;YAAEC,aAAa,EAAE,UAAUV,IAAI,CAACS,KAAK;UAAG,CAAC,GAAG,CAAC;QACtE,CAAC,CAAC;QACFL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAACK,IAAI,CAAC;QAE3CR,aAAa,GAAGG,QAAQ,CAACK,IAAI,CAAC1B,UAAU,IAAIqB,QAAQ,CAACK,IAAI,CAACA,IAAI,IAAIL,QAAQ,CAACK,IAAI,IAAI,EAAE;QACrFP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,aAAa,CAAC;QAE3D,IAAI,CAACS,KAAK,CAACC,OAAO,CAACV,aAAa,CAAC,IAAIA,aAAa,CAACW,MAAM,KAAK,CAAC,EAAE;UAC/DV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjDnB,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO2B,QAAQ,EAAE;QACjBX,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEU,QAAQ,CAACC,OAAO,EAAE,6BAA6B,CAAC;QAC1E;QACAb,aAAa,GAAG,CACd;UACEc,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,8BAA8B;UAC3CC,YAAY,EAAE,cAAc;UAC5BC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,iBAAiB,EAAE,EAAE;UACrBC,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,IAAI;UACjBC,UAAU,EAAE,IAAI;UAChBC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,GAAG;UACfC,SAAS,EAAE,EAAE;UACbC,eAAe,EAAE;QACnB,CAAC,CACF;MACH;MAEA1B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAExB,UAAU,CAAC;MACtDuB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,aAAa,CAACW,MAAM,EAAE,sBAAsB,CAAC;;MAEvE;MACA,MAAMiB,mBAAmB,GAAG5B,aAAa,CAAC6B,GAAG,CAAEC,KAAK,IAAK;QACvD7B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4B,KAAK,CAACf,IAAI,IAAIe,KAAK,CAAChB,GAAG,CAAC;;QAE7D;QACA,MAAMiB,aAAa,GAAGrD,UAAU,KAAKoD,KAAK,CAACX,cAAc,IAAI,CAAC,CAAC;;QAE/D;QACA,MAAMa,gBAAgB,GAAGF,KAAK,CAACN,aAAa,GAAG,CAAC;QAChD,MAAMF,WAAW,GAAGQ,KAAK,CAACR,WAAW,KAAK,KAAK,CAAC,CAAC;;QAEjD,IAAIW,QAAQ,GAAG,CAAC;QAChB,IAAIC,OAAO,GAAGH,aAAa,IAAIT,WAAW,IAAIU,gBAAgB;QAC9D,IAAInB,OAAO,GAAG,EAAE;QAEhB,IAAIqB,OAAO,EAAE;UACX;UACA,IAAIJ,KAAK,CAACb,YAAY,KAAK,YAAY,EAAE;YACvCgB,QAAQ,GAAIvD,UAAU,GAAGoD,KAAK,CAACZ,aAAa,GAAI,GAAG;YACnD,IAAIY,KAAK,CAACV,iBAAiB,EAAE;cAC3Ba,QAAQ,GAAGE,IAAI,CAACC,GAAG,CAACH,QAAQ,EAAEH,KAAK,CAACV,iBAAiB,CAAC;YACxD;UACF,CAAC,MAAM,IAAIU,KAAK,CAACb,YAAY,KAAK,cAAc,EAAE;YAChDgB,QAAQ,GAAGH,KAAK,CAACZ,aAAa;UAChC;UACAL,OAAO,GAAG,gBAAgB;QAC5B,CAAC,MAAM;UACL,IAAI,CAACkB,aAAa,EAAE;YAClBlB,OAAO,GAAG,yBAAyB7C,KAAK,CAACqE,cAAc,CAACP,KAAK,CAACX,cAAc,IAAI,CAAC,CAAC,EAAE;UACtF,CAAC,MAAM,IAAI,CAACa,gBAAgB,EAAE;YAC5BnB,OAAO,GAAG,mBAAmB;UAC/B,CAAC,MAAM,IAAI,CAACS,WAAW,EAAE;YACvBT,OAAO,GAAG,eAAe;UAC3B,CAAC,MAAM;YACLA,OAAO,GAAG,gBAAgB;UAC5B;UACAqB,OAAO,GAAG,KAAK;QACjB;QAEA,OAAO;UACL,GAAGJ,KAAK;UACRI,OAAO;UACPD,QAAQ;UACRpB;QACF,CAAC;MACH,CAAC,CAAC;MAEFZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0B,mBAAmB,CAAC;;MAE/D;MACA,MAAMU,gBAAgB,GAAGV,mBAAmB,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1D;QACA,IAAID,CAAC,CAACN,OAAO,IAAI,CAACO,CAAC,CAACP,OAAO,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAACM,CAAC,CAACN,OAAO,IAAIO,CAAC,CAACP,OAAO,EAAE,OAAO,CAAC;;QAErC;QACA,OAAOO,CAAC,CAACR,QAAQ,GAAGO,CAAC,CAACP,QAAQ;MAChC,CAAC,CAAC;MAEFlD,aAAa,CAACuD,gBAAgB,CAAC;IACjC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD3D,aAAa,CAAC,EAAE,CAAC;IACnB;IACAE,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM0D,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACpB,aAAa,KAAK,CAAC,EAAE;IAEzDnC,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,IAAI;QACF;QACA,MAAMc,QAAQ,GAAG,MAAMpC,KAAK,CAAC8E,IAAI,CAAC,GAAGjD,YAAY,uBAAuB,EAAE;UACxEmB,IAAI,EAAE6B,SAAS,CAAC7B,IAAI;UACpB+B,WAAW,EAAEpE,UAAU;UACvBqE,MAAM,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,GAAG,CAAE;QACrB,CAAC,CAAC;QAEF,IAAIX,QAAQ,CAACK,IAAI,CAACwC,KAAK,EAAE;UACvBrE,gBAAgB,CAAC;YACfoC,IAAI,EAAE6B,SAAS,CAAC7B,IAAI;YACpBkB,QAAQ,EAAE9B,QAAQ,CAACK,IAAI,CAACyB,QAAQ;YAChCpB,OAAO,EAAE,uBAAuB7C,KAAK,CAACqE,cAAc,CAAClC,QAAQ,CAACK,IAAI,CAACyB,QAAQ,CAAC,EAAE;YAC9EgB,WAAW,EAAE9C,QAAQ,CAACK,IAAI,CAACyC,WAAW;YACtCzB,aAAa,EAAErB,QAAQ,CAACK,IAAI,CAACgB;UAC/B,CAAC,CAAC;UACF/C,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC,OAAOmC,QAAQ,EAAE;QACjB;QACAX,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CvB,gBAAgB,CAAC;UACfoC,IAAI,EAAE6B,SAAS,CAAC7B,IAAI;UACpBkB,QAAQ,EAAEW,SAAS,CAACX,QAAQ;UAC5BpB,OAAO,EAAE,uBAAuB7C,KAAK,CAACqE,cAAc,CAACO,SAAS,CAACX,QAAQ,CAAC,EAAE;UAC1EgB,WAAW,EAAEL,SAAS,CAAC9B;QACzB,CAAC,CAAC;QACFrC,MAAM,CAAC,CAAC;MACV;IACF,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACdzC,OAAO,CAACyC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;IACArD,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM6D,qBAAqB,GAAGA,CAAA,KAAM;IAClCvE,gBAAgB,CAAC;MACfoC,IAAI,EAAE,EAAE;MACRkB,QAAQ,EAAE,CAAC;MACXpB,OAAO,EAAE,EAAE;MACXoC,WAAW,EAAE;IACf,CAAC,CAAC;IACFxE,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM0E,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC7D,UAAU,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACtB3D,kBAAkB,CAAC,+BAA+B,CAAC;MACnD;IACF;IAEAE,iBAAiB,CAAC,IAAI,CAAC;IACvBF,kBAAkB,CAAC,EAAE,CAAC;IAEtB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMpC,KAAK,CAAC8E,IAAI,CAAC,GAAGjD,YAAY,uBAAuB,EAAE;QACxEmB,IAAI,EAAEzB,UAAU,CAAC8D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCP,WAAW,EAAEpE,UAAU;QACvBqE,MAAM,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;MAChB,CAAC,CAAC;MAEF,IAAIX,QAAQ,CAACK,IAAI,CAACwC,KAAK,EAAE;QACvBrE,gBAAgB,CAAC;UACfoC,IAAI,EAAEZ,QAAQ,CAACK,IAAI,CAACyC,WAAW,GAAG3D,UAAU,CAAC8D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/D,UAAU,CAAC8D,IAAI,CAAC,CAAC;UACrFnB,QAAQ,EAAE9B,QAAQ,CAACK,IAAI,CAACyB,QAAQ;UAChCpB,OAAO,EAAE,uBAAuB7C,KAAK,CAACqE,cAAc,CAAClC,QAAQ,CAACK,IAAI,CAACyB,QAAQ,CAAC,EAAE;UAC9EgB,WAAW,EAAE9C,QAAQ,CAACK,IAAI,CAACyC;QAC7B,CAAC,CAAC;QACFxE,MAAM,CAAC,CAAC;MACV,CAAC,MAAM;QACLgB,kBAAkB,CAACU,QAAQ,CAACK,IAAI,CAACK,OAAO,IAAI,wBAAwB,CAAC;MACvE;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAZ,KAAK,CAACvC,QAAQ,cAAAmD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB9C,IAAI,cAAA+C,oBAAA,uBAApBA,oBAAA,CAAsB1C,OAAO,KAAI,gCAAgC;MACtFpB,kBAAkB,CAAC+D,YAAY,CAAC;IAClC;IAEA7D,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAM8D,sBAAsB,GAAIC,CAAC,IAAK;IACpCnE,aAAa,CAACmE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC7B,IAAIpE,eAAe,EAAE;MACnBC,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACErB,OAAA,CAACb,KAAK;IAACiB,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAACoF,IAAI,EAAC,IAAI;IAACC,QAAQ;IAAAC,QAAA,gBACnD3F,OAAA,CAACb,KAAK,CAACyG,MAAM;MACXC,WAAW;MACXC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,eAEF3F,OAAA,CAACb,KAAK,CAAC+G,KAAK;QAACC,SAAS,EAAC,2BAA2B;QAAAR,QAAA,gBAChD3F,OAAA,CAACR,KAAK;UAAC2G,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEfvG,OAAA,CAACb,KAAK,CAACqH,IAAI;MACTV,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCE,KAAK,EAAE,OAAO;QACdQ,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,gBAGF3F,OAAA;QAAKmG,SAAS,EAAC,8BAA8B;QAAAR,QAAA,gBAC3C3F,OAAA;UAAImG,SAAS,EAAC,MAAM;UAAAR,QAAA,gBAClB3F,OAAA,CAACR,KAAK;YAAC2G,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAE5B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvG,OAAA;UAAKmG,SAAS,EAAC,gCAAgC;UAAAR,QAAA,gBAC7C3F,OAAA;YAAKmG,SAAS,EAAC,aAAa;YAAAR,QAAA,eAC1B3F,OAAA;cACE2G,IAAI,EAAC,MAAM;cACXR,SAAS,EAAC,gCAAgC;cAC1CS,WAAW,EAAC,qCAAqC;cACjDpB,KAAK,EAAEtE,UAAW;cAClB2F,QAAQ,EAAExB,sBAAuB;cACjCyB,UAAU,EAAGxB,CAAC,IAAK;gBACjB,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE;kBACrBhC,qBAAqB,CAAC,CAAC;gBACzB;cACF;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvG,OAAA,CAACZ,MAAM;YACL4H,OAAO,EAAC,eAAe;YACvBC,OAAO,EAAElC,qBAAsB;YAC/BmC,QAAQ,EAAE5F,cAAc,IAAI,CAACJ,UAAU,CAAC8D,IAAI,CAAC,CAAE;YAC/Cc,KAAK,EAAE;cAAEqB,QAAQ,EAAE,OAAO;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAzB,QAAA,EAE5CrE,cAAc,gBACbtB,OAAA,CAACT,OAAO;cAAC8H,SAAS,EAAC,QAAQ;cAAC5B,IAAI,EAAC;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAExC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLnF,eAAe,iBACdpB,OAAA;UAAOmG,SAAS,EAAC,0BAA0B;UAAAR,QAAA,EAAEvE;QAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACrE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENvG,OAAA;QAAImG,SAAS,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEnC3F,OAAO,gBACNZ,OAAA;QAAKmG,SAAS,EAAC,kBAAkB;QAAAR,QAAA,gBAC/B3F,OAAA,CAACT,OAAO;UAAC8H,SAAS,EAAC,QAAQ;UAACL,OAAO,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CvG,OAAA;UAAKmG,SAAS,EAAC,MAAM;UAAAR,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,gBAENvG,OAAA,CAAAE,SAAA;QAAAyF,QAAA,GAEGnF,kBAAkB,iBACjBR,OAAA;UAAKmG,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACnB3F,OAAA;YAAImG,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAyB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDvG,OAAA,CAACX,IAAI;YACH8G,SAAS,EAAC,kCAAkC;YAC5CL,KAAK,EAAE;cACLC,eAAe,EAAE,wBAAwB;cACzCC,WAAW,EAAE,SAAS;cACtBsB,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,eAEF3F,OAAA,CAACX,IAAI,CAACmH,IAAI;cAACL,SAAS,EAAC,MAAM;cAAAR,QAAA,eACzB3F,OAAA;gBAAKmG,SAAS,EAAC,mDAAmD;gBAAAR,QAAA,gBAChE3F,OAAA;kBAAKmG,SAAS,EAAC,2BAA2B;kBAAAR,QAAA,gBACxC3F,OAAA,CAACN,OAAO;oBAACyG,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzCvG,OAAA;oBAAMmG,SAAS,EAAC,sBAAsB;oBAAAR,QAAA,EAAC;kBAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNvG,OAAA,CAACZ,MAAM;kBACL4H,OAAO,EAAC,gBAAgB;kBACxBvB,IAAI,EAAC,IAAI;kBACTwB,OAAO,EAAEnC,qBAAsB;kBAC/BoC,QAAQ,EAAElG,QAAS;kBAAA2E,QAAA,gBAEnB3F,OAAA,CAACP,OAAO;oBAAC0G,SAAS,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDvG,OAAA;UAAImG,SAAS,EAAC,MAAM;UAAAR,QAAA,GAAC,sBAEnB,eAAA3F,OAAA;YAAMmG,SAAS,EAAC,YAAY;YAACL,KAAK,EAAE;cAACG,KAAK,EAAE;YAAuB,CAAE;YAAAN,QAAA,GAAC,GACnE,EAACjF,UAAU,CAAC6G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1D,OAAO,CAAC,CAACvB,MAAM,EAAC,UAAQ,EAAC7B,UAAU,CAAC6G,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC1D,OAAO,CAAC,CAACvB,MAAM,EAAC,wBAChG;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACJ7F,UAAU,CAAC6B,MAAM,KAAK,CAAC,gBACtBvC,OAAA;UAAKmG,SAAS,EAAC,kBAAkB;UAACL,KAAK,EAAE;YAACG,KAAK,EAAE;UAAuB,CAAE;UAAAN,QAAA,gBACxE3F,OAAA,CAACR,KAAK;YAACiG,IAAI,EAAE,EAAG;YAACU,SAAS,EAAC,MAAM;YAACL,KAAK,EAAE;cAAC2B,OAAO,EAAE;YAAG;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DvG,OAAA;YAAA2F,QAAA,EAAK;UAAuB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAENvG,OAAA;UAAKmG,SAAS,EAAC,SAAS;UAAAR,QAAA,EACrBjF,UAAU,CAAC+C,GAAG,CAAEe,SAAS,iBACxBxE,OAAA;YAAyBmG,SAAS,EAAC,QAAQ;YAAAR,QAAA,eACzC3F,OAAA,CAACX,IAAI;cACH8G,SAAS,EAAE,kBAAkB3F,kBAAkB,KAAKgE,SAAS,CAAC9B,GAAG,GAAG,SAAS,GAAG,EAAE,IAAI,CAAC8B,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACpB,aAAa,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cAC9J0C,KAAK,EAAE;gBACLC,eAAe,EAAEvF,kBAAkB,KAAKgE,SAAS,CAAC9B,GAAG,GACjD,wBAAwB,GACxB8B,SAAS,CAACV,OAAO,GACf,uBAAuB,GACvB,wBAAwB;gBAC9BkC,WAAW,EAAExF,kBAAkB,KAAKgE,SAAS,CAAC9B,GAAG,GAC7C,SAAS,GACT8B,SAAS,CAACV,OAAO,GACf,uBAAuB,GACvB,wBAAwB;gBAC9B4D,MAAM,EAAE,CAAClD,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACpB,aAAa,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;gBACvFuE,UAAU,EAAE,eAAe;gBAC3BF,OAAO,EAAE,CAACjD,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACpB,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG;cACvE,CAAE;cACF6D,OAAO,EAAEA,CAAA,KAAMzC,SAAS,CAACV,OAAO,IAAIU,SAAS,CAACpB,aAAa,GAAG,CAAC,IAAImB,oBAAoB,CAACC,SAAS,CAAE;cAAAmB,QAAA,eAEnG3F,OAAA,CAACX,IAAI,CAACmH,IAAI;gBAACL,SAAS,EAAC,MAAM;gBAAAR,QAAA,eACzB3F,OAAA;kBAAKmG,SAAS,EAAC,kDAAkD;kBAAAR,QAAA,eAC/D3F,OAAA;oBAAKmG,SAAS,EAAC,aAAa;oBAAAR,QAAA,gBAC1B3F,OAAA;sBAAKmG,SAAS,EAAC,gCAAgC;sBAAAR,QAAA,gBAC7C3F,OAAA,CAACR,KAAK;wBAAC2G,SAAS,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvCvG,OAAA;wBAAImG,SAAS,EAAC,cAAc;wBAAAR,QAAA,EAAEnB,SAAS,CAAC7B;sBAAI;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACjD/F,kBAAkB,KAAKgE,SAAS,CAAC9B,GAAG,iBACnC1C,OAAA,CAACV,KAAK;wBAACsI,EAAE,EAAC,SAAS;wBAACzB,SAAS,EAAC,MAAM;wBAAAR,QAAA,EAAC;sBAAO;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CACpD,eACDvG,OAAA,CAACV,KAAK;wBAACsI,EAAE,EAAEpD,SAAS,CAACV,OAAO,GAAG,SAAS,GAAG,SAAU;wBAACqC,SAAS,EAAC,MAAM;wBAAAR,QAAA,EACnEnB,SAAS,CAACV,OAAO,GAAG,WAAW,GAAG;sBAAuB;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAENvG,OAAA;sBAAGmG,SAAS,EAAC,YAAY;sBAACL,KAAK,EAAE;wBAACG,KAAK,EAAE;sBAAuB,CAAE;sBAAAN,QAAA,EAAEnB,SAAS,CAAC5B;oBAAW;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAG7F/B,SAAS,CAACnB,UAAU,iBACnBrD,OAAA;sBAAKmG,SAAS,EAAC,MAAM;sBAAAR,QAAA,gBACnB3F,OAAA;wBAAKmG,SAAS,EAAC,oDAAoD;wBAAAR,QAAA,eACjE3F,OAAA;0BAAO8F,KAAK,EAAE;4BAACG,KAAK,EAAE,uBAAuB;4BAAE4B,QAAQ,EAAE;0BAAQ,CAAE;0BAAAlC,QAAA,GAChEnB,SAAS,CAACjB,eAAe,IAAI,CAAC,EAAC,GAClC;wBAAA;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNvG,OAAA;wBACEmG,SAAS,EAAC,UAAU;wBACpBL,KAAK,EAAE;0BACLsB,MAAM,EAAE,KAAK;0BACbrB,eAAe,EAAE,wBAAwB;0BACzC+B,YAAY,EAAE;wBAChB,CAAE;wBAAAnC,QAAA,eAEF3F,OAAA;0BACEmG,SAAS,EAAC,cAAc;0BACxB4B,IAAI,EAAC,aAAa;0BAClBjC,KAAK,EAAE;4BACLkC,KAAK,EAAE,GAAGxD,SAAS,CAACjB,eAAe,IAAI,CAAC,GAAG;4BAC3CwC,eAAe,EAAEvB,SAAS,CAACjB,eAAe,IAAI,EAAE,GAC5C,SAAS,GACTiB,SAAS,CAACjB,eAAe,IAAI,EAAE,GAC/B,SAAS,GACT,SAAS;4BACboE,UAAU,EAAE,iBAAiB;4BAC7BG,YAAY,EAAE;0BAChB,CAAE;0BACF,iBAAetD,SAAS,CAACjB,eAAe,IAAI,CAAE;0BAC9C,iBAAc,GAAG;0BACjB,iBAAc;wBAAK;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,eAEDvG,OAAA;sBAAKmG,SAAS,EAAC,mDAAmD;sBAAAR,QAAA,gBAChE3F,OAAA;wBAAA2F,QAAA,gBACE3F,OAAA;0BAAMmG,SAAS,EAAE,WAAW3B,SAAS,CAACV,OAAO,GAAG,cAAc,GAAG,cAAc,EAAG;0BAAA6B,QAAA,EAC/EnB,SAAS,CAACV,OAAO,GAAG,QAAQlE,KAAK,CAACqE,cAAc,CAACO,SAAS,CAACX,QAAQ,CAAC,EAAE,GAAGW,SAAS,CAAC/B;wBAAO;0BAAA2D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvF,CAAC,EAEN/B,SAAS,CAACpB,aAAa,KAAK6E,SAAS,iBACpCjI,OAAA;0BAAKmG,SAAS,EAAC,uBAAuB;0BAAAR,QAAA,gBACpC3F,OAAA,CAACV,KAAK;4BACJsI,EAAE,EAAEpD,SAAS,CAACpB,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG,SAAU;4BACrD+C,SAAS,EAAC,kBAAkB;4BAAAR,QAAA,EAE3BnB,SAAS,CAACpB,aAAa,GAAG,CAAC,GACxB,GAAGoB,SAAS,CAACpB,aAAa,YAAY,GACtC;0BAAqB;4BAAAgD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEpB,CAAC,EACP/B,SAAS,CAAC0D,cAAc,iBACvBlI,OAAA;4BAAMmG,SAAS,EAAC,YAAY;4BAAAR,QAAA,GAAC,QACrB,EAACnB,SAAS,CAAC0D,cAAc,EAAC,QAClC;0BAAA;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENvG,OAAA;wBAAKmG,SAAS,EAAC,UAAU;wBAAAR,QAAA,eACvB3F,OAAA;0BAAKmG,SAAS,EAAC,OAAO;0BAAAR,QAAA,GACnBnB,SAAS,CAACzB,cAAc,iBACvB/C,OAAA;4BAAKmG,SAAS,EAAE3B,SAAS,CAACV,OAAO,GAAG,cAAc,GAAG,cAAe;4BAAA6B,QAAA,GAAC,OAC9D,EAAC/F,KAAK,CAACqE,cAAc,CAACO,SAAS,CAACzB,cAAc,CAAC,EAAC,GAAC,EAACyB,SAAS,CAACV,OAAO,GAAG,GAAG,GAAG,GAAG;0BAAA;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjF,CACN,EACA/B,SAAS,CAACxB,iBAAiB,iBAC1BhD,OAAA;4BAAK8F,KAAK,EAAE;8BAACG,KAAK,EAAE;4BAAuB,CAAE;4BAAAN,QAAA,GAAC,OACvC,EAAC/F,KAAK,CAACqE,cAAc,CAACO,SAAS,CAACxB,iBAAiB,CAAC;0BAAA;4BAAAoD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CACN,EACA/B,SAAS,CAACvB,OAAO,iBAChBjD,OAAA;4BAAKmG,SAAS,EAAC,cAAc;4BAAAR,QAAA,GAAC,WACnB,EAAC,IAAIwC,IAAI,CAAC3D,SAAS,CAACvB,OAAO,CAAC,CAACmF,kBAAkB,CAAC,CAAC,EAAC,SAC7D;0BAAA;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC,GA1HC/B,SAAS,CAAC9B,GAAG;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2HlB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,eACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEbvG,OAAA,CAACb,KAAK,CAACkJ,MAAM;MACXvC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE;MACf,CAAE;MAAAL,QAAA,eAEF3F,OAAA,CAACZ,MAAM;QAAC4H,OAAO,EAAC,eAAe;QAACC,OAAO,EAAE5G,MAAO;QAAC6G,QAAQ,EAAElG,QAAS;QAAA2E,QAAA,EAAC;MAErE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAAC9F,EAAA,CAveIN,cAAc;EAAA,QAULL,cAAc;AAAA;AAAAwI,EAAA,GAVvBnI,cAAc;AAyepB,eAAeA,cAAc;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}