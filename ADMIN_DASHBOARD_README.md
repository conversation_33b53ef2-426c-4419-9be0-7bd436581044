# Admin Dashboard Implementation

## Tổng quan
Hệ thống Admin Dashboard đã được triển khai hoàn chỉnh với các tính năng:
- Thống kê tổng quan hệ thống
- <PERSON><PERSON><PERSON><PERSON> đồ doanh thu theo thời gian
- <PERSON><PERSON> bố khách sạn theo khu vực và phân loại
- Danh sách yêu cầu phê duyệt gần đây
- B<PERSON>o cáo vi phạm gần đây

## Cấu trúc Files

### Backend
```
Backend/src/route_controller/DashboardAdmin/
├── DashboardAdminController.js  # Controller xử lý logic
└── DashboardAdminRoute.js       # Routes định nghĩa endpoints
```

### Frontend
```
Admin/src/redux/adminDashboard/
├── actions.js     # Redux actions
├── factories.js   # API calls
├── reducer.js     # State management
└── saga.js        # Side effects handling

Admin/src/pages/dashboard/
└── DashboardPage.jsx  # Main dashboard component
```

## API Endpoints

### GET /api/dashboard-admin/metrics
**<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> dữ liệu thống kê cho admin dashboard

**Headers**:
```
Authorization: Bearer <admin-jwt-token>
```

**Query Parameters**:
- `period` (optional): 'day' | 'week' | 'month' | 'year'

**Response**:
```json
{
  "success": true,
  "data": {
    "totalHotels": 1245,
    "activeHotels": 987,
    "pendingApprovals": 58,
    "totalCustomers": 25430,
    "totalOwners": 156,
    "totalReservations": 8934,
    "totalRevenue": 12500000,
    "pendingReports": 23,
    "revenueData": {
      "labels": ["T1", "T2", "T3", ...],
      "datasets": [...]
    },
    "hotelDistributionData": {
      "labels": ["Miền Bắc", "Miền Trung", ...],
      "datasets": [...]
    },
    "hotelCategoryData": {
      "labels": ["5 sao", "4 sao", ...],
      "datasets": [...]
    },
    "recentApprovals": [...],
    "recentReports": [...]
  }
}
```

## Tính năng

### 1. Thống kê tổng quan
- Tổng số khách sạn
- Khách sạn đang hoạt động
- Yêu cầu chờ phê duyệt
- Tổng số khách hàng

### 2. Biểu đồ doanh thu
- Hiển thị doanh thu 12 tháng gần nhất
- Tự động tính toán từ các reservation đã hoàn thành
- Hỗ trợ format số tiền (K, M)

### 3. Phân bố khách sạn
- Theo khu vực địa lý
- Theo phân loại sao

### 4. Hoạt động gần đây
- Yêu cầu phê duyệt khách sạn mới
- Báo cáo vi phạm feedback

## Cách sử dụng

### 1. Khởi động Backend
```bash
cd Backend
npm start
```

### 2. Khởi động Frontend
```bash
cd Admin
npm start
```

### 3. Truy cập Dashboard
1. Đăng nhập với tài khoản admin
2. Chọn tab "Dashboard" trong sidebar
3. Xem các thống kê và biểu đồ

## Xử lý lỗi

### Loading States
- Hiển thị spinner khi đang tải dữ liệu
- Disable các button khi đang xử lý

### Error Handling
- Hiển thị thông báo lỗi rõ ràng
- Nút "Thử lại" để reload dữ liệu
- Fallback cho các trường hợp không có dữ liệu

### Empty States
- Hiển thị thông báo khi không có dữ liệu
- Icon và text phù hợp cho từng loại chart

## Testing

### Test API
```bash
cd Backend
node test-dashboard-api.js
```

### Test Frontend
1. Đăng nhập với tài khoản admin
2. Kiểm tra các tính năng:
   - Load dữ liệu dashboard
   - Thay đổi period filter
   - Refresh dữ liệu
   - Xử lý lỗi khi mất kết nối

## Bảo mật
- Yêu cầu JWT token hợp lệ
- Kiểm tra role ADMIN
- Validate input parameters
- Error handling không expose sensitive data

## Performance
- Pagination cho recent activities
- Efficient database queries với aggregation
- Caching có thể được thêm vào sau
- Lazy loading cho charts

## Mở rộng trong tương lai
- Real-time updates với WebSocket
- Export báo cáo PDF/Excel
- Advanced filtering và search
- Notification system
- Audit logs
