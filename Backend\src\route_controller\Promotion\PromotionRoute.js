const express = require('express');
const router = express.Router();
const promotionController = require('./PromotionController');
const checkCustomer = require('../../middlewares/checkCustomer');
const { isAdmin } = require('../../middlewares/checkAdmin');
const checkRole = require('../../middlewares/checkRole');
const { optionalAuth } = require('../../middlewares/optionalAuth');

// Create promotion (Admin only)
router.post('/', isAdmin, promotionController.createPromotion);

// Get all promotions (Admin or Customer)
router.get('/', optionalAuth, promotionController.getAllPromotions);

// Get user-specific promotions (both public and personal that user can use)
router.get('/user', checkCustomer, promotionController.getUserPromotions);

// Get public promotions for customers (exclude personalized)
router.get('/public', optionalAuth, promotionController.getPublicPromotions);

// Get single promotion by ID (Admin or Customer)
router.get('/:id', promotionController.getPromotionById);

// Update promotion (Admin only)
router.put('/:id', isAdmin, promotionController.updatePromotion);

// Delete promotion (Admin only)
router.delete('/:id', isAdmin, promotionController.deletePromotion);

// Toggle promotion status (Admin only)
router.patch('/:id/status', isAdmin, promotionController.togglePromotionStatus);

// Apply promotion (Customer only)
router.post('/apply', promotionController.applyPromotionCode);

// Get promotion usage statistics (Admin only)
router.get('/:id/stats', isAdmin, promotionController.getPromotionStats);

// Get user promotion history (Admin only)
router.get('/users/:userId/history', isAdmin, promotionController.getUserPromotionHistory);

// Get all promotion usage records (Admin only)
router.get('/usage/all', isAdmin, promotionController.getAllPromotionUsage);

// Check user promotion eligibility (Customer)
router.get('/:promotionId/eligibility', checkCustomer, promotionController.checkUserPromotionEligibility);

// Create personalized voucher (Admin only)
router.post('/personalized', isAdmin, promotionController.createPersonalizedVoucher);

// Create campaign vouchers (Admin only)
router.post('/campaign', isAdmin, promotionController.createCampaignVouchers);

// Confirm promotion usage when booking is successful (Customer)
router.post('/confirm', checkCustomer, promotionController.confirmPromotionUsage);

// Cancel promotion reservation (Customer)
router.post('/cancel', checkCustomer, promotionController.cancelPromotionReservation);

// Get user's active promotion reservations (Customer)
router.get('/reservations/active', checkCustomer, promotionController.getUserActiveReservations);

// Admin routes for promotion management
router.get('/reservations/all', isAdmin, promotionController.getAllPromotionReservations);
router.post('/cleanup/:type', isAdmin, promotionController.runCleanupJob);

module.exports = router;
