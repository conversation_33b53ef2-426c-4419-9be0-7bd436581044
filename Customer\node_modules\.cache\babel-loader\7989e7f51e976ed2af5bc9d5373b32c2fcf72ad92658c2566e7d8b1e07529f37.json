{"ast": null, "code": "import { combineReducers } from 'redux';\nimport AuthReducer from './auth/reducer';\nimport SearchReducer from './search/reducer';\nimport HotelReducer from './hotel/reducer';\nimport RoomReducer from './room/reducer';\nimport FeedbackReducer from './feedback/reducer';\nimport ReservationReducer from './reservations/reducer';\nimport ReportFeedbackReducer from './reportedFeedback/reducer';\nimport chatboxReducer from './chatbox/reducer';\nimport SocketReducer from './socket/socketSlice';\nimport messageReducer from './message/reducer';\nimport PromotionReducer from './promotion/reducer';\nimport promotionSliceReducer from './promotion/promotionSlice';\nconst rootReducer = combineReducers({\n  Auth: AuthReducer,\n  Search: SearchReducer,\n  hotel: HotelReducer,\n  Room: RoomReducer,\n  Feedback: FeedbackReducer,\n  Reservation: ReservationReducer,\n  ReportFeedback: ReportFeedbackReducer,\n  ChatBox: chatboxReducer,\n  Socket: SocketReducer,\n  Message: messageReducer,\n  Promotion: PromotionReducer,\n  promotion: promotionSliceReducer // New promotion slice\n});\nexport default rootReducer;", "map": {"version": 3, "names": ["combineReducers", "AuthReducer", "SearchReducer", "HotelReducer", "RoomReducer", "FeedbackReducer", "ReservationReducer", "ReportFeedbackReducer", "chatboxReducer", "SocketReducer", "messageReducer", "PromotionReducer", "promotionSliceReducer", "rootReducer", "<PERSON><PERSON>", "Search", "hotel", "Room", "<PERSON><PERSON><PERSON>", "Reservation", "ReportFeedback", "ChatBox", "Socket", "Message", "Promotion", "promotion"], "sources": ["E:/WDP301_UROOM/Customer/src/redux/root-reducer.js"], "sourcesContent": ["import { combineReducers } from 'redux';\r\nimport AuthReducer from './auth/reducer';\r\nimport SearchReducer from './search/reducer';\r\nimport HotelReducer from './hotel/reducer';\r\nimport RoomReducer from './room/reducer';\r\nimport FeedbackReducer from './feedback/reducer';\r\nimport ReservationReducer from './reservations/reducer';\r\nimport ReportFeedbackReducer from './reportedFeedback/reducer';\r\nimport chatboxReducer from './chatbox/reducer';\r\nimport SocketReducer from './socket/socketSlice';\r\nimport messageReducer from './message/reducer';\r\nimport PromotionReducer from './promotion/reducer';\r\nimport promotionSliceReducer from './promotion/promotionSlice';\r\n\r\nconst rootReducer = combineReducers({\r\n    Auth: AuthReducer,\r\n    Search: SearchReducer,\r\n    hotel: HotelReducer,\r\n    Room: RoomReducer,\r\n    Feedback:FeedbackReducer,\r\n    Reservation:ReservationReducer,\r\n    ReportFeedback:ReportFeedbackReducer,\r\n    ChatBox: chatboxReducer,\r\n    Socket: SocketReducer,\r\n    Message: messageReducer,\r\n    Promotion: PromotionReducer,\r\n    promotion: promotionSliceReducer, // New promotion slice\r\n});\r\n\r\nexport default rootReducer;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,kBAAkB,MAAM,wBAAwB;AACvD,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,qBAAqB,MAAM,4BAA4B;AAE9D,MAAMC,WAAW,GAAGb,eAAe,CAAC;EAChCc,IAAI,EAAEb,WAAW;EACjBc,MAAM,EAAEb,aAAa;EACrBc,KAAK,EAAEb,YAAY;EACnBc,IAAI,EAAEb,WAAW;EACjBc,QAAQ,EAACb,eAAe;EACxBc,WAAW,EAACb,kBAAkB;EAC9Bc,cAAc,EAACb,qBAAqB;EACpCc,OAAO,EAAEb,cAAc;EACvBc,MAAM,EAAEb,aAAa;EACrBc,OAAO,EAAEb,cAAc;EACvBc,SAAS,EAAEb,gBAAgB;EAC3Bc,SAAS,EAAEb,qBAAqB,CAAE;AACtC,CAAC,CAAC;AAEF,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}