/* Promotion Modal Styles */
.promotion-card {
  transition: all 0.3s ease;
  border-radius: 12px !important;
}

.promotion-card:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.3) !important;
}

.promotion-card.current {
  border-width: 2px !important;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
}

.promotion-card.disabled {
  filter: grayscale(50%);
  cursor: not-allowed !important;
}

.promotion-applied {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2) !important;
}

/* Custom scrollbar for modal */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.5);
}

/* Animation for promotion cards */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.promotion-card {
  animation: slideInUp 0.4s ease-out;
}

/* Badge styles */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
}

/* Button hover effects */
.btn-outline-light:hover {
  background-color: rgba(255,255,255,0.1) !important;
  border-color: rgba(255,255,255,0.5) !important;
}

.btn-outline-danger:hover {
  background-color: rgba(220, 53, 69, 0.2) !important;
}

/* Manual code input section */
.manual-code-section {
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.manual-code-input {
  background: rgba(255,255,255,0.1) !important;
  border: 1px solid rgba(255,255,255,0.3) !important;
  color: white !important;
  transition: all 0.3s ease;
}

.manual-code-input:focus {
  background: rgba(255,255,255,0.15) !important;
  border-color: rgba(255,255,255,0.5) !important;
  box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.1) !important;
  color: white !important;
}

.manual-code-input::placeholder {
  color: rgba(255,255,255,0.6) !important;
}

/* Usage badge styles */
.usage-badge {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
}

.usage-info {
  font-size: 0.75rem;
  color: rgba(255,255,255,0.7);
}

/* Promotion section divider */
.promotion-divider {
  border-color: rgba(255,255,255,0.3) !important;
  margin: 1.5rem 0;
}

/* Enhanced promotion card hover */
.promotion-card:hover:not(.disabled) {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.4) !important;
  border-color: rgba(255,255,255,0.5) !important;
}

/* Usage progress bar */
.usage-progress {
  background: rgba(255,255,255,0.2);
  border-radius: 2px;
  overflow: hidden;
  height: 4px;
}

.usage-progress-bar {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.usage-progress-bar.low {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.usage-progress-bar.medium {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.usage-progress-bar.high {
  background: linear-gradient(90deg, #dc3545, #e74c3c);
}

/* Usage info text */
.usage-info-text {
  font-size: 0.7rem;
  color: rgba(255,255,255,0.6);
  font-weight: 500;
}

/* Personalized promotion indicator */
.personalized-indicator {
  background: linear-gradient(45deg, #6f42c1, #e83e8c);
  color: white;
  font-size: 0.65rem;
  padding: 0.15rem 0.4rem;
  border-radius: 3px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Public promotion indicator */
.public-indicator {
  background: linear-gradient(45deg, #17a2b8, #20c997);
  color: white;
  font-size: 0.65rem;
  padding: 0.15rem 0.4rem;
  border-radius: 3px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
