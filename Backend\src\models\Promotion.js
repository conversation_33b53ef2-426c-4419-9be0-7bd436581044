const mongoose = require("mongoose");

const promotionSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    discountType: {
      type: String,
      enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
      required: true,
    },
    discountValue: {
      type: Number,
      required: true,
      min: 0,
    },
    maxDiscountAmount: {
      type: Number,
      min: 0,
    },
    minOrderAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    usageLimit: {
      type: Number,
      default: null,
    },
    usedCount: {
      type: Number,
      default: 0,
    },
    userUsageLimit: {
      type: Number,
      default: 1,
      min: 1,
      max: 10, // Giới hạn tối đa 10 lần/user để tránh abuse
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPersonalized: {
      type: Boolean,
      default: false,
    },
    targetUserTypes: [{
      type: String,
      enum: ['NEW_USER', 'RETURNING_USER', 'VIP_USER', 'ALL'],
      default: 'ALL'
    }],
    createdBy: {
      type: Number,
      ref: 'User',
      required: true,
    },
  },
  {
    versionKey: '__v', // Enable versioning for optimistic locking
    timestamps: true
  }
);
promotionSchema.pre("save", function (next) {
  if (this.startDate >= this.endDate) {
    return next(new Error("Start date must be before end date."));
  }
  next();
});
promotionSchema.methods.isValid = function () {
  const now = new Date();
  return this.isActive &&
    now >= this.startDate &&
    now <= this.endDate &&
    (this.usageLimit === null || this.usedCount < this.usageLimit);
};

// Method để kiểm tra user có thể sử dụng promotion này không
promotionSchema.methods.canUserUse = async function (userId) {
  // Import PromotionUser model
  const PromotionUser = require('./PromotionUser');

  // Đếm số lần user đã sử dụng promotion này
  const userUsageCount = await PromotionUser.getUserUsageCount(userId, this._id);

  if (userUsageCount >= this.userUsageLimit) {
    return {
      canUse: false,
      remainingUses: 0,
      reason: `You have reached the usage limit (${this.userUsageLimit} times) for this promotion`
    };
  }

  return { canUse: true, remainingUses: this.userUsageLimit - userUsageCount };
};

// Static method để apply promotion với optimistic locking
promotionSchema.statics.applyPromotionWithLock = async function(promotionId, userId, orderAmount, maxRetries = 3) {
  const PromotionReservation = require('./PromotionReservation');

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Get promotion with current version
      const promotion = await this.findById(promotionId);
      if (!promotion) {
        throw new Error('Promotion not found');
      }

      // Validate promotion
      if (!promotion.isValid()) {
        throw new Error('Promotion is not valid');
      }

      if (orderAmount < promotion.minOrderAmount) {
        throw new Error(`Minimum order amount is ${promotion.minOrderAmount}`);
      }

      // Check user eligibility
      const userEligibility = await promotion.canUserUse(userId);
      if (!userEligibility.canUse) {
        throw new Error(userEligibility.reason);
      }

      // Check if user already has active reservation for this promotion
      const hasActiveReservation = await PromotionReservation.hasActiveReservation(promotionId, userId);
      if (hasActiveReservation) {
        throw new Error('You already have an active reservation for this promotion');
      }

      // Calculate current usage including reservations
      const reservedCount = await PromotionReservation.countDocuments({
        promotionId,
        status: 'RESERVED',
        expiresAt: { $gt: new Date() }
      });

      const totalUsage = promotion.usedCount + reservedCount;

      if (promotion.usageLimit && totalUsage >= promotion.usageLimit) {
        throw new Error('Promotion usage limit exceeded');
      }

      // Calculate discount
      let discount = 0;
      if (promotion.discountType === 'PERCENTAGE') {
        discount = (orderAmount * promotion.discountValue) / 100;
        if (promotion.maxDiscountAmount) {
          discount = Math.min(discount, promotion.maxDiscountAmount);
        }
      } else if (promotion.discountType === 'FIXED_AMOUNT') {
        discount = promotion.discountValue;
      }

      // Create reservation
      const reservation = await PromotionReservation.reservePromotion({
        promotionId,
        userId,
        orderAmount,
        discountAmount: discount,
        metadata: {
          appliedDiscountType: promotion.discountType,
          appliedDiscountValue: promotion.discountValue
        }
      });

      return {
        valid: true,
        discount,
        promotionId,
        reservationId: reservation._id,
        expiresAt: reservation.expiresAt,
        userUsageLimit: promotion.userUsageLimit,
        remainingUses: userEligibility.remainingUses
      };

    } catch (error) {
      if (error.name === 'VersionError' && attempt < maxRetries - 1) {
        // Retry on version conflict
        await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
        continue;
      }
      throw error;
    }
  }

  throw new Error('Failed to apply promotion after multiple attempts');
};

// Static method để confirm promotion usage khi booking thành công
promotionSchema.statics.confirmPromotionUsage = async function(promotionId, userId, reservationId) {
  const session = await mongoose.startSession();

  try {
    return await session.withTransaction(async () => {
      const PromotionReservation = require('./PromotionReservation');
      const PromotionUser = require('./PromotionUser');

      // Confirm reservation
      const confirmedReservation = await PromotionReservation.confirmReservation(
        promotionId, userId, reservationId
      );

      if (!confirmedReservation) {
        throw new Error('Promotion reservation not found or expired');
      }

      // Increment usage count with optimistic locking
      const promotion = await this.findById(promotionId).session(session);
      const updated = await this.findOneAndUpdate(
        { _id: promotionId, __v: promotion.__v },
        {
          $inc: { usedCount: 1, __v: 1 }
        },
        { new: true, session }
      );

      if (!updated) {
        throw new Error('Failed to update promotion usage count');
      }

      // Create PromotionUser record
      await PromotionUser.create([{
        userId,
        promotionId,
        reservationId,
        discountAmount: confirmedReservation.discountAmount,
        orderAmount: confirmedReservation.orderAmount,
        appliedDiscountType: confirmedReservation.metadata.appliedDiscountType,
        appliedDiscountValue: confirmedReservation.metadata.appliedDiscountValue,
        status: 'USED'
      }], { session });

      return updated;
    });
  } finally {
    await session.endSession();
  }
};

// Static method để rollback promotion usage khi booking fail
promotionSchema.statics.rollbackPromotionUsage = async function(promotionId, userId, reservationId) {
  const session = await mongoose.startSession();

  try {
    return await session.withTransaction(async () => {
      const PromotionReservation = require('./PromotionReservation');
      const PromotionUser = require('./PromotionUser');

      // Cancel reservation if exists
      await PromotionReservation.cancelReservation(promotionId, userId);

      // If there's a confirmed usage, rollback
      const promotionUser = await PromotionUser.findOne({
        promotionId,
        userId,
        reservationId,
        status: 'USED'
      }).session(session);

      if (promotionUser) {
        // Decrement usage count
        const promotion = await this.findById(promotionId).session(session);
        await this.findOneAndUpdate(
          { _id: promotionId, __v: promotion.__v },
          {
            $inc: { usedCount: -1, __v: 1 }
          },
          { session }
        );

        // Mark as cancelled
        await PromotionUser.findByIdAndUpdate(
          promotionUser._id,
          { status: 'CANCELLED' },
          { session }
        );
      }

      return true;
    });
  } finally {
    await session.endSession();
  }
};

module.exports = mongoose.model("Promotion", promotionSchema);