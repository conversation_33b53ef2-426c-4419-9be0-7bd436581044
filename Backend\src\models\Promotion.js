const mongoose = require("mongoose");

const promotionSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    discountType: {
      type: String,
      enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
      required: true,
    },
    discountValue: {
      type: Number,
      required: true,
      min: 0,
    },
    maxDiscountAmount: {
      type: Number,
      min: 0,
    },
    minOrderAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    usageLimit: {
      type: Number,
      default: null, // null = unlimited
    },
    usedCount: {
      type: Number,
      default: 0,
    },
    userUsageLimit: {
      type: Number,
      default: 1,
      min: 1,
      max: 10,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    // Simplified: isPublic thay vì isPersonalized
    isPublic: {
      type: Boolean,
      default: true, // true = public, false = private
    },
    // For private promotions: specify which user owns this promotion
    userId: {
      type: Number,
      ref: 'User',
      default: null, // null for public promotions
    },
    createdBy: {
      type: Number,
      ref: 'User',
      required: true,
    },
  },
  {
    versionKey: '__v',
    timestamps: true
  }
);
// Validation
promotionSchema.pre("save", function (next) {
  if (this.startDate >= this.endDate) {
    return next(new Error("Start date must be before end date."));
  }

  // Validation: private promotions must have userId
  if (!this.isPublic && !this.userId) {
    return next(new Error("Private promotions must specify userId"));
  }

  // Validation: public promotions should not have userId
  if (this.isPublic && this.userId) {
    return next(new Error("Public promotions should not specify userId"));
  }

  next();
});

// Check if promotion is valid (time, active, usage)
promotionSchema.methods.isValid = function () {
  const now = new Date();
  return this.isActive &&
    now >= this.startDate &&
    now <= this.endDate &&
    (this.usageLimit === null || this.usedCount < this.usageLimit);
};

// Check if user can use this promotion
promotionSchema.methods.canUserUse = async function (userId) {
  // For private promotions, check ownership
  if (!this.isPublic && this.userId !== null && this.userId !== userId) {
    return {
      canUse: false,
      remainingUses: 0,
      reason: "This is a private promotion not assigned to you"
    };
  }

  // For unassigned private promotions (userId = null), they are claimable
  if (!this.isPublic && this.userId === null) {
    return {
      canUse: true,
      remainingUses: this.userUsageLimit,
      reason: "This promotion can be claimed"
    };
  }

  // Check user usage count
  const PromotionUser = require('./PromotionUser');
  const userUsageCount = await PromotionUser.getUserUsageCount(userId, this._id);

  if (userUsageCount >= this.userUsageLimit) {
    return {
      canUse: false,
      remainingUses: 0,
      reason: `You have reached the usage limit (${this.userUsageLimit} times) for this promotion`
    };
  }

  return {
    canUse: true,
    remainingUses: this.userUsageLimit - userUsageCount
  };
};

// Simplified apply promotion method (no reservation system)
promotionSchema.statics.applyPromotionWithLock = async function(promotionId, userId, orderAmount) {
  try {
    // Get promotion
    const promotion = await this.findById(promotionId);
    if (!promotion) {
      throw new Error('Promotion not found');
    }

    // Basic validations
    if (!promotion.isValid()) {
      throw new Error('Promotion is not valid');
    }

    if (orderAmount < promotion.minOrderAmount) {
      throw new Error(`Minimum order amount is ${promotion.minOrderAmount}`);
    }

    // Check user eligibility (simplified)
    const userEligibility = await promotion.canUserUse(userId);
    if (!userEligibility.canUse) {
      throw new Error(userEligibility.reason);
    }

    // Calculate discount
    let discount = 0;
    if (promotion.discountType === 'PERCENTAGE') {
      discount = (orderAmount * promotion.discountValue) / 100;
      if (promotion.maxDiscountAmount) {
        discount = Math.min(discount, promotion.maxDiscountAmount);
      }
    } else if (promotion.discountType === 'FIXED_AMOUNT') {
      discount = promotion.discountValue;
    }

    // Return result (no reservation system)
    return {
      valid: true,
      discount,
      promotionId,
      userUsageLimit: promotion.userUsageLimit,
      remainingUses: userEligibility.remainingUses
    };

  } catch (error) {
    throw error;
  }
};

// Static method để confirm promotion usage khi booking thành công
promotionSchema.statics.confirmPromotionUsage = async function(promotionId, userId, orderAmount, discountAmount) {
  try {
    const PromotionUser = require('./PromotionUser');

    // Get promotion details
    const promotion = await this.findById(promotionId);
    if (!promotion) {
      throw new Error('Promotion not found');
    }

    // Update promotion usage count
    const updated = await this.findByIdAndUpdate(
      promotionId,
      { $inc: { usedCount: 1 } },
      { new: true }
    );

    if (!updated) {
      throw new Error('Failed to update promotion usage count');
    }

    // Create PromotionUser record
    await PromotionUser.create({
      userId,
      promotionId,
      reservationId: null, // No reservation system
      discountAmount,
      orderAmount,
      appliedDiscountType: promotion.discountType,
      appliedDiscountValue: promotion.discountValue,
      status: 'USED'
    });

    console.log(`✅ Promotion usage confirmed: ${promotion.code} for user ${userId}`);
    return updated;
  } catch (error) {
    console.error('❌ Error confirming promotion usage:', error);
    throw error;
  }
};

// Static method để rollback promotion usage khi booking fail
promotionSchema.statics.rollbackPromotionUsage = async function(promotionId, userId) {
  try {
    const PromotionUser = require('./PromotionUser');

    // Find and rollback confirmed usage
    const promotionUser = await PromotionUser.findOne({
      promotionId,
      userId,
      status: 'USED'
    });

    if (promotionUser) {
      // Decrement usage count
      await this.findByIdAndUpdate(
        promotionId,
        { $inc: { usedCount: -1 } }
      );

      // Mark as cancelled
      await PromotionUser.findByIdAndUpdate(
        promotionUser._id,
        { status: 'CANCELLED' }
      );

      console.log(`✅ Promotion usage rolled back: ${promotionId} for user ${userId}`);
    }

    return true;
  } catch (error) {
    console.error('❌ Error rolling back promotion usage:', error);
    throw error;
  }
};

module.exports = mongoose.model("Promotion", promotionSchema);