const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const promotionSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    discountType: {
      type: String,
      enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
      required: true,
    },
    discountValue: {
      type: Number,
      required: true,
      min: 0,
    },
    maxDiscountAmount: {
      type: Number,
      min: 0,
    },
    minOrderAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    usageLimit: {
      type: Number,
      default: null,
    },
    usedCount: {
      type: Number,
      default: 0,
    },
    userUsageLimit: {
      type: Number,
      default: 1,
      min: 1,
      max: 10, // <PERSON><PERSON><PERSON><PERSON> hạn tối đa 10 lần/user để tránh abuse
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPersonalized: {
      type: Boolean,
      default: false,
    },
    targetUserTypes: [{
      type: String,
      enum: ['NEW_USER', 'RETURNING_USER', 'VIP_USER', 'ALL'],
      default: 'ALL'
    }],
    createdBy: {
      type: Number,
      ref: 'User',
      required: true,
    },
  },
  { 
    versionKey: false,
    timestamps: true 
  }
);
promotionSchema.pre("save", function (next) {
  if (this.startDate >= this.endDate) {
    return next(new Error("Start date must be before end date."));
  }
  next();
});
promotionSchema.methods.isValid = function () {
  const now = new Date();
  return this.isActive &&
    now >= this.startDate &&
    now <= this.endDate &&
    (this.usageLimit === null || this.usedCount < this.usageLimit);
};

// Method để kiểm tra user có thể sử dụng promotion này không
promotionSchema.methods.canUserUse = async function (userId) {
  // Import PromotionUser model
  const PromotionUser = require('./PromotionUser');

  // Đếm số lần user đã sử dụng promotion này
  const userUsageCount = await PromotionUser.getUserUsageCount(userId, this._id);

  if (userUsageCount >= this.userUsageLimit) {
    return {
      canUse: false,
      remainingUses: 0,
      reason: `You have reached the usage limit (${this.userUsageLimit} times) for this promotion`
    };
  }

  return { canUse: true, remainingUses: this.userUsageLimit - userUsageCount };
};
module.exports = mongoose.model("Promotion", promotionSchema);