const mongoose = require('mongoose');

const promotionUserSchema = new mongoose.Schema(
  {
    userId: {
      type: Number,
      ref: 'User',
      required: true,
    },
    promotionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Promotion',
      required: true,
    },
    reservationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reservation',
      required: false, // Not required for direct promotion usage
      default: null,
    },
    usedAt: {
      type: Date,
      default: Date.now,
    },
    discountAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    orderAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    status: {
      type: String,
      enum: ['USED', 'CANCELLED'],
      default: 'USED',
    },
    // Thông tin bổ sung để audit
    appliedDiscountType: {
      type: String,
      enum: ['PERCENTAGE', 'FIXED_AMOUNT'],
      required: true,
    },
    appliedDiscountValue: {
      type: Number,
      required: true,
    },
  },
  { 
    versionKey: false,
    timestamps: true 
  }
);

// Compound indexes để tối ưu query
promotionUserSchema.index({ userId: 1, promotionId: 1 });
promotionUserSchema.index({ promotionId: 1, usedAt: -1 });
promotionUserSchema.index({ reservationId: 1 });
promotionUserSchema.index({ status: 1, usedAt: -1 });

// Static method để đếm số lần user đã sử dụng promotion
promotionUserSchema.statics.getUserUsageCount = async function(userId, promotionId) {
  return await this.countDocuments({
    userId: userId,
    promotionId: promotionId,
    status: 'USED'
  });
};

// Static method để lấy lịch sử sử dụng promotion của user
promotionUserSchema.statics.getUserPromotionHistory = async function(userId, options = {}) {
  const { page = 1, limit = 10, status } = options;
  const skip = (page - 1) * limit;
  
  let filter = { userId: userId };
  if (status) {
    filter.status = status;
  }
  
  return await this.find(filter)
    .populate('promotionId', 'code name discountType discountValue')
    .populate('reservationId', 'totalPrice finalPrice status')
    .sort({ usedAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method để lấy thống kê sử dụng promotion
promotionUserSchema.statics.getPromotionStats = async function(promotionId) {
  const stats = await this.aggregate([
    { $match: { promotionId: new mongoose.Types.ObjectId(promotionId), status: 'USED' } },
    {
      $group: {
        _id: null,
        totalUsage: { $sum: 1 },
        totalDiscountAmount: { $sum: '$discountAmount' },
        totalOrderAmount: { $sum: '$orderAmount' },
        uniqueUsers: { $addToSet: '$userId' },
        avgDiscountAmount: { $avg: '$discountAmount' },
        avgOrderAmount: { $avg: '$orderAmount' }
      }
    },
    {
      $project: {
        _id: 0,
        totalUsage: 1,
        totalDiscountAmount: 1,
        totalOrderAmount: 1,
        uniqueUserCount: { $size: '$uniqueUsers' },
        avgDiscountAmount: { $round: ['$avgDiscountAmount', 2] },
        avgOrderAmount: { $round: ['$avgOrderAmount', 2] }
      }
    }
  ]);
  
  return stats[0] || {
    totalUsage: 0,
    totalDiscountAmount: 0,
    totalOrderAmount: 0,
    uniqueUserCount: 0,
    avgDiscountAmount: 0,
    avgOrderAmount: 0
  };
};

module.exports = mongoose.model('PromotionUser', promotionUserSchema);
