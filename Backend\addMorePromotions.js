const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const UserPromotion = require("./src/models/UserPromotion");
const User = require("./src/models/user");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function addMorePromotions() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB");

    // Get admin user
    const adminUser = await User.findOne({ _id: 1 });
    if (!adminUser) {
      console.error("Admin user not found");
      process.exit(1);
    }

    // Additional interesting promotions
    const newPromotions = [
      {
        code: "FLASH50",
        name: "Flash Sale",
        description: "Limited time 50% off - Only 5 uses available!",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 1000000, // 1M VND max
        minOrderAmount: 800000, // 800k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-02-28"),
        usageLimit: 5, // Very limited
        usedCount: 2, // 3 left
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "FLASH_2025",
          urgency: "HIGH"
        }
      },
      {
        code: "STUDENT20",
        name: "Student Discount",
        description: "20% off for students with valid ID",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 200000, // 200k VND max
        minOrderAmount: 300000, // 300k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 156,
        userUsageLimit: 3,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "STUDENT_2025",
          requiresVerification: true
        }
      },
      {
        code: "FAMILY30",
        name: "Family Package",
        description: "30% off for family bookings (3+ rooms)",
        discountType: "PERCENTAGE",
        discountValue: 30,
        maxDiscountAmount: 800000, // 800k VND max
        minOrderAmount: 2000000, // 2M VND min (family booking)
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 200,
        usedCount: 34,
        userUsageLimit: 2,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "FAMILY_2025",
          minRooms: 3
        }
      },
      {
        code: "EARLYBIRD",
        name: "Early Bird Special",
        description: "Book 30 days in advance and save 25%",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 500000, // 500k VND max
        minOrderAmount: 700000, // 700k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-11-30"),
        usageLimit: 300,
        usedCount: 78,
        userUsageLimit: 5,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "EARLYBIRD_2025",
          advanceBookingDays: 30
        }
      },
      {
        code: "LASTMINUTE",
        name: "Last Minute Deal",
        description: "Book within 24 hours and get 15% off",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 300000, // 300k VND max
        minOrderAmount: 400000, // 400k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 500,
        usedCount: 234,
        userUsageLimit: 10,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "LASTMINUTE_2025",
          bookingWindow: 24
        }
      },
      {
        code: "COMEBACK",
        name: "Welcome Back",
        description: "We miss you! 40% off your return booking",
        discountType: "PERCENTAGE",
        discountValue: 40,
        maxDiscountAmount: 600000, // 600k VND max
        minOrderAmount: 800000, // 800k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 12,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // Personalized for returning customers
        targetUserTypes: ["RETURNING_USER"],
        createdBy: adminUser._id,
        metadata: {
          autoTrigger: "returning_customer",
          inactivityDays: 90
        }
      },
      {
        code: "REFER50K",
        name: "Referral Bonus",
        description: "Refer a friend and both get 50,000 VND off",
        discountType: "FIXED_AMOUNT",
        discountValue: 50000,
        minOrderAmount: 500000, // 500k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 89,
        userUsageLimit: 5,
        isActive: true,
        isPersonalized: true, // Given through referral system
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          autoTrigger: "referral",
          referralProgram: true
        }
      },
      {
        code: "NEWYEAR2025",
        name: "New Year Celebration",
        description: "Ring in 2025 with 35% off your stay",
        discountType: "PERCENTAGE",
        discountValue: 35,
        maxDiscountAmount: 700000, // 700k VND max
        minOrderAmount: 1000000, // 1M VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-01-31"), // January only
        usageLimit: 150,
        usedCount: 67,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "NEWYEAR_2025",
          seasonal: true
        }
      },
      {
        code: "ALMOSTFULL",
        name: "Almost Used Up",
        description: "Hurry! Only 1 use left",
        discountType: "FIXED_AMOUNT",
        discountValue: 75000,
        minOrderAmount: 600000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        usedCount: 9, // Only 1 left!
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "SCARCITY_TEST",
          urgency: "VERY_HIGH"
        }
      },
      {
        code: "INACTIVE",
        name: "Inactive Promotion",
        description: "This promotion is currently inactive",
        discountType: "PERCENTAGE",
        discountValue: 20,
        minOrderAmount: 500000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 5,
        userUsageLimit: 2,
        isActive: false, // Inactive
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "INACTIVE_TEST",
          reason: "PAUSED_BY_ADMIN"
        }
      }
    ];

    // Insert new promotions
    const createdPromotions = await Promotion.insertMany(newPromotions);
    console.log(`Created ${createdPromotions.length} additional promotions`);

    // Create some specific user assignments for testing
    const users = await User.find({ _id: { $in: [1, 2, 3, 4, 5] } });
    
    if (users.length > 0) {
      const userPromotions = [];
      
      // Assign COMEBACK promotion to user 3 (returning customer)
      const comebackPromo = createdPromotions.find(p => p.code === "COMEBACK");
      if (comebackPromo) {
        userPromotions.push({
          userId: 3,
          promotionId: comebackPromo._id,
          assignmentType: 'EARNED',
          maxUsage: 1,
          usageCount: 0,
          status: 'ASSIGNED',
          metadata: {
            assignmentReason: 'RETENTION_OFFER',
            triggerEvent: 'RETURNING_CUSTOMER',
            lastBookingDate: new Date('2024-10-15')
          }
        });
      }

      // Assign REFER50K to user 4 and 5 (referral program)
      const referralPromo = createdPromotions.find(p => p.code === "REFER50K");
      if (referralPromo) {
        [4, 5].forEach(userId => {
          userPromotions.push({
            userId,
            promotionId: referralPromo._id,
            assignmentType: 'EARNED',
            maxUsage: 1,
            usageCount: 0,
            status: 'ASSIGNED',
            metadata: {
              assignmentReason: 'REFERRAL_BONUS',
              triggerEvent: 'FRIEND_REFERRAL',
              referredBy: userId === 4 ? 5 : 4
            }
          });
        });
      }

      if (userPromotions.length > 0) {
        await UserPromotion.insertMany(userPromotions);
        console.log(`Created ${userPromotions.length} additional user promotion assignments`);
      }
    }

    console.log("\n🎉 Additional promotions created successfully!");
    console.log("\n📋 New Promotions Added:");
    createdPromotions.forEach(promo => {
      const urgency = promo.usageLimit - promo.usedCount <= 5 ? " 🔥 URGENT" : "";
      const status = promo.isActive ? "🟢" : "🔴";
      console.log(`${status} ${promo.code}: ${promo.name}${urgency}`);
      console.log(`   ${promo.discountType === 'PERCENTAGE' ? promo.discountValue + '%' : promo.discountValue + ' VND'} off (${promo.usedCount}/${promo.usageLimit} used)`);
    });

    console.log("\n🧪 New Test Scenarios:");
    console.log("- FLASH50: Limited quantity (only 3 left!)");
    console.log("- STUDENT20: Category-specific discount");
    console.log("- FAMILY30: High minimum order amount");
    console.log("- EARLYBIRD: Advance booking promotion");
    console.log("- LASTMINUTE: Last-minute booking deals");
    console.log("- COMEBACK: Personalized returning customer offer");
    console.log("- REFER50K: Referral program bonus");
    console.log("- NEWYEAR2025: Time-limited seasonal promotion");
    console.log("- ALMOSTFULL: Scarcity marketing (1 use left)");
    console.log("- INACTIVE: Inactive promotion (should not appear)");

    console.log("\n🎯 Recommended Testing:");
    console.log("1. Test scarcity with FLASH50 and ALMOSTFULL");
    console.log("2. Test high minimum orders with FAMILY30");
    console.log("3. Test personalized assignments (COMEBACK for User 3)");
    console.log("4. Test referral system (REFER50K for Users 4 & 5)");
    console.log("5. Test inactive promotions (INACTIVE should not show)");
    console.log("6. Test seasonal promotions (NEWYEAR2025)");

    process.exit(0);
  } catch (error) {
    console.error("Error adding promotions:", error);
    process.exit(1);
  }
}

addMorePromotions();
