{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useCallback, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { debounce } from 'lodash';\nimport { validatePromotion, clearPromotion, setError, updateLastValidatedAmount, checkExpiration, selectAppliedPromotion, selectLastValidatedAmount, selectPromotionValidating, selectIsPromotionExpired } from '../redux/promotion/promotionSlice';\n\n// Configuration constants\nconst VALIDATION_CONFIG = {\n  DEBOUNCE_DELAY: 500,\n  // ms\n  SIGNIFICANT_CHANGE_THRESHOLD: 0.05,\n  // 5%\n  MIN_AMOUNT_CHANGE: 10000,\n  // 10k VND minimum change to trigger validation\n  VALIDATION_COOLDOWN: 2000,\n  // 2 seconds cooldown between validations\n  EXPIRATION_CHECK_INTERVAL: 30000 // 30 seconds\n};\nexport const usePromotionValidation = () => {\n  _s();\n  const dispatch = useDispatch();\n  const appliedPromotion = useSelector(selectAppliedPromotion);\n  const lastValidatedAmount = useSelector(selectLastValidatedAmount);\n  const isValidating = useSelector(selectPromotionValidating);\n  const isExpired = useSelector(selectIsPromotionExpired);\n  const lastValidationTime = useRef(0);\n  const validationTimeoutRef = useRef(null);\n  const expirationCheckRef = useRef(null);\n\n  // Check if validation is needed based on amount change\n  const shouldValidate = useCallback(newAmount => {\n    if (!appliedPromotion.code) return false;\n    if (isValidating) return false;\n    const now = Date.now();\n    if (now - lastValidationTime.current < VALIDATION_CONFIG.VALIDATION_COOLDOWN) {\n      return false;\n    }\n    const amountDiff = Math.abs(newAmount - lastValidatedAmount);\n    const percentChange = lastValidatedAmount > 0 ? amountDiff / lastValidatedAmount : 1;\n    return amountDiff >= VALIDATION_CONFIG.MIN_AMOUNT_CHANGE || percentChange >= VALIDATION_CONFIG.SIGNIFICANT_CHANGE_THRESHOLD;\n  }, [appliedPromotion.code, lastValidatedAmount, isValidating]);\n\n  // Debounced validation function\n  const debouncedValidate = useCallback(debounce(async amount => {\n    if (!appliedPromotion.code || !shouldValidate(amount)) return;\n    try {\n      lastValidationTime.current = Date.now();\n      const result = await dispatch(validatePromotion({\n        code: appliedPromotion.code,\n        orderAmount: amount\n      })).unwrap();\n      if (result.valid) {\n        dispatch(updateLastValidatedAmount(amount));\n      }\n    } catch (error) {\n      console.error('Promotion validation failed:', error);\n      // Error is already handled in the slice\n    }\n  }, VALIDATION_CONFIG.DEBOUNCE_DELAY), [appliedPromotion.code, shouldValidate, dispatch]);\n\n  // Main validation function\n  const validatePromotionIfNeeded = useCallback(orderAmount => {\n    // Clear any pending validation\n    if (validationTimeoutRef.current) {\n      clearTimeout(validationTimeoutRef.current);\n    }\n\n    // Check expiration first\n    dispatch(checkExpiration());\n    if (isExpired) {\n      return;\n    }\n\n    // Schedule validation\n    validationTimeoutRef.current = setTimeout(() => {\n      debouncedValidate(orderAmount);\n    }, 0);\n  }, [debouncedValidate, dispatch, isExpired]);\n\n  // Force validation (for critical moments like before booking)\n  const forceValidation = useCallback(async orderAmount => {\n    if (!appliedPromotion.code) {\n      return {\n        valid: true\n      };\n    }\n    try {\n      const result = await dispatch(validatePromotion({\n        code: appliedPromotion.code,\n        orderAmount\n      })).unwrap();\n      if (result.valid) {\n        dispatch(updateLastValidatedAmount(orderAmount));\n      }\n      return result;\n    } catch (error) {\n      return {\n        valid: false,\n        message: error || 'Validation failed'\n      };\n    }\n  }, [appliedPromotion.code, dispatch]);\n\n  // Clear promotion with cleanup\n  const clearPromotionWithCleanup = useCallback(() => {\n    // Clear any pending validations\n    if (validationTimeoutRef.current) {\n      clearTimeout(validationTimeoutRef.current);\n    }\n    debouncedValidate.cancel();\n    dispatch(clearPromotion());\n  }, [dispatch, debouncedValidate]);\n\n  // Set up expiration checking\n  useEffect(() => {\n    if (appliedPromotion.code && appliedPromotion.expiresAt) {\n      expirationCheckRef.current = setInterval(() => {\n        dispatch(checkExpiration());\n      }, VALIDATION_CONFIG.EXPIRATION_CHECK_INTERVAL);\n      return () => {\n        if (expirationCheckRef.current) {\n          clearInterval(expirationCheckRef.current);\n        }\n      };\n    }\n  }, [appliedPromotion.code, appliedPromotion.expiresAt, dispatch]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (validationTimeoutRef.current) {\n        clearTimeout(validationTimeoutRef.current);\n      }\n      if (expirationCheckRef.current) {\n        clearInterval(expirationCheckRef.current);\n      }\n      debouncedValidate.cancel();\n    };\n  }, [debouncedValidate]);\n\n  // Calculate time until expiration\n  const getTimeUntilExpiration = useCallback(() => {\n    if (!appliedPromotion.expiresAt) return null;\n    const expirationTime = new Date(appliedPromotion.expiresAt).getTime();\n    const now = Date.now();\n    const timeLeft = expirationTime - now;\n    if (timeLeft <= 0) return 0;\n    return Math.floor(timeLeft / 1000); // seconds\n  }, [appliedPromotion.expiresAt]);\n\n  // Format time remaining for display\n  const formatTimeRemaining = useCallback(() => {\n    const seconds = getTimeUntilExpiration();\n    if (seconds === null) return null;\n    if (seconds <= 0) return 'Expired';\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    if (minutes > 0) {\n      return `${minutes}m ${remainingSeconds}s`;\n    }\n    return `${remainingSeconds}s`;\n  }, [getTimeUntilExpiration]);\n\n  // Check if promotion is about to expire (less than 2 minutes)\n  const isAboutToExpire = useCallback(() => {\n    const seconds = getTimeUntilExpiration();\n    return seconds !== null && seconds > 0 && seconds < 120;\n  }, [getTimeUntilExpiration]);\n  return {\n    // Validation functions\n    validatePromotionIfNeeded,\n    forceValidation,\n    clearPromotionWithCleanup,\n    // State\n    isValidating,\n    isExpired,\n    isAboutToExpire: isAboutToExpire(),\n    // Time utilities\n    getTimeUntilExpiration,\n    formatTimeRemaining,\n    // Configuration\n    config: VALIDATION_CONFIG\n  };\n};\n_s(usePromotionValidation, \"8f/Yqc/Xh1PRgQ6QJV+QMKiTsrA=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector, useSelector];\n});\nexport default usePromotionValidation;", "map": {"version": 3, "names": ["useCallback", "useRef", "useEffect", "useDispatch", "useSelector", "debounce", "validatePromotion", "clearPromotion", "setError", "updateLastValidatedAmount", "checkExpiration", "selectAppliedPromotion", "selectLastValidatedAmount", "selectPromotionValidating", "selectIsPromotionExpired", "VALIDATION_CONFIG", "DEBOUNCE_DELAY", "SIGNIFICANT_CHANGE_THRESHOLD", "MIN_AMOUNT_CHANGE", "VALIDATION_COOLDOWN", "EXPIRATION_CHECK_INTERVAL", "usePromotionValidation", "_s", "dispatch", "appliedPromotion", "lastValidatedAmount", "isValidating", "isExpired", "lastValidationTime", "validationTimeoutRef", "expirationCheckRef", "shouldValidate", "newAmount", "code", "now", "Date", "current", "amountDiff", "Math", "abs", "percentChange", "debouncedV<PERSON><PERSON>", "amount", "result", "orderAmount", "unwrap", "valid", "error", "console", "validatePromotionIfNeeded", "clearTimeout", "setTimeout", "forceValidation", "message", "clearPromotionWithCleanup", "cancel", "expiresAt", "setInterval", "clearInterval", "getTimeUntilExpiration", "expirationTime", "getTime", "timeLeft", "floor", "formatTimeRemaining", "seconds", "minutes", "remainingSeconds", "isAboutToExpire", "config"], "sources": ["E:/WDP301_UROOM/Customer/src/hooks/usePromotionValidation.js"], "sourcesContent": ["import { useCallback, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { debounce } from 'lodash';\nimport {\n  validatePromotion,\n  clearPromotion,\n  setError,\n  updateLastValidatedAmount,\n  checkExpiration,\n  selectAppliedPromotion,\n  selectLastValidatedAmount,\n  selectPromotionValidating,\n  selectIsPromotionExpired,\n} from '../redux/promotion/promotionSlice';\n\n// Configuration constants\nconst VALIDATION_CONFIG = {\n  DEBOUNCE_DELAY: 500, // ms\n  SIGNIFICANT_CHANGE_THRESHOLD: 0.05, // 5%\n  MIN_AMOUNT_CHANGE: 10000, // 10k VND minimum change to trigger validation\n  VALIDATION_COOLDOWN: 2000, // 2 seconds cooldown between validations\n  EXPIRATION_CHECK_INTERVAL: 30000, // 30 seconds\n};\n\nexport const usePromotionValidation = () => {\n  const dispatch = useDispatch();\n  const appliedPromotion = useSelector(selectAppliedPromotion);\n  const lastValidatedAmount = useSelector(selectLastValidatedAmount);\n  const isValidating = useSelector(selectPromotionValidating);\n  const isExpired = useSelector(selectIsPromotionExpired);\n  \n  const lastValidationTime = useRef(0);\n  const validationTimeoutRef = useRef(null);\n  const expirationCheckRef = useRef(null);\n\n  // Check if validation is needed based on amount change\n  const shouldValidate = useCallback((newAmount) => {\n    if (!appliedPromotion.code) return false;\n    if (isValidating) return false;\n    \n    const now = Date.now();\n    if (now - lastValidationTime.current < VALIDATION_CONFIG.VALIDATION_COOLDOWN) {\n      return false;\n    }\n    \n    const amountDiff = Math.abs(newAmount - lastValidatedAmount);\n    const percentChange = lastValidatedAmount > 0 ? amountDiff / lastValidatedAmount : 1;\n    \n    return (\n      amountDiff >= VALIDATION_CONFIG.MIN_AMOUNT_CHANGE ||\n      percentChange >= VALIDATION_CONFIG.SIGNIFICANT_CHANGE_THRESHOLD\n    );\n  }, [appliedPromotion.code, lastValidatedAmount, isValidating]);\n\n  // Debounced validation function\n  const debouncedValidate = useCallback(\n    debounce(async (amount) => {\n      if (!appliedPromotion.code || !shouldValidate(amount)) return;\n      \n      try {\n        lastValidationTime.current = Date.now();\n        \n        const result = await dispatch(validatePromotion({\n          code: appliedPromotion.code,\n          orderAmount: amount,\n        })).unwrap();\n        \n        if (result.valid) {\n          dispatch(updateLastValidatedAmount(amount));\n        }\n      } catch (error) {\n        console.error('Promotion validation failed:', error);\n        // Error is already handled in the slice\n      }\n    }, VALIDATION_CONFIG.DEBOUNCE_DELAY),\n    [appliedPromotion.code, shouldValidate, dispatch]\n  );\n\n  // Main validation function\n  const validatePromotionIfNeeded = useCallback((orderAmount) => {\n    // Clear any pending validation\n    if (validationTimeoutRef.current) {\n      clearTimeout(validationTimeoutRef.current);\n    }\n    \n    // Check expiration first\n    dispatch(checkExpiration());\n    \n    if (isExpired) {\n      return;\n    }\n    \n    // Schedule validation\n    validationTimeoutRef.current = setTimeout(() => {\n      debouncedValidate(orderAmount);\n    }, 0);\n  }, [debouncedValidate, dispatch, isExpired]);\n\n  // Force validation (for critical moments like before booking)\n  const forceValidation = useCallback(async (orderAmount) => {\n    if (!appliedPromotion.code) {\n      return { valid: true };\n    }\n    \n    try {\n      const result = await dispatch(validatePromotion({\n        code: appliedPromotion.code,\n        orderAmount,\n      })).unwrap();\n      \n      if (result.valid) {\n        dispatch(updateLastValidatedAmount(orderAmount));\n      }\n      \n      return result;\n    } catch (error) {\n      return {\n        valid: false,\n        message: error || 'Validation failed',\n      };\n    }\n  }, [appliedPromotion.code, dispatch]);\n\n  // Clear promotion with cleanup\n  const clearPromotionWithCleanup = useCallback(() => {\n    // Clear any pending validations\n    if (validationTimeoutRef.current) {\n      clearTimeout(validationTimeoutRef.current);\n    }\n    debouncedValidate.cancel();\n    \n    dispatch(clearPromotion());\n  }, [dispatch, debouncedValidate]);\n\n  // Set up expiration checking\n  useEffect(() => {\n    if (appliedPromotion.code && appliedPromotion.expiresAt) {\n      expirationCheckRef.current = setInterval(() => {\n        dispatch(checkExpiration());\n      }, VALIDATION_CONFIG.EXPIRATION_CHECK_INTERVAL);\n      \n      return () => {\n        if (expirationCheckRef.current) {\n          clearInterval(expirationCheckRef.current);\n        }\n      };\n    }\n  }, [appliedPromotion.code, appliedPromotion.expiresAt, dispatch]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (validationTimeoutRef.current) {\n        clearTimeout(validationTimeoutRef.current);\n      }\n      if (expirationCheckRef.current) {\n        clearInterval(expirationCheckRef.current);\n      }\n      debouncedValidate.cancel();\n    };\n  }, [debouncedValidate]);\n\n  // Calculate time until expiration\n  const getTimeUntilExpiration = useCallback(() => {\n    if (!appliedPromotion.expiresAt) return null;\n    \n    const expirationTime = new Date(appliedPromotion.expiresAt).getTime();\n    const now = Date.now();\n    const timeLeft = expirationTime - now;\n    \n    if (timeLeft <= 0) return 0;\n    \n    return Math.floor(timeLeft / 1000); // seconds\n  }, [appliedPromotion.expiresAt]);\n\n  // Format time remaining for display\n  const formatTimeRemaining = useCallback(() => {\n    const seconds = getTimeUntilExpiration();\n    if (seconds === null) return null;\n    if (seconds <= 0) return 'Expired';\n    \n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    \n    if (minutes > 0) {\n      return `${minutes}m ${remainingSeconds}s`;\n    }\n    return `${remainingSeconds}s`;\n  }, [getTimeUntilExpiration]);\n\n  // Check if promotion is about to expire (less than 2 minutes)\n  const isAboutToExpire = useCallback(() => {\n    const seconds = getTimeUntilExpiration();\n    return seconds !== null && seconds > 0 && seconds < 120;\n  }, [getTimeUntilExpiration]);\n\n  return {\n    // Validation functions\n    validatePromotionIfNeeded,\n    forceValidation,\n    clearPromotionWithCleanup,\n    \n    // State\n    isValidating,\n    isExpired,\n    isAboutToExpire: isAboutToExpire(),\n    \n    // Time utilities\n    getTimeUntilExpiration,\n    formatTimeRemaining,\n    \n    // Configuration\n    config: VALIDATION_CONFIG,\n  };\n};\n\nexport default usePromotionValidation;\n"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SACEC,iBAAiB,EACjBC,cAAc,EACdC,QAAQ,EACRC,yBAAyB,EACzBC,eAAe,EACfC,sBAAsB,EACtBC,yBAAyB,EACzBC,yBAAyB,EACzBC,wBAAwB,QACnB,mCAAmC;;AAE1C;AACA,MAAMC,iBAAiB,GAAG;EACxBC,cAAc,EAAE,GAAG;EAAE;EACrBC,4BAA4B,EAAE,IAAI;EAAE;EACpCC,iBAAiB,EAAE,KAAK;EAAE;EAC1BC,mBAAmB,EAAE,IAAI;EAAE;EAC3BC,yBAAyB,EAAE,KAAK,CAAE;AACpC,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,gBAAgB,GAAGpB,WAAW,CAACO,sBAAsB,CAAC;EAC5D,MAAMc,mBAAmB,GAAGrB,WAAW,CAACQ,yBAAyB,CAAC;EAClE,MAAMc,YAAY,GAAGtB,WAAW,CAACS,yBAAyB,CAAC;EAC3D,MAAMc,SAAS,GAAGvB,WAAW,CAACU,wBAAwB,CAAC;EAEvD,MAAMc,kBAAkB,GAAG3B,MAAM,CAAC,CAAC,CAAC;EACpC,MAAM4B,oBAAoB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM6B,kBAAkB,GAAG7B,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM8B,cAAc,GAAG/B,WAAW,CAAEgC,SAAS,IAAK;IAChD,IAAI,CAACR,gBAAgB,CAACS,IAAI,EAAE,OAAO,KAAK;IACxC,IAAIP,YAAY,EAAE,OAAO,KAAK;IAE9B,MAAMQ,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGN,kBAAkB,CAACQ,OAAO,GAAGrB,iBAAiB,CAACI,mBAAmB,EAAE;MAC5E,OAAO,KAAK;IACd;IAEA,MAAMkB,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACP,SAAS,GAAGP,mBAAmB,CAAC;IAC5D,MAAMe,aAAa,GAAGf,mBAAmB,GAAG,CAAC,GAAGY,UAAU,GAAGZ,mBAAmB,GAAG,CAAC;IAEpF,OACEY,UAAU,IAAItB,iBAAiB,CAACG,iBAAiB,IACjDsB,aAAa,IAAIzB,iBAAiB,CAACE,4BAA4B;EAEnE,CAAC,EAAE,CAACO,gBAAgB,CAACS,IAAI,EAAER,mBAAmB,EAAEC,YAAY,CAAC,CAAC;;EAE9D;EACA,MAAMe,iBAAiB,GAAGzC,WAAW,CACnCK,QAAQ,CAAC,MAAOqC,MAAM,IAAK;IACzB,IAAI,CAAClB,gBAAgB,CAACS,IAAI,IAAI,CAACF,cAAc,CAACW,MAAM,CAAC,EAAE;IAEvD,IAAI;MACFd,kBAAkB,CAACQ,OAAO,GAAGD,IAAI,CAACD,GAAG,CAAC,CAAC;MAEvC,MAAMS,MAAM,GAAG,MAAMpB,QAAQ,CAACjB,iBAAiB,CAAC;QAC9C2B,IAAI,EAAET,gBAAgB,CAACS,IAAI;QAC3BW,WAAW,EAAEF;MACf,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC;MAEZ,IAAIF,MAAM,CAACG,KAAK,EAAE;QAChBvB,QAAQ,CAACd,yBAAyB,CAACiC,MAAM,CAAC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;IACF;EACF,CAAC,EAAEhC,iBAAiB,CAACC,cAAc,CAAC,EACpC,CAACQ,gBAAgB,CAACS,IAAI,EAAEF,cAAc,EAAER,QAAQ,CAClD,CAAC;;EAED;EACA,MAAM0B,yBAAyB,GAAGjD,WAAW,CAAE4C,WAAW,IAAK;IAC7D;IACA,IAAIf,oBAAoB,CAACO,OAAO,EAAE;MAChCc,YAAY,CAACrB,oBAAoB,CAACO,OAAO,CAAC;IAC5C;;IAEA;IACAb,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC;IAE3B,IAAIiB,SAAS,EAAE;MACb;IACF;;IAEA;IACAE,oBAAoB,CAACO,OAAO,GAAGe,UAAU,CAAC,MAAM;MAC9CV,iBAAiB,CAACG,WAAW,CAAC;IAChC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACH,iBAAiB,EAAElB,QAAQ,EAAEI,SAAS,CAAC,CAAC;;EAE5C;EACA,MAAMyB,eAAe,GAAGpD,WAAW,CAAC,MAAO4C,WAAW,IAAK;IACzD,IAAI,CAACpB,gBAAgB,CAACS,IAAI,EAAE;MAC1B,OAAO;QAAEa,KAAK,EAAE;MAAK,CAAC;IACxB;IAEA,IAAI;MACF,MAAMH,MAAM,GAAG,MAAMpB,QAAQ,CAACjB,iBAAiB,CAAC;QAC9C2B,IAAI,EAAET,gBAAgB,CAACS,IAAI;QAC3BW;MACF,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAEZ,IAAIF,MAAM,CAACG,KAAK,EAAE;QAChBvB,QAAQ,CAACd,yBAAyB,CAACmC,WAAW,CAAC,CAAC;MAClD;MAEA,OAAOD,MAAM;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO;QACLD,KAAK,EAAE,KAAK;QACZO,OAAO,EAAEN,KAAK,IAAI;MACpB,CAAC;IACH;EACF,CAAC,EAAE,CAACvB,gBAAgB,CAACS,IAAI,EAAEV,QAAQ,CAAC,CAAC;;EAErC;EACA,MAAM+B,yBAAyB,GAAGtD,WAAW,CAAC,MAAM;IAClD;IACA,IAAI6B,oBAAoB,CAACO,OAAO,EAAE;MAChCc,YAAY,CAACrB,oBAAoB,CAACO,OAAO,CAAC;IAC5C;IACAK,iBAAiB,CAACc,MAAM,CAAC,CAAC;IAE1BhC,QAAQ,CAAChB,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACgB,QAAQ,EAAEkB,iBAAiB,CAAC,CAAC;;EAEjC;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIsB,gBAAgB,CAACS,IAAI,IAAIT,gBAAgB,CAACgC,SAAS,EAAE;MACvD1B,kBAAkB,CAACM,OAAO,GAAGqB,WAAW,CAAC,MAAM;QAC7ClC,QAAQ,CAACb,eAAe,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAEK,iBAAiB,CAACK,yBAAyB,CAAC;MAE/C,OAAO,MAAM;QACX,IAAIU,kBAAkB,CAACM,OAAO,EAAE;UAC9BsB,aAAa,CAAC5B,kBAAkB,CAACM,OAAO,CAAC;QAC3C;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACZ,gBAAgB,CAACS,IAAI,EAAET,gBAAgB,CAACgC,SAAS,EAAEjC,QAAQ,CAAC,CAAC;;EAEjE;EACArB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI2B,oBAAoB,CAACO,OAAO,EAAE;QAChCc,YAAY,CAACrB,oBAAoB,CAACO,OAAO,CAAC;MAC5C;MACA,IAAIN,kBAAkB,CAACM,OAAO,EAAE;QAC9BsB,aAAa,CAAC5B,kBAAkB,CAACM,OAAO,CAAC;MAC3C;MACAK,iBAAiB,CAACc,MAAM,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACd,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMkB,sBAAsB,GAAG3D,WAAW,CAAC,MAAM;IAC/C,IAAI,CAACwB,gBAAgB,CAACgC,SAAS,EAAE,OAAO,IAAI;IAE5C,MAAMI,cAAc,GAAG,IAAIzB,IAAI,CAACX,gBAAgB,CAACgC,SAAS,CAAC,CAACK,OAAO,CAAC,CAAC;IACrE,MAAM3B,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAM4B,QAAQ,GAAGF,cAAc,GAAG1B,GAAG;IAErC,IAAI4B,QAAQ,IAAI,CAAC,EAAE,OAAO,CAAC;IAE3B,OAAOxB,IAAI,CAACyB,KAAK,CAACD,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;EACtC,CAAC,EAAE,CAACtC,gBAAgB,CAACgC,SAAS,CAAC,CAAC;;EAEhC;EACA,MAAMQ,mBAAmB,GAAGhE,WAAW,CAAC,MAAM;IAC5C,MAAMiE,OAAO,GAAGN,sBAAsB,CAAC,CAAC;IACxC,IAAIM,OAAO,KAAK,IAAI,EAAE,OAAO,IAAI;IACjC,IAAIA,OAAO,IAAI,CAAC,EAAE,OAAO,SAAS;IAElC,MAAMC,OAAO,GAAG5B,IAAI,CAACyB,KAAK,CAACE,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IAErC,IAAIC,OAAO,GAAG,CAAC,EAAE;MACf,OAAO,GAAGA,OAAO,KAAKC,gBAAgB,GAAG;IAC3C;IACA,OAAO,GAAGA,gBAAgB,GAAG;EAC/B,CAAC,EAAE,CAACR,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMS,eAAe,GAAGpE,WAAW,CAAC,MAAM;IACxC,MAAMiE,OAAO,GAAGN,sBAAsB,CAAC,CAAC;IACxC,OAAOM,OAAO,KAAK,IAAI,IAAIA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,GAAG;EACzD,CAAC,EAAE,CAACN,sBAAsB,CAAC,CAAC;EAE5B,OAAO;IACL;IACAV,yBAAyB;IACzBG,eAAe;IACfE,yBAAyB;IAEzB;IACA5B,YAAY;IACZC,SAAS;IACTyC,eAAe,EAAEA,eAAe,CAAC,CAAC;IAElC;IACAT,sBAAsB;IACtBK,mBAAmB;IAEnB;IACAK,MAAM,EAAEtD;EACV,CAAC;AACH,CAAC;AAACO,EAAA,CA9LWD,sBAAsB;EAAA,QAChBlB,WAAW,EACHC,WAAW,EACRA,WAAW,EAClBA,WAAW,EACdA,WAAW;AAAA;AA2L/B,eAAeiB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}