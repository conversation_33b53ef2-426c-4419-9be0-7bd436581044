{"name": "uroom_project", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@google-cloud/translate": "^9.0.1", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.6.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/node": "^24.0.0", "@types/pdfjs-dist": "^2.10.377", "axios": "^1.8.4", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.8", "craco": "^0.0.3", "date-fns": "^4.1.0", "firebase": "^11.7.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.479.0", "moment": "^2.30.1", "next": "^15.2.2", "pdfmake": "^0.2.20", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-bootstrap": "^2.10.9", "react-bootstrap-icons": "^1.11.5", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "reactstrap": "^8.10.1", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-persist-transform-expire": "^0.0.2", "redux-saga": "^1.3.0", "socket.io-client": "^4.8.1", "uroom_project": "file:", "vietnam-provinces": "^0.0.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "exports": {".": {"require": "./dist/redux.cjs.js", "import": "./dist/redux.esm.js"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}