const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

async function testPrivatePromotions() {
  console.log('🧪 Testing Private Promotion System');
  console.log('=' .repeat(50));

  // Test 1: Guest user - should only see public promotions
  console.log('\n👤 Test 1: Guest User (No Auth)');
  try {
    const response = await axios.get(`${API_BASE_URL}/api/promotions/user`);
    console.log('✅ Success! Total promotions:', response.data.promotions?.length || 0);
    
    const types = response.data.promotions?.reduce((acc, promo) => {
      acc[promo.assignmentType] = (acc[promo.assignmentType] || 0) + 1;
      return acc;
    }, {});
    
    console.log('   Types:', Object.entries(types || {}).map(([type, count]) => `${type}: ${count}`).join(', '));
    
    if (response.data.promotions?.some(p => p.assignmentType !== 'PUBLIC')) {
      console.log('❌ ERROR: Guest user should only see PUBLIC promotions!');
    } else {
      console.log('✅ CORRECT: Guest user only sees PUBLIC promotions');
    }
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 2: Try to claim unassigned private promotion
  console.log('\n🎁 Test 2: Claim Unassigned Private Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'NEWUSER50',
      orderAmount: 300,
      userId: 1
    });
    console.log('✅ Claim Success:', response.data.message);
    console.log('   Claimed:', response.data.claimed ? 'YES' : 'NO');
    console.log('   Discount:', `$${response.data.discount}`);
  } catch (error) {
    console.log('❌ Claim Error:', error.response?.data?.message || error.message);
  }

  // Test 3: Try to claim already claimed promotion
  console.log('\n🔒 Test 3: Try to Claim Already Claimed Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'NEWUSER50',
      orderAmount: 300,
      userId: 2 // Different user
    });
    console.log('✅ Success:', response.data.message);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  // Test 4: Try to use private promotion assigned to different user
  console.log('\n🚫 Test 4: Try to Use Other User\'s Private Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'SPECIAL_USER1', // Assigned to User 1
      orderAmount: 300,
      userId: 2 // Different user trying to use it
    });
    console.log('✅ Success:', response.data.message);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  // Test 5: Correct user using their private promotion
  console.log('\n✅ Test 5: Correct User Using Their Private Promotion');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'SPECIAL_USER1', // Assigned to User 1
      orderAmount: 300,
      userId: 1 // Correct user
    });
    console.log('✅ Success:', response.data.message);
    console.log('   Discount:', `$${response.data.discount}`);
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
  }

  // Test 6: Try invalid private promotion code
  console.log('\n❌ Test 6: Invalid Private Promotion Code');
  try {
    const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {
      code: 'INVALID_PRIVATE123',
      orderAmount: 300,
      userId: 1
    });
    console.log('✅ Success:', response.data.message);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.data?.message || error.message);
  }

  console.log('\n🎉 Private Promotion Testing Complete!');
  console.log('\n📋 Expected Behavior:');
  console.log('✅ Guest users: Only see PUBLIC promotions in modal');
  console.log('✅ Private promotions: Only accessible via manual code entry');
  console.log('✅ Auto-claiming: Unassigned private promotions get claimed when used');
  console.log('✅ Access control: Users can only use their own private promotions');
  console.log('✅ Security: Private promotions are hidden from promotion list API');
}

testPrivatePromotions();
