const mongoose = require("mongoose");
const Promotion = require("./src/models/Promotion");
const UserPromotion = require("./src/models/UserPromotion");
const User = require("./src/models/user");
require("dotenv").config();

const uri = process.env.MONGODB_URI_DEVELOPMENT;

async function seedPromotions() {
  try {
    await mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log("Connected to MongoDB");

    // Clear existing promotions and user promotions
    await Promotion.deleteMany({});
    await UserPromotion.deleteMany({});
    console.log("Cleared existing promotion data");

    // Get first admin user (assuming user with _id: 1 is admin)
    const adminUser = await User.findOne({ _id: 1 });
    if (!adminUser) {
      console.error("Admin user not found");
      process.exit(1);
    }

    // Create sample promotions
    const promotions = [
      {
        code: "WELCOME15",
        name: "Welcome Bonus",
        description: "15% off for new customers on their first booking",
        discountType: "PERCENTAGE",
        discountValue: 15,
        maxDiscountAmount: 200000, // 200k VND max
        minOrderAmount: 500000, // 500k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 1000,
        usedCount: 45,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["NEW_USER", "ALL"],
        createdBy: adminUser._id,
        metadata: {
          autoTrigger: "welcome",
          campaignId: "WELCOME_2025"
        }
      },
      {
        code: "SUMMER25",
        name: "Summer Special",
        description: "25% off summer vacation bookings",
        discountType: "PERCENTAGE",
        discountValue: 25,
        maxDiscountAmount: 500000, // 500k VND max
        minOrderAmount: 1000000, // 1M VND min
        startDate: new Date("2025-06-01"),
        endDate: new Date("2025-08-31"),
        usageLimit: 500,
        usedCount: 123,
        userUsageLimit: 2,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "SUMMER_2025",
          season: "summer"
        }
      },
      {
        code: "SAVE100K",
        name: "Fixed Discount",
        description: "Save 100,000 VND on any booking",
        discountType: "FIXED_AMOUNT",
        discountValue: 100000,
        minOrderAmount: 800000, // 800k VND min
        startDate: new Date("2025-01-15"),
        endDate: new Date("2025-06-30"),
        usageLimit: 200,
        usedCount: 67,
        userUsageLimit: 3,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "FIXED_DISCOUNT_2025"
        }
      },
      {
        code: "LOYALTY30",
        name: "Loyalty Reward",
        description: "30% off for loyal customers",
        discountType: "PERCENTAGE",
        discountValue: 30,
        maxDiscountAmount: 1000000, // 1M VND max
        minOrderAmount: 1500000, // 1.5M VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 100,
        usedCount: 23,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // Personalized promotion
        targetUserTypes: ["VIP_USER"],
        createdBy: adminUser._id,
        metadata: {
          autoTrigger: "loyalty",
          tier: "GOLD"
        }
      },
      {
        code: "WEEKEND20",
        name: "Weekend Getaway",
        description: "20% off weekend bookings",
        discountType: "PERCENTAGE",
        discountValue: 20,
        maxDiscountAmount: 300000, // 300k VND max
        minOrderAmount: 600000, // 600k VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 300,
        usedCount: 89,
        userUsageLimit: 4,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "WEEKEND_2025",
          applicableDays: ["FRIDAY", "SATURDAY", "SUNDAY"]
        }
      },
      {
        code: "BIRTHDAY50",
        name: "Birthday Special",
        description: "50% off on your birthday month",
        discountType: "PERCENTAGE",
        discountValue: 50,
        maxDiscountAmount: 800000, // 800k VND max
        minOrderAmount: 1000000, // 1M VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 365,
        usedCount: 12,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: true, // Personalized promotion
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          autoTrigger: "birthday",
          specialOccasion: true
        }
      },
      {
        code: "EXPIRED10",
        name: "Expired Promotion",
        description: "This promotion has expired",
        discountType: "PERCENTAGE",
        discountValue: 10,
        minOrderAmount: 300000,
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-12-31"), // Expired
        usageLimit: 100,
        usedCount: 95,
        userUsageLimit: 2,
        isActive: false,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "EXPIRED_TEST"
        }
      },
      {
        code: "UPCOMING40",
        name: "Future Promotion",
        description: "40% off coming soon",
        discountType: "PERCENTAGE",
        discountValue: 40,
        maxDiscountAmount: 600000,
        minOrderAmount: 800000,
        startDate: new Date("2025-12-01"), // Future date
        endDate: new Date("2025-12-31"),
        usageLimit: 50,
        usedCount: 0,
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "FUTURE_2025"
        }
      },
      {
        code: "MAXEDOUT",
        name: "Used Up Promotion",
        description: "This promotion has reached its usage limit",
        discountType: "FIXED_AMOUNT",
        discountValue: 50000,
        minOrderAmount: 400000,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        usedCount: 10, // Maxed out
        userUsageLimit: 1,
        isActive: true,
        isPersonalized: false,
        targetUserTypes: ["ALL"],
        createdBy: adminUser._id,
        metadata: {
          campaignId: "MAXED_TEST"
        }
      },
      {
        code: "VIP100",
        name: "VIP Exclusive",
        description: "Exclusive 100,000 VND off for VIP members",
        discountType: "FIXED_AMOUNT",
        discountValue: 100000,
        minOrderAmount: 2000000, // 2M VND min
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 50,
        usedCount: 8,
        userUsageLimit: 2,
        isActive: true,
        isPersonalized: true, // VIP only
        targetUserTypes: ["VIP_USER"],
        createdBy: adminUser._id,
        metadata: {
          tier: "VIP",
          exclusive: true
        }
      }
    ];

    // Insert promotions
    const createdPromotions = await Promotion.insertMany(promotions);
    console.log(`Created ${createdPromotions.length} promotions`);

    // Create user promotion assignments for testing
    const users = await User.find({ _id: { $in: [1, 2, 3, 4, 5] } }); // Get first 5 users
    
    if (users.length > 0) {
      const userPromotions = [];
      
      // Assign some promotions to specific users
      for (const user of users) {
        // Assign welcome promotion to all users
        const welcomePromo = createdPromotions.find(p => p.code === "WELCOME15");
        if (welcomePromo) {
          userPromotions.push({
            userId: user._id,
            promotionId: welcomePromo._id,
            assignmentType: 'EARNED',
            maxUsage: 1,
            usageCount: user._id === 1 ? 1 : 0, // User 1 has used it
            status: user._id === 1 ? 'USED' : 'ASSIGNED',
            metadata: {
              assignmentReason: 'WELCOME_BONUS',
              triggerEvent: 'USER_REGISTRATION'
            }
          });
        }

        // Assign loyalty promotion to users 2 and 3
        if (user._id === 2 || user._id === 3) {
          const loyaltyPromo = createdPromotions.find(p => p.code === "LOYALTY30");
          if (loyaltyPromo) {
            userPromotions.push({
              userId: user._id,
              promotionId: loyaltyPromo._id,
              assignmentType: 'LOYALTY_REWARD',
              maxUsage: 1,
              usageCount: 0,
              status: 'ASSIGNED',
              metadata: {
                assignmentReason: 'LOYALTY_REWARD',
                tier: 'GOLD'
              }
            });
          }
        }

        // Assign birthday promotion to user 4
        if (user._id === 4) {
          const birthdayPromo = createdPromotions.find(p => p.code === "BIRTHDAY50");
          if (birthdayPromo) {
            userPromotions.push({
              userId: user._id,
              promotionId: birthdayPromo._id,
              assignmentType: 'EARNED',
              maxUsage: 1,
              usageCount: 0,
              status: 'ASSIGNED',
              metadata: {
                assignmentReason: 'BIRTHDAY_GIFT',
                triggerEvent: 'BIRTHDAY'
              }
            });
          }
        }

        // Assign VIP promotion to user 5
        if (user._id === 5) {
          const vipPromo = createdPromotions.find(p => p.code === "VIP100");
          if (vipPromo) {
            userPromotions.push({
              userId: user._id,
              promotionId: vipPromo._id,
              assignmentType: 'ASSIGNED',
              maxUsage: 2,
              usageCount: 1, // Used once
              status: 'ASSIGNED',
              metadata: {
                assignmentReason: 'MANUAL_ASSIGNMENT',
                tier: 'VIP'
              }
            });
          }
        }
      }

      if (userPromotions.length > 0) {
        await UserPromotion.insertMany(userPromotions);
        console.log(`Created ${userPromotions.length} user promotion assignments`);
      }
    }

    console.log("\n🎉 Promotion seeding completed successfully!");
    console.log("\n📋 Created Promotions:");
    createdPromotions.forEach(promo => {
      console.log(`- ${promo.code}: ${promo.name} (${promo.discountType} ${promo.discountValue}${promo.discountType === 'PERCENTAGE' ? '%' : ' VND'})`);
    });

    console.log("\n🧪 Test Scenarios:");
    console.log("- WELCOME15: Available for new users");
    console.log("- SUMMER25: Active promotion with good usage");
    console.log("- SAVE100K: Fixed amount discount");
    console.log("- LOYALTY30: Personalized for loyal customers");
    console.log("- WEEKEND20: Regular weekend promotion");
    console.log("- BIRTHDAY50: Special birthday promotion");
    console.log("- EXPIRED10: Expired promotion (should not show in booking)");
    console.log("- UPCOMING40: Future promotion (should show as 'Starting Soon')");
    console.log("- MAXEDOUT: Used up promotion (should not show in booking)");
    console.log("- VIP100: VIP exclusive promotion");

    process.exit(0);
  } catch (error) {
    console.error("Error seeding promotions:", error);
    process.exit(1);
  }
}

seedPromotions();
