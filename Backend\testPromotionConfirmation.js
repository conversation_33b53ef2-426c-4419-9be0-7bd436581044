const mongoose = require('mongoose');
require('dotenv').config();

// Connect to database
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/uroom');

const Promotion = require('./src/models/Promotion');
const PromotionUser = require('./src/models/PromotionUser');

async function testWithPromotion(promotion) {
  console.log(`🎫 ${promotion.code} - Current usedCount: ${promotion.usedCount}`);

  const promotionUsers = await PromotionUser.find({
    promotionId: promotion._id
  }).populate('userId', 'name email');

  console.log(`📋 PromotionUser records: ${promotionUsers.length}`);
  promotionUsers.forEach((pu, index) => {
    console.log(`   ${index + 1}. User: ${pu.userId?.name || 'Unknown'} - Status: ${pu.status} - Amount: $${pu.orderAmount} - Discount: $${pu.discountAmount}`);
  });

  // 2. Test confirmPromotionUsage method
  console.log('\n🧪 Testing confirmPromotionUsage method:');

  const testUserId = 11; // User 11
  const testOrderAmount = 500;
  const testDiscountAmount = 250; // 50% of $500 for VIP_USER2

  console.log(`   User ID: ${testUserId}`);
  console.log(`   Order Amount: $${testOrderAmount}`);
  console.log(`   Discount Amount: $${testDiscountAmount}`);

  try {
    const result = await Promotion.confirmPromotionUsage(
      promotion._id,
      testUserId,
      testOrderAmount,
      testDiscountAmount
    );

    console.log('✅ confirmPromotionUsage succeeded');
    console.log(`   New usedCount: ${result.usedCount}`);

    // Check PromotionUser record was created
    const newPromotionUser = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId: testUserId,
      status: 'USED'
    }).sort({ createdAt: -1 });

    if (newPromotionUser) {
      console.log('✅ PromotionUser record created successfully');
      console.log(`   Order Amount: $${newPromotionUser.orderAmount}`);
      console.log(`   Discount Amount: $${newPromotionUser.discountAmount}`);
      console.log(`   Status: ${newPromotionUser.status}`);
    } else {
      console.log('❌ PromotionUser record not found');
    }

  } catch (error) {
    console.log('❌ confirmPromotionUsage failed:', error.message);
  }

  // 3. Check final state
  console.log('\n📊 Final State:');
  const updatedPromo = await Promotion.findById(promotion._id);
  console.log(`🎫 ${promotion.code} - Final usedCount: ${updatedPromo.usedCount}`);

  const finalPromotionUsers = await PromotionUser.find({
    promotionId: promotion._id
  }).populate('userId', 'name email');

  console.log(`📋 Final PromotionUser records: ${finalPromotionUsers.length}`);
  finalPromotionUsers.forEach((pu, index) => {
    console.log(`   ${index + 1}. User: ${pu.userId?.name || 'Unknown'} - Status: ${pu.status} - Amount: $${pu.orderAmount} - Discount: $${pu.discountAmount}`);
  });
}

async function testPromotionConfirmation() {
  console.log('🧪 Testing Promotion Confirmation System');
  console.log('=' .repeat(50));

  try {
    // Wait for connection
    await mongoose.connection.asPromise();
    console.log('✅ Database connected successfully');

    // 1. Check current state
    console.log('\n📊 Current State:');

    // First, list all promotions to see what's available
    const allPromotions = await Promotion.find({}).select('code name isPublic userId');
    console.log(`📋 Available promotions: ${allPromotions.length}`);

    if (allPromotions.length === 0) {
      console.log('📝 No promotions found, creating test promotion...');

      const testPromotion = await Promotion.create({
        code: 'TEST_PROMO',
        name: 'Test Promotion for Confirmation',
        description: 'Test promotion to verify confirmation system',
        discountType: 'PERCENTAGE',
        discountValue: 50,
        maxDiscountAmount: 500,
        minOrderAmount: 100,
        maxUsageCount: 10,
        usedCount: 0,
        isPublic: false,
        userId: 11, // User 11 as number
        isActive: true,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        createdBy: 11, // Required field
      });

      console.log('✅ Test promotion created:', testPromotion.code);
      return testWithPromotion(testPromotion);
    }

    allPromotions.forEach((promo, index) => {
      console.log(`   ${index + 1}. ${promo.code} - ${promo.name} (${promo.isPublic ? 'PUBLIC' : 'PRIVATE'}) - User: ${promo.userId || 'N/A'}`);
    });

    const mysteryPromo = await Promotion.findOne({ code: 'MYSTERY80' });
    if (!mysteryPromo) {
      console.log('❌ MYSTERY80 promotion not found, using first available promotion...');
      const firstPromo = allPromotions[0];
      if (!firstPromo) {
        console.log('❌ No promotions available');
        return;
      }
      const fullPromo = await Promotion.findById(firstPromo._id);
      console.log(`✅ Using ${fullPromo.code} for testing`);
      return testWithPromotion(fullPromo);
    }

    await testWithPromotion(mysteryPromo);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

testPromotionConfirmation();
